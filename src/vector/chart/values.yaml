# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

additionalNetworkAllow: []
# ref: https://uds.defenseunicorns.com/reference/configuration/custom-resources/packages-v1alpha1-cr/#allow
# Examples:
# - direction: Egress
#   selector:
#     app.kubernetes.io/name: vector
#   remoteNamespace: elastic
#   remoteSelector:
#     app.kubernetes.io/name: elastic
#   port: 9090
#   description: "Elastic Storage"
# - direction: Egress
#   selector:
#     app.kubernetes.io/name: vector
#   remoteGenerated: Anywhere
#   port: 80
#   description: "S3 Storage"
