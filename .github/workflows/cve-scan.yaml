# Copyright 2025 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

# This workflow runs a nightly CVE scan against the unicorn flavor of UDS Core and aggregates results in a GitHub issue
name: <PERSON><PERSON> Scan

on:
  schedule:
    # Nightly at 12am MT / 7am UTC
    - cron: "0 7 * * *"
  pull_request:
    paths:
      - .github/workflows/cve-scan.yaml
      - tasks/scan.yaml
      - tasks/grype-markdown.tmpl
  workflow_call:
    inputs:
      release:
        type: boolean
        description: "Whether this is a release or not (will update issue)"
        default: false

permissions:
  id-token: write
  contents: read
  issues: write # Needed to create/update issues
  packages: read # Allows reading the unicorn GHCR packages

jobs:
  scan-unicorn-package:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Environment setup
        uses: ./.github/actions/setup
        with:
          registry1Username: ${{ secrets.IRON_BANK_ROBOT_USERNAME }}
          registry1Password: ${{ secrets.IRON_BANK_ROBOT_PASSWORD }}
          ghToken: ${{ secrets.GITHUB_TOKEN }}
          rapidfortUsername: ${{ secrets.RAPIDFORT_USERNAME }}
          rapidfortPassword: ${{ secrets.RAPIDFORT_PASSWORD }}

      - name: Install Grype
        run: curl -sSfL https://raw.githubusercontent.com/anchore/grype/main/install.sh | sh -s -- -b /usr/local/bin

      - name: Scan latest unicorn package
        # This task uses the defaults of latest version, unicorn flavor, core package, and negligible severity
        run: uds run -f tasks/scan.yaml

      # Only upload artifacts for PR runs
      - name: Upload CVE report to GitHub artifacts
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: cve-scan-report
          path: cve/scans/core-vulnerability-report.md

      # Create or update GitHub issue for scheduled runs
      - name: Create/Update CVE Scan Issue
        if: ${{ github.event_name == 'schedule' || inputs.release }}
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPO: ${{ github.repository }}
          REPORT_FILE: "cve/scans/core-vulnerability-report.md"
        run: |
          ISSUE_TITLE="CVE Dashboard"
          ISSUE_AUTHOR="github-actions[bot]"

          # Read the CVE report content *without* JSON escaping issues
          CVE_REPORT=$(cat "$REPORT_FILE")

          # Search for an existing issue by title & author
          ISSUE_NUMBER=$(gh issue list --repo "$REPO" --search "$ISSUE_TITLE in:title author:$ISSUE_AUTHOR" --state open --json number --jq '.[0].number')

          if [[ -n "$ISSUE_NUMBER" ]]; then
            echo "Updating existing issue #$ISSUE_NUMBER..."
            gh issue edit "$ISSUE_NUMBER" --repo "$REPO" --title "$ISSUE_TITLE" --body "$CVE_REPORT"
          else
            echo "Creating a new issue..."
            gh issue create --repo "$REPO" --title "$ISSUE_TITLE" --body "$CVE_REPORT" --label "security"
          fi
