# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

{{- if eq (include "keycloak.postgresql.config" .) "true" }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "keycloak.fullname" . }}-postgresql
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
type: Opaque
data:
  database: {{ .Values.postgresql.database | b64enc }}
  username: {{ .Values.postgresql.username | b64enc }}
  password: {{ .Values.postgresql.password | b64enc }}
  host: {{ .Values.postgresql.host | b64enc }}
  port: {{ .Values.postgresql.port | toString | b64enc }}
{{- end }}