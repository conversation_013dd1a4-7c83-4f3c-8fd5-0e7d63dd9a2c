{
  "compilerOptions": {
    "allowSyntheticDefaultImports": true,
    "declaration": true,
    "declarationMap": true,
    "emitDeclarationOnly": true,
    "esModuleInterop": true,
    "lib": ["ES2022", "WebWorker"],
    "module": "nodenext",
    "moduleResolution": "nodenext",
    "outDir": "dist",
    "resolveJsonModule": true,
    "rootDir": ".",
    "strict": true,
    "target": "ES2022",
    "useUnknownInCatchVariables": false,
  },
  "include": [
    "**/*.ts"
  ],
  "exclude": [
    "dist",
    "test/playwright",
    "test/vitest",
    "scripts/",
    "uds-docs"
  ]
}
