/**
 * Copyright 2024 Defense Unicorns
 * SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial
 */

// This file is auto-generated by kubernetes-fluent-client, do not edit manually

import { GenericKind, RegisterKind } from "kubernetes-fluent-client";

export class RequestAuthentication extends Generic<PERSON>ind {
  /**
   * Request authentication configuration for workloads. See more details at:
   * https://istio.io/docs/reference/config/security/request_authentication.html
   */
  spec?: Spec;
  status?: { [key: string]: unknown };
}

/**
 * Request authentication configuration for workloads. See more details at:
 * https://istio.io/docs/reference/config/security/request_authentication.html
 */
export interface Spec {
  /**
   * Define the list of JWTs that can be validated at the selected workloads' proxy.
   */
  jwtRules?: JwtRule[];
  /**
   * Optional.
   */
  selector?: Selector;
  /**
   * Optional.
   */
  targetRef?: TargetRef;
}

export interface JwtRule {
  /**
   * The list of JWT [audiences](https://tools.ietf.org/html/rfc7519#section-4.1.3) that are
   * allowed to access.
   */
  audiences?: string[];
  /**
   * If set to true, the original token will be kept for the upstream request.
   */
  forwardOriginalToken?: boolean;
  /**
   * List of header locations from which JWT is expected.
   */
  fromHeaders?: FromHeader[];
  /**
   * List of query parameters from which JWT is expected.
   */
  fromParams?: string[];
  /**
   * Identifies the issuer that issued the JWT.
   */
  issuer: string;
  /**
   * JSON Web Key Set of public keys to validate signature of the JWT.
   */
  jwks?: string;
  /**
   * URL of the provider's public key set to validate signature of the JWT.
   */
  jwks_uri?: string;
  /**
   * URL of the provider's public key set to validate signature of the JWT.
   */
  jwksUri?: string;
  /**
   * This field specifies a list of operations to copy the claim to HTTP headers on a
   * successfully verified token.
   */
  outputClaimToHeaders?: OutputClaimToHeader[];
  /**
   * This field specifies the header name to output a successfully verified JWT payload to the
   * backend.
   */
  outputPayloadToHeader?: string;
}

export interface FromHeader {
  /**
   * The HTTP header name.
   */
  name: string;
  /**
   * The prefix that should be stripped before decoding the token.
   */
  prefix?: string;
}

export interface OutputClaimToHeader {
  /**
   * The name of the claim to be copied from.
   */
  claim?: string;
  /**
   * The name of the header to be created.
   */
  header?: string;
}

/**
 * Optional.
 */
export interface Selector {
  /**
   * One or more labels that indicate a specific set of pods/VMs on which a policy should be
   * applied.
   */
  matchLabels?: { [key: string]: string };
}

/**
 * Optional.
 */
export interface TargetRef {
  /**
   * group is the group of the target resource.
   */
  group?: string;
  /**
   * kind is kind of the target resource.
   */
  kind?: string;
  /**
   * name is the name of the target resource.
   */
  name?: string;
  /**
   * namespace is the namespace of the referent.
   */
  namespace?: string;
}

RegisterKind(RequestAuthentication, {
  group: "security.istio.io",
  version: "v1",
  kind: "RequestAuthentication",
});
