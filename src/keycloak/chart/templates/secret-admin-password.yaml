# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

{{- if .Values.insecureAdminPasswordGeneration.enabled }}
{{- $kcPass := (randAlphaNum 32) | b64enc | quote }}
{{- $kcUser := .Values.insecureAdminPasswordGeneration.username | b64enc | quote }}
{{- $secretName := (print (include "keycloak.fullname" .) "-admin-password") }}
{{- if .Release.IsUpgrade }}
  {{- $existingSecret := (lookup "v1" "Secret" .Release.Namespace $secretName) }}
  {{- if $existingSecret }}
    {{- $kcUser = (index $existingSecret.data "username") | quote }}
    {{- $kcPass = (index $existingSecret.data "password") | quote }}
  {{- end }}
{{- end }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $secretName }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
type: Opaque
data:
  username: {{ $kcUser }}
  password: {{ $kcPass }}
{{- end }}
