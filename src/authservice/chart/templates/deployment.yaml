# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "authservice.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "authservice.labels" . | nindent 4 }}
spec:
{{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
{{- end }}
  selector:
    matchLabels:
      {{- include "authservice.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        # Pre-create an empty checksum to ensure pod cycles when first update occurs
        pepr.dev/checksum: "initialized"
      {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "authservice.selectorLabels" . | nindent 8 }}
    spec:
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 10003
              protocol: TCP
          livenessProbe:
            tcpSocket:
              port: 10003
          readinessProbe:
            tcpSocket:
              port: 10003
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: {{ include "authservice.name" . }}
              mountPath: /etc/authservice
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
        - name: {{ include "authservice.name" . }}
          secret:
            secretName: {{ include "authservice.fullname" . }}-uds
