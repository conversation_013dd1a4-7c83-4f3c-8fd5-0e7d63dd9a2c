# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

includes:
  - common-setup: https://raw.githubusercontent.com/defenseunicorns/uds-common/v1.16.3/tasks/setup.yaml

tasks:
  - name: validate
    actions:
      - description: Validate grafana is up
        wait:
          cluster:
            kind: Pod
            name: "app.kubernetes.io/instance=grafana"
            namespace: grafana
            condition: Ready
      - description: Validate grafana virtual service
        cmd: |
          if [ "$(curl -isS https://grafana.admin.uds.dev --output /dev/null -w '%{http_code}')" = "302" ]; then
            echo "<PERSON><PERSON> is up and running."
          else
            echo "ERROR: <PERSON><PERSON> returned a $(curl -isS https://grafana.admin.uds.dev --output /dev/null -w '%{http_code}') code."
            exit 1
          fi

          if curl -L -isS https://grafana.admin.uds.dev --output /dev/null -w '%{url_effective}' | grep "sso.uds.dev" 2>&1 1>/dev/null; then
            echo "<PERSON><PERSON> is redirecting to SSO as expected."
          else
            echo "ERROR: <PERSON><PERSON> is redirecting to $(curl -L -isS https://grafana.admin.uds.dev --output /dev/null -w '%{url_effective}')."
            exit 1
          fi

  - name: e2e-test
    actions:
      - description: "Setup the Doug User for testing"
        task: common-setup:keycloak-user
        with:
          group: "/UDS Core/Admin"
      - description: E2E Test for Grafana, optionally set FULL_CORE=true to test integrations with Loki
        cmd: |
          # renovate: datasource=docker depName=mcr.microsoft.com/playwright versioning=docker
          docker run --rm --ipc=host -e FULL_CORE="${FULL_CORE}" --net=host --mount type=bind,source="$(pwd)",target=/app mcr.microsoft.com/playwright:v1.53.2-noble sh -c " \
            cd app && \
            npm ci && \
            npx playwright test grafana.test.ts \
            "
        dir: test/playwright
