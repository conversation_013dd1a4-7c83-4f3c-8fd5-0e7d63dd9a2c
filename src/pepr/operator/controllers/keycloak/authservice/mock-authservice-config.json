{"allow_unmatched_requests": false, "listen_address": "0.0.0.0", "listen_port": "10003", "log_level": "trace", "default_oidc_config": {"skip_verify_peer_cert": false, "authorization_uri": "https://sso.uds.dev/realms/uds/protocol/openid-connect/auth", "token_uri": "https://sso.uds.dev/realms/uds/protocol/openid-connect/token", "jwks_fetcher": {"jwks_uri": "https://sso.uds.dev/realms/uds/protocol/openid-connect/certs", "periodic_fetch_interval_sec": 60, "skip_verify_peer_cert": false}, "client_id": "global_id", "client_secret": "global_secret", "id_token": {"preamble": "Bearer", "header": "Authorization"}, "access_token": {"header": "JWT"}, "trusted_certificate_authority": "", "logout": {"path": "/globallogout", "redirect_uri": "https://sso.uds.dev/realms/uds/protocol/openid-connect/token/logout"}, "absolute_session_timeout": "0", "idle_session_timeout": "0", "scopes": []}, "threads": 8, "chains": [{"name": "local", "match": {"header": ":local", "prefix": "localhost"}, "filters": [{"oidc_override": {"authorization_uri": "https://sso.uds.dev/realms/uds/protocol/openid-connect/auth", "token_uri": "https://sso.uds.dev/realms/uds/protocol/openid-connect/token", "callback_uri": "https://localhost/login", "client_id": "local_id", "client_secret": "local_secret", "cookie_name_prefix": "local", "logout": {"path": "/local", "redirect_uri": "https://sso.uds.dev/realms/uds/protocol/openid-connect/token/logout"}, "scopes": []}}]}]}