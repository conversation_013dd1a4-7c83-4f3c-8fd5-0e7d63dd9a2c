# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: v1
kind: Namespace
metadata:
  name: authservice-test-app
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: httpbin-other
  namespace: authservice-test-app
spec:
  sso:
    - name: Demo SSO
      clientId: uds-core-httpbin
      redirectUris:
        - "https://protected.uds.dev/login"
      enableAuthserviceSelector:
        app: httpbin
      groups:
        anyOf:
          - "/UDS Core/Admin"
  network:
    expose:
      - service: httpbin
        selector:
          app: httpbin
        gateway: tenant
        host: protected
        port: 8000
        targetPort: 80
    allow:
      - direction: Ingress
        selector:
          app: httpbin
        ports:
          - 80
