# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: prometheus-stack
  namespace: {{ .Release.Namespace }}
spec:
  network:
    serviceMesh:
      mode: ambient
    allow:
      # Permit intra-namespace communication
      - direction: Ingress
        remoteGenerated: IntraNamespace

      - direction: Egress
        remoteGenerated: IntraNamespace

      - direction: Egress
        remoteGenerated: KubeAPI
        selector:
          app: kube-prometheus-stack-operator

      - direction: Egress
        remoteGenerated: KubeAPI
        selector:
          app: prometheus

      - direction: Egress
        remoteGenerated: KubeAPI
        selector:
          app.kubernetes.io/name: kube-state-metrics

      - direction: Egress
        remoteGenerated: KubeAPI
        selector:
          app: kube-prometheus-stack-admission-create

      - direction: Egress
        remoteGenerated: KubeAPI
        selector:
          app: kube-prometheus-stack-admission-patch

      - direction: Ingress
        # todo: evaluate a "KubeAPI" _ingress_ generated rule for webhook calls
        remoteGenerated: Anywhere
        selector:
          app: kube-prometheus-stack-operator
        port: 10250
        description: "Webhook"

      # Prometheus scrape targets
      - direction: Egress
        remoteNamespace: "" # todo: restrict this overly permissive netpol
        selector:
          app.kubernetes.io/name: prometheus
        description: "Metrics Scraping"

      - direction: Egress
        remoteGenerated: KubeNodes
        selector:
          app.kubernetes.io/name: prometheus
        description: "Metrics Scraping of Kube Nodes"

      - direction: Ingress
        remoteNamespace: grafana
        remoteSelector:
          app.kubernetes.io/name: grafana
        remoteServiceAccount: grafana
        selector:
          app.kubernetes.io/name: prometheus
        port: 9090
        description: "Grafana Metrics Queries"

      - direction: Ingress
        remoteNamespace: grafana
        remoteSelector:
          app.kubernetes.io/name: grafana
        remoteServiceAccount: grafana
        selector:
          app.kubernetes.io/name: alertmanager
        description: Grafana Alerts Queries
        port: 9093

      # Custom rules for additional networking access
      {{- with .Values.additionalNetworkAllow }}
      {{ toYaml . | nindent 6 }}
      {{- end }}
