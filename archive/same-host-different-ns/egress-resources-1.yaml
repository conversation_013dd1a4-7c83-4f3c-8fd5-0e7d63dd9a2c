# Add the gateway, service entry, destination rule, and virtual service
apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: different-subdomain-gateway-1
  namespace: ns-a
spec:
  selector:
    app: egressgateway
  servers:
  - port:
      number: 443
      name: tls
      protocol: TLS
    hosts:
      - example.com
    tls:
      mode: PASSTHROUGH
---
apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: different-subdomain-service-entry-1
  namespace: ns-a
spec:
  location: MESH_EXTERNAL
  hosts:
  - example.com
  ports: 
  - number: 443
    name: tls
    protocol: TLS
  resolution: DNS
---
apiVersion: networking.istio.io/v1
kind: DestinationRule
metadata:
  name: egressgateway-for-different-subdomain-1
  namespace: ns-a
spec:
  host: egressgateway.istio-egress-gateway.svc.cluster.local
  subsets:
    - name: different-subdomain-a
---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: different-subdomain-through-egress-gateway-1
  namespace: ns-a
spec:
  hosts:
  - example.com
  gateways:
  - different-subdomain-gateway-1
  - mesh
  tls:
  - match:
    - gateways:
      - mesh
      port: 443
      sniHosts:
      - example.com
    route:
    - destination:
        host: egressgateway.istio-egress-gateway.svc.cluster.local
        subset: different-subdomain-a
        port:
          number: 443
  - match:
    - gateways:
      - different-subdomain-gateway-1
      port: 443
      sniHosts:
      - example.com
    route:
    - destination:
        host: example.com
        port:
          number: 443
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-egress-gateway-access-1
  namespace: ns-a
spec:
  podSelector: 
    matchLabels:
      app: curl1
  policyTypes:
  - Egress
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: istio-egress-gateway
      podSelector:
        matchLabels:
          app: egressgateway
    ports:
    - protocol: TCP
      port: 443