# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: v1
kind: ServiceAccount
metadata:
  name: httpbin
  namespace: test-admin-app
---
apiVersion: v1
kind: Service
metadata:
  name: httpbin
  namespace: test-admin-app
spec:
  ports:
    - name: http
      port: 8000
      targetPort: 80
    - name: port8080
      port: 8080
      targetPort: 8080
  selector:
    app: httpbin
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: httpbin
  namespace: test-admin-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: httpbin
      version: v1
  template:
    metadata:
      labels:
        app: httpbin
        version: v1
    spec:
      serviceAccountName: httpbin
      containers:
        - image: docker.io/kong/httpbin:0.2.3
          imagePullPolicy: IfNotPresent
          name: httpbin
          resources:
            limits:
              cpu: 50m
              memory: 64Mi
            requests:
              cpu: 50m
              memory: 64Mi
          ports:
            - containerPort: 80
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            runAsGroup: 10001
            runAsNonRoot: true
            runAsUser: 10001
            capabilities:
              drop:
                - ALL
              add:
                - NET_BIND_SERVICE
        - name: curl
          image: curlimages/curl
          imagePullPolicy: IfNotPresent
          command: ["sleep", "3600"]
          resources:
            limits:
              cpu: 50m
              memory: 64Mi
            requests:
              cpu: 10m
              memory: 16Mi
          ports:
            - containerPort: 8080
