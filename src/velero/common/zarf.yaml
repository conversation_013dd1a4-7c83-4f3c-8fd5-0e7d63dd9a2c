# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: uds-core-velero-common
  description: "UDS Core Velero Common"
  url: https://velero.io/

variables:
  - name: VELERO_BUCKET_PROVIDER_URL
    description: "S3 compatible object storage service for use with Velero"
    default: "http://minio.uds-dev-stack.svc.cluster.local:9000"
  - name: VELERO_BUCKET
    description: "S3 compatible object storage bucket for use with Velero"
    default: "uds"
  - name: VELERO_BUCKET_REGION
    description: "Region of the bucket for use with Velero"
    default: "uds-dev-stack"
  - name: VELERO_BUCKET_KEY
    description: "Key to use when connecting to the Velero bucket"
    default: "uds"
  - name: VELERO_BUCKET_KEY_SECRET
    sensitive: true
    description: "Key secret to use when connecting to the Velero bucket"
    default: "uds-secret"
  - name: VELERO_BUCKET_CREDENTIAL_NAME
    default: "velero-bucket-credentials"
  - name: VELERO_BUCKET_CREDENTIAL_KEY
    default: "cloud"

components:
  - name: velero
    required: true
    charts:
      - name: uds-velero-config
        namespace: velero
        version: 0.1.0
        localPath: ../chart
      - name: velero
        namespace: velero
        url: https://vmware-tanzu.github.io/helm-charts
        version: 9.1.3
        repoName: velero
        releaseName: velero
        valuesFiles:
          - ../values/values.yaml
