# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: UDSBundle
metadata:
  name: uds-core-eks-nightly
  description: A UDS bundle for deploying EKS and UDS Core
  # x-release-please-start-version
  version: "0.45.1"
  # x-release-please-end

packages:
  - name: init
    repository: ghcr.io/zarf-dev/packages/init
    ref: v0.57.0

  - name: core
    path: ../../../build
    # x-release-please-start-version
    ref: 0.45.1
    # x-release-please-end
    optionalComponents:
      - metrics-server # note: metrics-server is not available as an EKS addon in govcloud
      - istio-egress-gateway
    overrides:
      velero:
        velero:
          variables:
            - name: VELERO_USE_SECRET
              description: "Toggle use secret off to use IRSA."
              path: credentials.useSecret
            - name: VELERO_IRSA_ROLE_ARN
              description: "IRSA ARN annotation to use for Velero"
              path: serviceAccount.server.annotations.eks\.amazonaws\.com/role-arn
          values:
            - path: snapshotsEnabled
              value: true
            - path: schedules.udsbackup.template.snapshotVolumes
              value: true
            - path: configuration.volumeSnapshotLocation
              value:
                - name: default
                  provider: aws
                  config:
                    region: "###ZARF_VAR_VELERO_BUCKET_REGION###"
      loki:
        loki:
          values:
            - path: loki.storage.s3.endpoint
              value: ""
            - path: loki.storage.s3.secretAccessKey
              value: ""
            - path: loki.storage.s3.accessKeyId
              value: ""
          variables:
            - name: LOKI_CHUNKS_BUCKET
              description: "The object storage bucket for Loki chunks"
              path: loki.storage.bucketNames.chunks
            - name: LOKI_RULER_BUCKET
              description: "The object storage bucket for Loki ruler"
              path: loki.storage.bucketNames.ruler
            - name: LOKI_ADMIN_BUCKET
              description: "The object storage bucket for Loki admin"
              path: loki.storage.bucketNames.admin
            - name: LOKI_S3_REGION
              description: "The S3 region"
              path: loki.storage.s3.region
            - name: LOKI_IRSA_ROLE_ARN
              description: "The irsa role annotation"
              path: serviceAccount.annotations.eks\.amazonaws\.com/role-arn
      grafana:
        grafana:
          variables:
            - name: GRAFANA_HA
              description: Enable HA Grafana
              path: autoscaling.enabled
        uds-grafana-config:
          variables:
            - name: GRAFANA_PG_HOST
              description: Grafana postgresql host
              path: postgresql.host
            - name: GRAFANA_PG_PORT
              description: Grafana postgresql port
              path: postgresql.port
            - name: GRAFANA_PG_DATABASE
              description: Grafana postgresql database
              path: postgresql.database
            - name: GRAFANA_PG_PASSWORD
              description: Grafana postgresql password
              path: postgresql.password
              sensitive: true
            - name: GRAFANA_PG_USER
              description: Grafana postgresql username
              path: postgresql.user
      keycloak:
        keycloak:
          values:
            - path: devMode
              value: false
            - path: autoscaling.enabled
              value: true
          variables:
            - name: KEYCLOAK_DB_HOST
              path: postgresql.host
            - name: KEYCLOAK_DB_USERNAME
              path: postgresql.username
            - name: KEYCLOAK_DB_DATABASE
              path: postgresql.database
            - name: KEYCLOAK_DB_PASSWORD
              path: postgresql.password
              sensitive: true
