# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: v1
kind: Secret
metadata:
  name: {{ include "uds-grafana-config.fullname" . }}-postgresql
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "uds-grafana-config.labels" . | nindent 4 }}
type: Opaque
stringData:
  {{- if eq (include "grafana.postgresql.config" .) "true" }}
  GF_DATABASE_TYPE: "postgres"
  GF_DATABASE_HOST: "{{ .Values.postgresql.host }}:{{ .Values.postgresql.port }}"
  GF_DATABASE_NAME: "{{ .Values.postgresql.database }}"
  GF_DATABASE_USER: "{{ .Values.postgresql.user }}"
  GF_DATABASE_PASSWORD: "{{ .Values.postgresql.password }}"
  GF_DATABASE_SSL_MODE: "{{ .Values.postgresql.ssl_mode }}"
  {{- else }}
  {}
  {{- end }}
