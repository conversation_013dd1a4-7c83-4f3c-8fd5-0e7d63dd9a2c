# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: Notify Lula
description: "Comment on PR to notify Lula Team"


inputs:
  state:
    description: 'state of the comment update'
    required: true
    default: ''
  flavor:
    description: 'flavor of the comment update'
    required: true
    default: ''
  ghToken:
    description: 'GITHUB_TOKEN'
    required: true

runs:
  using: composite
  steps:
    - name: Find Comment
      uses: peter-evans/find-comment@3eae4d37986fb5a8592848f6a574fdf654e61f9e # v3.1.0
      id: fc
      with:
        issue-number: ${{ github.event.pull_request.number }}
        comment-author: 'github-actions[bot]'
        body-includes: Compliance ${{ inputs.flavor }} Evaluation
        token: ${{ inputs.ghToken }}

    - name: Create comment
      if: ${{ steps.fc.outputs.comment-id == '' && inputs.state == 'failure'}}
      uses: peter-evans/create-or-update-comment@71345be0265236311c031f5c7866368bd1eff043 # v4.0.0
      with:
        issue-number: ${{ github.event.pull_request.number }}
        token: ${{ inputs.ghToken }}
        body: |
          Compliance ${{ inputs.flavor }} Evaluation: ${{ inputs.state }}

          CC: @defenseunicorns/lula-dev

    - name: Update comment
      if: ${{ steps.fc.outputs.comment-id != '' }}
      uses: peter-evans/create-or-update-comment@71345be0265236311c031f5c7866368bd1eff043 # v4.0.0
      with:
        comment-id: ${{ steps.fc.outputs.comment-id }}
        token: ${{ inputs.ghToken }}
        edit-mode: replace
        body: |
          Compliance ${{ inputs.flavor }} Evaluation: ${{ inputs.state }}

          CC: @defenseunicorns/lula-dev
