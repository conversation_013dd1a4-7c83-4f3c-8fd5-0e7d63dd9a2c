# Test apps with various network allow configurations for egress testing

# Package 1
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-1
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-1
  namespace: test-app-1
spec:
  sso:
    - name: Test App 1 - Multiple Valid URIs
      clientId: test-shared-1
      redirectUris:
        - "https://test-shared-1.uds.dev/login"
        - "https://test-shared-1.uds.dev/callback"
        - "https://test-shared-1.uds.dev/auth/redirect"
      enableAuthserviceSelector:
        app: test-app-1

---

# Package 2
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-2
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-2
  namespace: test-app-2
spec:
  sso:
    - name: Test App 2 - Invalid URI Format
      clientId: test-shared-1
      redirectUris:
        - "not-a-valid-uri"
        - "https://test-shared-1.uds.dev/login"
      enableAuthserviceSelector:
        app: test-app-2

---

# Package 3
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-3
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-3
  namespace: test-app-3
spec:
  sso:
    - name: Test App 3 - External Domain
      clientId: test-shared-1
      redirectUris:
        - "https://external-app.example.com/login"
        - "https://another-domain.org/callback"
      enableAuthserviceSelector:
        app: test-app-3

---

# Package 4
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-4
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-4
  namespace: test-app-4
spec:
  sso:
    - name: Test App 4 - Mixed Valid/Invalid
      clientId: test-shared-2
      redirectUris:
        - "https://test-shared-2.uds.dev/login"
        - "ftp://invalid-protocol.uds.dev/login"
        - "https://test-shared-2.uds.dev/admin"
      enableAuthserviceSelector:
        app: test-app-4

---

# Package 5
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-5
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-5
  namespace: test-app-5
spec:
  sso:
    - name: Test App 5 - Localhost URIs
      clientId: test-shared-2
      redirectUris:
        - "http://localhost:8080/callback"
        - "https://127.0.0.1:3000/login"
        - "https://test-shared-2.uds.dev/login"
      enableAuthserviceSelector:
        app: test-app-5

---

# Package 6
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-6
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-6
  namespace: test-app-6
spec:
  sso:
    - name: Test App 6 - Malformed URLs
      clientId: test-shared-2
      redirectUris:
        - "https://test-shared-2.uds.dev/login"
        - "https://malformed..domain.com/login"
        - "http://spaces in url.com/callback"
      enableAuthserviceSelector:
        app: test-app-6

---

# Package 7
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-7
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-7
  namespace: test-app-7
spec:
  sso:
    - name: Test App 7 - Corporate Domains
      clientId: test-shared-3
      redirectUris:
        - "https://app.company.com/sso/callback"
        - "https://internal.corp.net/auth"
        - "https://enterprise.business.org/login"
      enableAuthserviceSelector:
        app: test-app-7

---

# Package 8
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-8
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-8
  namespace: test-app-8
spec:
  sso:
    - name: Test App 8 - Empty and Invalid
      clientId: test-shared-3
      redirectUris:
        - ""
        - "javascript:alert('xss')"
        - "https://test-shared-3.uds.dev/login"
      enableAuthserviceSelector:
        app: test-app-8

---

# Package 9
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-9
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-9
  namespace: test-app-9
spec:
  sso:
    - name: Test App 9 - Long URI List
      clientId: test-shared-3
      redirectUris:
        - "https://test-shared-3.uds.dev/login"
        - "https://test-shared-3.uds.dev/callback"
        - "https://test-shared-3.uds.dev/auth/success"
        - "https://test-shared-3.uds.dev/auth/failure"
        - "https://backup.example.com/fallback"
        - "https://mobile.app.com/redirect"
      enableAuthserviceSelector:
        app: test-app-9

---

# Package 10
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-10
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-10
  namespace: test-app-10
spec:
  sso:
    - name: Test App 10 - IP Addresses
      clientId: test-shared-4
      redirectUris:
        - "https://*************:8443/callback"
        - "http://*********/login"
        - "https://test-shared-4.uds.dev/login"
      enableAuthserviceSelector:
        app: test-app-10
---

# Package 11
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-11
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-11
  namespace: test-app-11
spec:
  sso:
    - name: Test App 11 - Non-HTTPS Protocols
      clientId: test-shared-4
      redirectUris:
        - "http://insecure.uds.dev/login"
        - "ftp://file-server.com/callback"
        - "ldap://directory.company.com/auth"
      enableAuthserviceSelector:
        app: test-app-11

---

# Package 12
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-12
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-12
  namespace: test-app-12
spec:
  sso:
    - name: Test App 12 - Special Characters
      clientId: test-shared-4
      redirectUris:
        - "https://test-shared-4.uds.dev/login"
        - "https://app-with-dashes.uds.dev/callback"
        - "https://app_with_underscores.example.com/auth"
        - "https://números.español.com/login"
      enableAuthserviceSelector:
        app: test-app-12

---

# Package 13
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-13
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-13
  namespace: test-app-13
spec:
  sso:
    - name: Test App 13 - Query Parameters
      clientId: test-shared-5
      redirectUris:
        - "https://test-shared-5.uds.dev/login?param=value"
        - "https://app.example.com/callback?state=abc123&code=xyz"
        - "https://test-shared-5.uds.dev/auth#fragment"
      enableAuthserviceSelector:
        app: test-app-13

---

# Package 14
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-14
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-14
  namespace: test-app-14
spec:
  sso:
    - name: Test App 14 - Wildcard Attempts
      clientId: test-shared-5
      redirectUris:
        - "https://*.uds.dev/login"
        - "https://test-shared-5.uds.dev/*"
        - "https://test-shared-5.uds.dev/login"
      enableAuthserviceSelector:
        app: test-app-14

---

# Package 15
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-15
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-15
  namespace: test-app-15
spec:
  sso:
    - name: Test App 15 - Very Long URLs
      clientId: test-shared-5
      redirectUris:
        - "https://test-shared-5.uds.dev/login"
        - "https://very-long-subdomain-name-that-exceeds-normal-limits.extremely-long-domain-name.com/very/long/path/with/many/segments/callback"
        - "https://short.com/login"
      enableAuthserviceSelector:
        app: test-app-15

---

# Package 16
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-16
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-16
  namespace: test-app-16
spec:
  sso:
    - name: Test App 16 - Port Numbers
      clientId: test-16
      redirectUris:
        - "https://test-16.uds.dev:8443/login"
        - "http://app.example.com:3000/callback"
        - "https://secure.domain.org:9999/auth"
      enableAuthserviceSelector:
        app: test-app-16

---

# Package 17
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-17
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-17
  namespace: test-app-17
spec:
  sso:
    - name: Test App 17 - Suspicious URLs
      clientId: test-17
      redirectUris:
        - "https://phishing-site.malicious.com/steal-tokens"
        - "https://test-17.uds.dev/login"
        - "data:text/html,<script>alert('xss')</script>"
      enableAuthserviceSelector:
        app: test-app-17

---

# Package 18
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-18
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-18
  namespace: test-app-18
spec:
  sso:
    - name: Test App 18 - Mixed Case Domains
      clientId: test-18
      redirectUris:
        - "https://Test-18.UDS.DEV/Login"
        - "HTTPS://UPPERCASE.EXAMPLE.COM/CALLBACK"
        - "https://MiXeD-CaSe.DoMaIn.org/auth"
      enableAuthserviceSelector:
        app: test-app-18

---

# Package 19
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-19
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-19
  namespace: test-app-19
spec:
  sso:
    - name: Test App 19 - Edge Case Paths
      clientId: test-19
      redirectUris:
        - "https://test-19.uds.dev/login"
        - "https://app.example.com/"
        - "https://minimal.com"
        - "https://deep.nested.path.example.com/a/b/c/d/e/f/g/callback"
        - "https://encoded%20spaces.com/login"
      enableAuthserviceSelector:
        app: test-app-19

---

# Package 20
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-20
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-20
  namespace: test-app-20
spec:
  sso:
    - name: Test App 20 - International Domains
      clientId: test-20
      redirectUris:
        - "https://test-20.uds.dev/login"
        - "https://münchen.deutschland.de/anmeldung"
        - "https://тест.рф/вход"
        - "https://测试.中国/登录"
        - "https://xn--nxasmq6b.xn--j6w193g/callback"
      enableAuthserviceSelector:
        app: test-app-20