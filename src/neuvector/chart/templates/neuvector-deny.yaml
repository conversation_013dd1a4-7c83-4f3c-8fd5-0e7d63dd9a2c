# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

{{- if .Values.denyLocalAuth }}
apiVersion: security.istio.io/v1
kind: AuthorizationPolicy
metadata:
  name: neuvector-deny-local-login
  namespace: {{ .Release.Namespace }}
spec:
  action: DENY
  targetRefs:
  - name: neuvector-manager-waypoint
    kind: Gateway
    group: gateway.networking.k8s.io
  rules:
  - to:
    - operation:
        paths: ["/auth"]
        ports: ["8443"]
        # Don't block DELETE which is used for OIDC logout
        methods: ["POST","GET","PUT","CONNECT"]
{{- end }}
