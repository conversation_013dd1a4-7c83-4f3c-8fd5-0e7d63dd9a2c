apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-3
  namespace: test-3
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 80
        remoteHost: example.com
        remoteProtocol: HTTP
        description: "Example Curl"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-4
  namespace: test-4
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 80
        remoteHost: example.com
        remoteProtocol: HTTP
        description: "Example Curl"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-5
  namespace: test-5
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 80
        remoteHost: example.com
        remoteProtocol: HTTP
        description: "Example Curl"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-8
  namespace: test-8
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 443
        remoteHost: google.com
        remoteProtocol: TLS
        description: "Example Curl"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-9
  namespace: test-9
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 443
        remoteHost: example.com
        remoteProtocol: TLS
        description: "Example Curl"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-10
  namespace: test-10
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 443
        remoteHost: example.com
        remoteProtocol: TLS
        description: "Example Curl"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-14
  namespace: test-14
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 8888
        remoteHost: example.com
        remoteProtocol: HTTP
        description: "Bad port 8888 - should fail"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-15
  namespace: test-15
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 443
        remoteHost: kubernetes.io
        remoteProtocol: TLS
        description: "Kubernetes Docs"
      - direction: Egress
        selector:
          app: curl
        ports:
          - 80
          - 443
        remoteHost: helm.sh
        remoteProtocol: TLS
        description: "Helm Multi-port"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-16
  namespace: test-16
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 7777
        remoteHost: badservice.local
        remoteProtocol: TLS
        description: "Bad port 7777 - should fail"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-17
  namespace: test-17
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 443
        remoteHost: quay.io
        remoteProtocol: TLS
        description: "Quay Registry"
      - direction: Egress
        selector:
          app: curl
        ports:
          - 443
        remoteHost: registry.redhat.io
        remoteProtocol: TLS
        description: "Red Hat Registry"
      - direction: Egress
        selector:
          app: curl
        ports:
          - 80
        remoteHost: mirror.openshift.com
        remoteProtocol: HTTP
        description: "OpenShift Mirror"
      - direction: Egress
        selector:
          app: curl
        ports:
          - 443
        remoteHost: access.redhat.com
        remoteProtocol: TLS
        description: "Red Hat Access"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-18
  namespace: test-18
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 5555
        remoteHost: example.com
        remoteProtocol: HTTP
        description: "Bad port 5555 - should fail"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-20
  namespace: test-20
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 3333
        remoteHost: failhost.example
        remoteProtocol: TLS
        description: "Bad port 3333 - should fail"