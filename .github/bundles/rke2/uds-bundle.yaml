# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: UDSBundle
metadata:
  name: uds-core-rke2-nightly
  description: A UDS bundle for deploying RKE2 and UDS Core
  # x-release-please-start-version
  version: "0.45.1"
  # x-release-please-end

packages:
  - name: pod-identity-webhook
    repository: ghcr.io/defenseunicorns/packages/uds/pod-identity-webhook
    ref: 0.3.1-upstream

  - name: init
    repository: ghcr.io/zarf-dev/packages/init
    ref: v0.57.0
    overrides:
      zarf-registry:
        docker-registry:
          variables:
            - path: affinity.custom
              name: REGISTRY_AFFINITY_CUSTOM_UDS
            - path: persistence.accessMode
              name: REGISTRY_PVC_ACCESS_MODE
              default: ReadWriteMany
      zarf-seed-registry:
        docker-registry:
          variables:
            - path: affinity.custom
              name: REGISTRY_AFFINITY_CUSTOM_UDS
            - path: persistence.accessMode
              name: R<PERSON><PERSON><PERSON>Y_PVC_ACCESS_MODE
              default: ReadWriteMany

  - name: core
    path: ../../../build
    # x-release-please-start-version
    ref: 0.45.1
    # x-release-please-end
    optionalComponents:
      - metrics-server
      - istio-egress-gateway
    overrides:
      istio-admin-gateway:
        gateway:
          values:
            - path: service.annotations
              value:
                service.beta.kubernetes.io/aws-load-balancer-scheme: "internet-facing"
                service.beta.kubernetes.io/aws-load-balancer-target-node-labels: "kubernetes.io/os=linux"
      istio-tenant-gateway:
        gateway:
          values:
            - path: service.annotations
              value:
                service.beta.kubernetes.io/aws-load-balancer-scheme: "internet-facing"
                service.beta.kubernetes.io/aws-load-balancer-target-node-labels: "kubernetes.io/os=linux"
      kube-prometheus-stack:
        uds-prometheus-config:
          values:
            - path: rke2CorednsNetpol.enabled
              value: true
      velero:
        velero:
          variables:
            - name: VELERO_USE_SECRET
              description: "Toggle use secret off to use IRSA."
              path: credentials.useSecret
            - name: VELERO_IRSA_ROLE_ARN
              description: "IRSA ARN annotation to use for Velero"
              path: serviceAccount.server.annotations.irsa/role-arn
      loki:
        loki:
          values:
            - path: loki.storage.s3.endpoint
              value: ""
            - path: loki.storage.s3.secretAccessKey
              value: ""
            - path: loki.storage.s3.accessKeyId
              value: ""
            - path: global.dnsService
              value: rke2-coredns-rke2-coredns
          variables:
            - name: LOKI_CHUNKS_BUCKET
              description: "The object storage bucket for Loki chunks"
              path: loki.storage.bucketNames.chunks
            - name: LOKI_RULER_BUCKET
              description: "The object storage bucket for Loki ruler"
              path: loki.storage.bucketNames.ruler
            - name: LOKI_ADMIN_BUCKET
              description: "The object storage bucket for Loki admin"
              path: loki.storage.bucketNames.admin
            - name: LOKI_S3_REGION
              description: "The S3 region"
              path: loki.storage.s3.region
            - name: LOKI_IRSA_ROLE_ARN
              description: "The irsa role annotation"
              path: serviceAccount.annotations.irsa/role-arn
      grafana:
        grafana:
          variables:
            - name: GRAFANA_HA
              description: Enable HA Grafana
              path: autoscaling.enabled
        uds-grafana-config:
          variables:
            - name: GRAFANA_PG_HOST
              description: Grafana postgresql host
              path: postgresql.host
            - name: GRAFANA_PG_PORT
              description: Grafana postgresql port
              path: postgresql.port
            - name: GRAFANA_PG_DATABASE
              description: Grafana postgresql database
              path: postgresql.database
            - name: GRAFANA_PG_PASSWORD
              description: Grafana postgresql password
              path: postgresql.password
              sensitive: true
            - name: GRAFANA_PG_USER
              description: Grafana postgresql username
              path: postgresql.user
      keycloak:
        keycloak:
          values:
            - path: devMode
              value: false
            - path: autoscaling.enabled
              value: true
          variables:
            - name: KEYCLOAK_DB_HOST
              path: postgresql.host
            - name: KEYCLOAK_DB_USERNAME
              path: postgresql.username
            - name: KEYCLOAK_DB_DATABASE
              path: postgresql.database
            - name: KEYCLOAK_DB_PASSWORD
              path: postgresql.password
              sensitive: true
