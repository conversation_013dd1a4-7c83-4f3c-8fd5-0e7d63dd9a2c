# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: Nightly

on:
  schedule:
    # Runs every morning at 2:00 AM UTC
    - cron: '0 2 * * *'
  pull_request:
    # milestoned is added here as a workaround for release-please not triggering PR workflows (PRs should be added to a milestone to trigger the workflow).
    # labeled is added to support renovate-ready labelling on PRs
    types: [milestoned, labeled, opened, reopened, synchronize]
    paths:
      - '.github/workflows/upgrade-ha.yaml'
      - 'bundles/k3d-standard/**'

permissions:
  contents: read
  id-token: write
  packages: read
  pull-requests: write # Allows writing to pull requests (needed for renovate-readiness)

jobs:
  uds-core-ha-upgrade-nightly:
    runs-on: uds-ubuntu-big-boy-8-core
    timeout-minutes: 45
    name: HA Testing

    strategy:
      matrix:
        flavor: [upstream, registry1, unicorn]
        test-type: [install, upgrade]

    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Check renovate readiness
        if: startsWith(github.event.pull_request.head.ref, 'renovate/') # Only call for Renovate PRs
        uses: ./.github/actions/renovate-readiness
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Environment setup
        uses: ./.github/actions/setup
        with:
          registry1Username: ${{ secrets.IRON_BANK_ROBOT_USERNAME }}
          registry1Password: ${{ secrets.IRON_BANK_ROBOT_PASSWORD }}
          ghToken: ${{ secrets.GITHUB_TOKEN }}
          rapidfortUsername: ${{ secrets.RAPIDFORT_USERNAME }}
          rapidfortPassword: ${{ secrets.RAPIDFORT_PASSWORD }}

      - name: Run UDS Core Install HA Test
        if: ${{ matrix.test-type == 'install' }}
        run: uds run test-uds-core-ha --set FLAVOR=${{ matrix.flavor }} --no-progress

      - name: Run UDS Core Upgrade HA Test
        if: ${{ matrix.test-type == 'upgrade' }}
        run: uds run test-uds-core-ha-upgrade --set FLAVOR=${{ matrix.flavor }} --no-progress

      - name: Debug Output
        if: ${{ always() }}
        uses: ./.github/actions/debug-output

      - name: Save logs
        if: always()
        uses: ./.github/actions/save-logs
        with:
          suffix: -nightly-${{ matrix.flavor }}-${{ matrix.test-type }}
