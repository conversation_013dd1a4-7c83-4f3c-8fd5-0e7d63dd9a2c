# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

variables:
  - name: BASE_REPO
    default: "ghcr.io/defenseunicorns/packages"
  - name: FLAVOR
    default: "upstream"
  - name: SNAPSHOT
    description: Whether this is a snapshot release
    default: "false"

tasks:
  - name: determine-repo
    actions:
      - description: "Determine repository for the given flavor/type of release"
        cmd: |
          repo="${BASE_REPO}"
          # unicorn flavor = private repository
          if [ "${FLAVOR}" = "unicorn" ]; then
            repo="${repo}/private"
          fi
          repo="${repo}/uds"
          # snapshots = snapshot repository
          if [ "${SNAPSHOT}" = "true" ]; then
            repo="${repo}/snapshots"
          fi
          echo "${repo}"
        mute: true
        setVariables:
          - name: TARGET_REPO

  - name: aks-coredns-setup
    actions:
      - description: Setup Custom ConfigMap for Core DNS
        cmd: |
          uds zarf tools kubectl apply -f - <<EOF
          apiVersion: v1
          data:
            uds.override: |
              rewrite stop {
                name regex (.*\.admin\.uds\.dev) admin-ingressgateway.istio-admin-gateway.svc.cluster.local answer auto
              }
              rewrite stop {
                name regex (.*\.uds\.dev) tenant-ingressgateway.istio-tenant-gateway.svc.cluster.local answer auto
              }
          kind: ConfigMap
          metadata:
            name: coredns-custom
            namespace: kube-system
          EOF
            uds zarf tools kubectl -n kube-system rollout restart deployment coredns
  - name: eks-storageclass-setup
    actions:
      - description: Setup GP3 Storage Class
        cmd: |
          uds zarf tools kubectl apply -f - <<EOF
            apiVersion: storage.k8s.io/v1
            kind: StorageClass
            metadata:
              name: gp3
              annotations:
                storageclass.kubernetes.io/is-default-class: "true"
            provisioner: ebs.csi.aws.com
            allowVolumeExpansion: true
            reclaimPolicy: Delete
            volumeBindingMode: WaitForFirstConsumer
            parameters:
              encrypted: "true"
              fsType: ext4
              type: gp3
          EOF
  - name: admin-gw-ip
    actions:
      - description: Fetch Admin Gateway IP Address
        cmd: |
          IP_ADDR=$(uds zarf tools kubectl get service -n istio-admin-gateway admin-ingressgateway -o=jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null)
          if [ -z $IP_ADDR ]; then
            HOSTNAME=$(uds zarf tools kubectl get service -n istio-admin-gateway admin-ingressgateway -o=jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null)
            IP_ADDR=$(dig +short $HOSTNAME | head -n1)
          fi; echo $IP_ADDR
        mute: true
        setVariables:
          - name: ADMIN_GW_IP
  - name: tenant-gw-ip
    actions:
      - description: Fetch Tenant Gateway IP Address
        cmd: |
          IP_ADDR=$(uds zarf tools kubectl get service -n istio-tenant-gateway tenant-ingressgateway -o=jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null)
          if [ -z $IP_ADDR ]; then
            HOSTNAME=$(uds zarf tools kubectl get service -n istio-tenant-gateway tenant-ingressgateway -o=jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null)
            IP_ADDR=$(dig +short $HOSTNAME | head -n1)
          fi; echo $IP_ADDR
        mute: true
        setVariables:
          - name: TENANT_GW_IP
  - name: setup-hosts
    actions:
      - task: admin-gw-ip
      - task: tenant-gw-ip
      - description: Adds Cluster LoadBalancer IP Addresses to match appropriate hosts names in /etc/hosts
        mute: true
        cmd: |
          echo "$ADMIN_GW_IP keycloak.admin.uds.dev neuvector.admin.uds.dev grafana.admin.uds.dev demo.admin.uds.dev\n$TENANT_GW_IP sso.uds.dev demo-8080.uds.dev demo-8081.uds.dev protected.uds.dev" | sudo tee -a /etc/hosts
