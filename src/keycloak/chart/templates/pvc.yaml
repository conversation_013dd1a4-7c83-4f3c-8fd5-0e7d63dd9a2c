# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

{{- if .Values.persistence.providers.enabled }}
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: {{ include "keycloak.fullname" . }}-providers
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
spec:
  accessModes:
    - {{ .Values.persistence.accessMode | quote }}
  resources:
    requests:
      storage: {{ .Values.persistence.providers.size | quote }}
  {{- if .Values.persistence.storageClassName }}
  storageClassName: "{{ .Values.persistence.storageClassName }}"
  {{- end }}
---
{{- end }}

{{- if .Values.persistence.conf.enabled }}
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: {{ include "keycloak.fullname" . }}-conf
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
spec:
  accessModes:
    - {{ .Values.persistence.accessMode | quote }}
  resources:
    requests:
      storage: {{ .Values.persistence.conf.size | quote }}
  {{- if .Values.persistence.storageClassName }}
  storageClassName: "{{ .Values.persistence.storageClassName }}"
  {{- end }}
---
{{- end }}

{{- if or .Values.persistence.data.enabled .Values.devMode }}
# devMode enables this PVC by default to preserve legacy behavior
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: {{ include "keycloak.fullname" . }}-data
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
spec:
  accessModes:
    - {{ .Values.persistence.accessMode | quote }}
  resources:
    requests:
      storage: {{ .Values.persistence.data.size | quote }}
  {{- if .Values.persistence.storageClassName }}
  storageClassName: "{{ .Values.persistence.storageClassName }}"
  {{- end }}
---
{{- end }}

{{- if or .Values.persistence.themes.enabled .Values.devMode }}
# devMode enables this PVC by default to preserve legacy behavior
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: {{ include "keycloak.fullname" . }}-themes
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
spec:
  accessModes:
    - {{ .Values.persistence.accessMode | quote }}
  resources:
    requests:
      storage: {{ .Values.persistence.themes.size | quote }}
  {{- if .Values.persistence.storageClassName }}
  storageClassName: "{{ .Values.persistence.storageClassName }}"
  {{- end }}
{{- end }}
