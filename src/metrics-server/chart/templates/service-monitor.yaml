# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

{{- if .Capabilities.APIVersions.Has "monitoring.coreos.com/v1" }}
# The serviceMonitor for metrics-server is unique due to permissive mTLS on its port, so it is created outside of the Package spec
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  annotations:
    uds/skip-sm-mutate: "true"
  name: metrics-server-metrics
  namespace: metrics-server
spec:
  endpoints:
  - path: /metrics
    port: https
    scheme: https
    tlsConfig:
      insecureSkipVerify: true
  selector:
    matchLabels:
      app.kubernetes.io/name: metrics-server
{{- end }}
