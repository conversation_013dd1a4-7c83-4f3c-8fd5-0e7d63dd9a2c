{"peerAuths": [{"apiVersion": "security.istio.io/v1beta1", "kind": "PeerAuthentication", "metadata": {"annotations": {"meta.helm.sh/release-name": "zarf-a102b532d6a523b085622665b606574b0cd82025", "meta.helm.sh/release-namespace": "istio-system"}, "creationTimestamp": "2024-06-07T14:35:59Z", "generation": 1, "labels": {"app.kubernetes.io/managed-by": "<PERSON><PERSON>"}, "name": "default-istio-system", "namespace": "istio-system", "resourceVersion": "1154", "uid": "846d35fc-f942-4efc-b1ea-be35d3352db8"}, "spec": {"mtls": {"mode": "STRICT"}}}, {"apiVersion": "security.istio.io/v1beta1", "kind": "PeerAuthentication", "metadata": {"annotations": {"meta.helm.sh/release-name": "zarf-a102b532d6a523b085622665b606574b0cd82025", "meta.helm.sh/release-namespace": "istio-system"}, "creationTimestamp": "2024-06-07T14:35:59Z", "generation": 1, "labels": {"app.kubernetes.io/managed-by": "<PERSON><PERSON>"}, "name": "permissive-pepr-webhook", "namespace": "pepr-system", "resourceVersion": "1155", "uid": "8911bc2b-fc43-414c-9511-7712c463a8f3"}, "spec": {"mtls": {"mode": "STRICT"}, "portLevelMtls": {"3000": {"mode": "PERMISSIVE"}}, "selector": {"matchLabels": {"pepr.dev/controller": "admission"}}}}, {"apiVersion": "security.istio.io/v1beta1", "kind": "PeerAuthentication", "metadata": {"annotations": {"meta.helm.sh/release-name": "uds-metrics-server-config", "meta.helm.sh/release-namespace": "metrics-server"}, "creationTimestamp": "2024-06-07T14:36:34Z", "generation": 1, "labels": {"app.kubernetes.io/managed-by": "<PERSON><PERSON>"}, "name": "metrics-server-api-exception", "namespace": "metrics-server", "resourceVersion": "1520", "uid": "829a5b07-76c8-45f9-903e-f90976b677aa"}, "spec": {"mtls": {"mode": "STRICT"}, "portLevelMtls": {"10250": {"mode": "PERMISSIVE"}}, "selector": {"matchLabels": {"app.kubernetes.io/name": "metrics-server"}}}}, {"apiVersion": "security.istio.io/v1beta1", "kind": "PeerAuthentication", "metadata": {"annotations": {"meta.helm.sh/release-name": "keycloak", "meta.helm.sh/release-namespace": "keycloak"}, "creationTimestamp": "2024-06-07T14:37:03Z", "generation": 1, "labels": {"app.kubernetes.io/managed-by": "<PERSON><PERSON>"}, "name": "keycloak", "namespace": "keycloak", "resourceVersion": "1659", "uid": "21586733-a185-4a6b-b8cc-cc77bb77b012"}, "spec": {"mtls": {"mode": "STRICT"}}}, {"apiVersion": "security.istio.io/v1beta1", "kind": "PeerAuthentication", "metadata": {"annotations": {"meta.helm.sh/release-name": "uds-neuvector-config", "meta.helm.sh/release-namespace": "neuvector"}, "creationTimestamp": "2024-06-07T14:38:19Z", "generation": 1, "labels": {"app.kubernetes.io/managed-by": "<PERSON><PERSON>"}, "name": "controller-neuvector", "namespace": "neuvector", "resourceVersion": "1912", "uid": "c927e10e-e859-4f54-b2ad-ff457ae8a7e5"}, "spec": {"mtls": {"mode": "STRICT"}, "portLevelMtls": {"18300": {"mode": "PERMISSIVE"}, "30443": {"mode": "PERMISSIVE"}}, "selector": {"matchLabels": {"app": "neuvector-controller-pod"}}}}, {"apiVersion": "security.istio.io/v1beta1", "kind": "PeerAuthentication", "metadata": {"annotations": {"meta.helm.sh/release-name": "uds-loki-config", "meta.helm.sh/release-namespace": "loki"}, "creationTimestamp": "2024-06-07T14:38:49Z", "generation": 1, "labels": {"app.kubernetes.io/managed-by": "<PERSON><PERSON>"}, "name": "loki-simple-scalable", "namespace": "loki", "resourceVersion": "2382", "uid": "78bfd0bf-5682-45b5-9a38-0fc4dee6ccd4"}, "spec": {"mtls": {"mode": "STRICT"}, "portLevelMtls": {"9095": {"mode": "PERMISSIVE"}}, "selector": {"matchLabels": {"app.kubernetes.io/name": "loki"}}}}, {"apiVersion": "security.istio.io/v1beta1", "kind": "PeerAuthentication", "metadata": {"annotations": {"meta.helm.sh/release-name": "uds-prometheus-config", "meta.helm.sh/release-namespace": "monitoring"}, "creationTimestamp": "2024-06-07T14:40:25Z", "generation": 1, "labels": {"app.kubernetes.io/managed-by": "<PERSON><PERSON>"}, "name": "prometheus-operator-webhook", "namespace": "monitoring", "resourceVersion": "2862", "uid": "1131d8b9-3c21-4076-b22a-e4bd3c0bdc44"}, "spec": {"mtls": {"mode": "STRICT"}, "portLevelMtls": {"10250": {"mode": "PERMISSIVE"}}, "selector": {"matchLabels": {"app": "kube-prometheus-stack-operator"}}}}]}