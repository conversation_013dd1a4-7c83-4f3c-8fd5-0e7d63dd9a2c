{"istioddeployment": {"apiVersion": "apps/v1", "kind": "Deployment", "metadata": {"annotations": {"deployment.kubernetes.io/revision": "1", "meta.helm.sh/release-name": "isti<PERSON>", "meta.helm.sh/release-namespace": "istio-system"}, "creationTimestamp": "2024-06-07T14:35:55Z", "generation": 1, "labels": {"app": "isti<PERSON>", "app.kubernetes.io/managed-by": "<PERSON><PERSON>", "install.operator.istio.io/owning-resource": "unknown", "istio": "pilot", "istio.io/rev": "default", "operator.istio.io/component": "Pilot", "release": "isti<PERSON>"}, "name": "isti<PERSON>", "namespace": "istio-system", "resourceVersion": "1141", "uid": "c913f7f1-0ac7-4a73-9fd5-614715411fb6"}, "spec": {"progressDeadlineSeconds": 600, "replicas": 1, "revisionHistoryLimit": 10, "selector": {"matchLabels": {"istio": "pilot"}}, "strategy": {"rollingUpdate": {"maxSurge": "100%", "maxUnavailable": "25%"}, "type": "RollingUpdate"}, "template": {"metadata": {"annotations": {"prometheus.io/port": "15014", "prometheus.io/scrape": "true", "sidecar.istio.io/inject": "false"}, "creationTimestamp": null, "labels": {"app": "isti<PERSON>", "install.operator.istio.io/owning-resource": "unknown", "istio": "pilot", "istio.io/dataplane-mode": "none", "istio.io/rev": "default", "operator.istio.io/component": "Pilot", "sidecar.istio.io/inject": "false"}}, "spec": {"containers": [{"args": ["discovery", "--monitoringAddr=:15014", "--log_output_level=default:info", "--domain", "cluster.local", "--keepaliveMaxServerConnectionAge", "30m"], "env": [{"name": "REVISION", "value": "default"}, {"name": "PILOT_CERT_PROVIDER", "value": "isti<PERSON>"}, {"name": "POD_NAME", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "metadata.name"}}}, {"name": "POD_NAMESPACE", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "metadata.namespace"}}}, {"name": "SERVICE_ACCOUNT", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "spec.serviceAccountName"}}}, {"name": "KUBECONFIG", "value": "/var/run/secrets/remote/config"}, {"name": "PILOT_TRACE_SAMPLING", "value": "1"}, {"name": "PILOT_ENABLE_ANALYSIS", "value": "false"}, {"name": "CLUSTER_ID", "value": "Kubernetes"}, {"name": "GOMEMLIMIT", "valueFrom": {"resourceFieldRef": {"divisor": "0", "resource": "limits.memory"}}}, {"name": "GOMAXPROCS", "valueFrom": {"resourceFieldRef": {"divisor": "0", "resource": "limits.cpu"}}}, {"name": "PLATFORM"}], "image": "docker.io/istio/pilot:1.22.1-distroless", "imagePullPolicy": "IfNotPresent", "name": "discovery", "ports": [{"containerPort": 8080, "protocol": "TCP"}, {"containerPort": 15010, "protocol": "TCP"}, {"containerPort": 15017, "protocol": "TCP"}], "readinessProbe": {"failureThreshold": 3, "httpGet": {"path": "/ready", "port": 8080, "scheme": "HTTP"}, "initialDelaySeconds": 1, "periodSeconds": 3, "successThreshold": 1, "timeoutSeconds": 5}, "resources": {"requests": {"cpu": "500m", "memory": "2Gi"}}, "securityContext": {"allowPrivilegeEscalation": false, "capabilities": {"drop": ["ALL"]}, "readOnlyRootFilesystem": true, "runAsNonRoot": true}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File", "volumeMounts": [{"mountPath": "/var/run/secrets/tokens", "name": "istio-token", "readOnly": true}, {"mountPath": "/var/run/secrets/istio-dns", "name": "local-certs"}, {"mountPath": "/etc/cacerts", "name": "cacerts", "readOnly": true}, {"mountPath": "/var/run/secrets/remote", "name": "istio-kubeconfig", "readOnly": true}, {"mountPath": "/var/run/secrets/istiod/tls", "name": "istio-csr-dns-cert", "readOnly": true}, {"mountPath": "/var/run/secrets/istiod/ca", "name": "istio-csr-ca-configmap", "readOnly": true}]}], "dnsPolicy": "ClusterFirst", "restartPolicy": "Always", "schedulerName": "default-scheduler", "securityContext": {}, "serviceAccount": "isti<PERSON>", "serviceAccountName": "isti<PERSON>", "terminationGracePeriodSeconds": 30, "tolerations": [{"key": "cni.istio.io/not-ready", "operator": "Exists"}], "volumes": [{"emptyDir": {"medium": "Memory"}, "name": "local-certs"}, {"name": "istio-token", "projected": {"defaultMode": 420, "sources": [{"serviceAccountToken": {"audience": "istio-ca", "expirationSeconds": 43200, "path": "istio-token"}}]}}, {"name": "cacerts", "secret": {"defaultMode": 420, "optional": true, "secretName": "cacerts"}}, {"name": "istio-kubeconfig", "secret": {"defaultMode": 420, "optional": true, "secretName": "istio-kubeconfig"}}, {"name": "istio-csr-dns-cert", "secret": {"defaultMode": 420, "optional": true, "secretName": "istiod-tls"}}, {"configMap": {"defaultMode": 420, "name": "istio-ca-root-cert", "optional": true}, "name": "istio-csr-ca-configmap"}]}}}, "status": {"availableReplicas": 1, "conditions": [{"lastTransitionTime": "2024-06-07T14:35:57Z", "lastUpdateTime": "2024-06-07T14:35:57Z", "message": "Deployment has minimum availability.", "reason": "MinimumReplicasAvailable", "status": "True", "type": "Available"}, {"lastTransitionTime": "2024-06-07T14:35:55Z", "lastUpdateTime": "2024-06-07T14:35:57Z", "message": "ReplicaSet \"istiod-5d75444496\" has successfully progressed.", "reason": "NewReplicaSetAvailable", "status": "True", "type": "Progressing"}], "observedGeneration": 1, "readyReplicas": 1, "replicas": 1, "updatedReplicas": 1}}, "istiodhpa": {"apiVersion": "autoscaling/v2", "kind": "HorizontalPodAutoscaler", "metadata": {"annotations": {"meta.helm.sh/release-name": "isti<PERSON>", "meta.helm.sh/release-namespace": "istio-system"}, "creationTimestamp": "2024-06-07T14:35:55Z", "labels": {"app": "isti<PERSON>", "app.kubernetes.io/managed-by": "<PERSON><PERSON>", "install.operator.istio.io/owning-resource": "unknown", "istio.io/rev": "default", "operator.istio.io/component": "Pilot", "release": "isti<PERSON>"}, "name": "isti<PERSON>", "namespace": "istio-system", "resourceVersion": "153610", "uid": "595b2821-fb62-4203-9a4d-c3bba5689215"}, "spec": {"maxReplicas": 5, "metrics": [{"resource": {"name": "cpu", "target": {"averageUtilization": 80, "type": "Utilization"}}, "type": "Resource"}], "minReplicas": 1, "scaleTargetRef": {"apiVersion": "apps/v1", "kind": "Deployment", "name": "isti<PERSON>"}}, "status": {"conditions": [{"lastTransitionTime": "2024-06-11T12:12:51Z", "message": "recent recommendations were higher than current one, applying the highest recent recommendation", "reason": "ScaleDownStabilized", "status": "True", "type": "AbleToScale"}, {"lastTransitionTime": "2024-06-07T14:37:10Z", "message": "the HPA was able to successfully calculate a replica count from cpu resource utilization (percentage of request)", "reason": "ValidMetricFound", "status": "True", "type": "ScalingActive"}, {"lastTransitionTime": "2024-06-07T15:31:59Z", "message": "the desired count is within the acceptable range", "reason": "Desired<PERSON><PERSON><PERSON>R<PERSON><PERSON>", "status": "False", "type": "ScalingLimited"}], "currentMetrics": [{"resource": {"current": {"averageUtilization": 0, "averageValue": "3m"}, "name": "cpu"}, "type": "Resource"}], "currentReplicas": 1, "desiredReplicas": 1}}}