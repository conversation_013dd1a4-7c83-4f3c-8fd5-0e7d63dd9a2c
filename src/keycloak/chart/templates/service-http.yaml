# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: v1
kind: Service
metadata:
  name: {{ include "keycloak.fullname" . }}-http
  namespace: {{ .Release.Namespace }}  
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
    {{- range $key, $value := .Values.service.labels }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
    istio.io/use-waypoint: {{ include "keycloak.fullname" . }}-waypoint
    app.kubernetes.io/component: http
    # Enables "zarf connect keycloak"
    zarf.dev/connect-name: keycloak
  annotations:
    zarf.dev/connect-description: "Directly connect to the Keycloak HTTP service"    
spec:
  type: {{ .Values.service.type }}
  {{- if .Values.service.sessionAffinity }}
  sessionAffinity: {{ .Values.service.sessionAffinity }}
    {{- with .Values.service.sessionAffinityConfig }}
  sessionAffinityConfig:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}
  ports:
    - name: http
      port: 8080
      targetPort: http
      protocol: TCP
    - name: http-metrics
      port: 9000
      targetPort: metrics
      protocol: TCP
  selector:
    {{- include "keycloak.selectorLabels" . | nindent 4 }}
