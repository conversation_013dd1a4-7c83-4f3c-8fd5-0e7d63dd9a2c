# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

metadata:
  name: communications-terminated-after-inactivity-PLACEHOLDER
  uuid: 663f5e92-6db4-4042-8b5a-eba3ebe5a622
domain:
  type: kubernetes
  kubernetes-spec:
    resources: []
provider:
  type: opa
  opa-spec:
    rego: |
      package validate

      validate := false

      # Check on destination rule, outlier detection?
      # -> Doesn't appear that UDS is configured to create destination rules.
