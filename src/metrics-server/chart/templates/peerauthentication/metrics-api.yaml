# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

{{- if .Capabilities.APIVersions.Has "security.istio.io/v1beta1" }}
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: metrics-server-api-exception
  namespace: {{ .Release.Namespace }}
spec:
  mtls:
    mode: STRICT
  selector:
    matchLabels:
      app.kubernetes.io/name: metrics-server
  portLevelMtls:
    # Allow api service calls to operate permissive since ingress originates from the nodes
    "10250":
      mode: PERMISSIVE
{{- end }}
