apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: httpbin-service-entry
  namespace: curl-ns
  labels:
    istio.io/use-waypoint: egress-waypoint
    istio.io/use-waypoint-namespace: istio-egress
spec:
  location: MESH_EXTERNAL
  hosts:
  - httpbin.org
  ports: 
  - number: 443
    name: tls
    protocol: TLS
  resolution: DNS
  exportTo: ["."]
# ---
# apiVersion: networking.istio.io/v1
# kind: ServiceEntry
# metadata:
#   name: httpbin-service-entry
#   namespace: istio-egress
#   labels:
#     istio.io/use-waypoint: egress-waypoint
# spec:
#   location: MESH_EXTERNAL
#   hosts:
#   - httpbin.org
#   ports: 
#   - number: 443
#     name: tls
#     protocol: TLS
#   resolution: DNS
#   exportTo: ["."]
