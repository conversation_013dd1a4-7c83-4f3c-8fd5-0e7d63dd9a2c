# Add the gateway, service entry, destination rule, and virtual service
apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: wildcard-gateway
  namespace: wildcard-test
spec:
  selector:
    app: egressgateway
  servers:
  - port:
      number: 443
      name: tls
      protocol: TLS
    hosts:
      - "*.wikipedia.org"
    tls:
      mode: PASSTHROUGH
---
apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: wildcard-service-entry
  namespace: wildcard-test
spec:
  location: MESH_EXTERNAL
  hosts:
  - "www.wikipedia.org" # Needs to match "*.wikipedia.org" 
  ports: 
  - number: 443
    name: tls
    protocol: TLS
  resolution: DNS
---
apiVersion: networking.istio.io/v1
kind: DestinationRule
metadata:
  name: egressgateway-for-wildcard
  namespace: wildcard-test
spec:
  host: egressgateway.istio-egress-gateway.svc.cluster.local
  subsets:
    - name: wildcard
---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: wildcard-through-egress-gateway
  namespace: wildcard-test
spec:
  hosts:
  - "*.wikipedia.org"
  gateways:
  - wildcard-gateway
  - mesh
  tls:
  - match:
    - gateways:
      - mesh
      port: 443
      sniHosts:
      - "*.wikipedia.org"
    route:
    - destination:
        host: egressgateway.istio-egress-gateway.svc.cluster.local
        subset: wildcard
        port:
          number: 443
  - match:
    - gateways:
      - wildcard-gateway
      port: 443
      sniHosts:
      - "*.wikipedia.org"
    route:
    - destination:
        host: "www.wikipedia.org" # Match service entry
        port:
          number: 443
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-egress-gateway-access
  namespace: wildcard-test
spec:
  podSelector: {}
  policyTypes:
  - Egress
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: istio-egress-gateway
      podSelector:
        matchLabels:
          app: egressgateway
    ports:
    - protocol: TCP
      port: 443