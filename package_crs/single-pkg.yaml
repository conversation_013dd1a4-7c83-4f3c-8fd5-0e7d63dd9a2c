apiVersion: v1
kind: Namespace
metadata:
  name: ns-1
---
# Test 1 - Apply a Single Package
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-1
  namespace: ns-1
spec:
  network: 
    allow:
    - direction: Egress
      selector:
        app: curl1
      description: "Curl to example.com"
      remoteHost: "example.com"
      remoteProtocol: TLS
      port: 443
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: curl1
  namespace: ns-1
---
apiVersion: v1
kind: Service
metadata:
  name: curl1
  namespace: ns-1
  labels:
    app: curl1
    service: curl1
spec:
  ports:
  - port: 80
    name: http
  selector:
    app: curl1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl1
  namespace: ns-1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl1
  template:
    metadata:
      labels:
        app: curl1
    spec:
      terminationGracePeriodSeconds: 0
      serviceAccountName: curl1
      containers:
      - name: curl
        image: curlimages/curl
        command: ["/bin/sleep", "infinity"]
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - mountPath: /etc/curl/tls
          name: secret-volume
      volumes:
      - name: secret-volume
        secret:
          secretName: curl-secret
          optional: true
