# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

# action.yml
name: "Setup Environment"
description: "UDS Environment Setup"
inputs:
  ghToken:
    description: "GITHUB_TOKEN"
    required: true
  registry1Username:
    description: "IRON_BANK_ROBOT_USERNAME"
    required: true
  registry1Password:
    description: "IRON_BANK_ROBOT_PASSWORD"
    required: true
  rapidfortUsername:
    description: "RAPIDFORT_USERNAME"
    required: true
  rapidfortPassword:
    description: "RAPIDFORT_PASSWORD"
    required: true

runs:
  using: "composite"
  steps:
    - name: Use Node.js latest
      uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
      with:
        node-version: 24

    - name: Install k3d
      shell: bash
      # renovate: datasource=github-tags depName=k3d-io/k3d versioning=semver
      run: curl -s https://raw.githubusercontent.com/k3d-io/k3d/main/install.sh | TAG=v5.8.3 bash

    - name: Install UDS CLI
      uses: defenseunicorns/setup-uds@ab842abcad1f7a3305c2538e3dd1950d0daacfa5 # v1.0.1
      with:
        # renovate: datasource=github-tags depName=defenseunicorns/uds-cli versioning=semver
        version: v0.27.8

    - name: Iron Bank Login
      if: ${{ inputs.registry1Username != '' }}
      env:
        REGISTRY_USERNAME: ${{ inputs.registry1Username }}
        REGISTRY_PASSWORD: ${{ inputs.registry1Password }}
      run: echo "${{ env.REGISTRY_PASSWORD }}" | uds zarf tools registry login -u "${{ env.REGISTRY_USERNAME }}" --password-stdin registry1.dso.mil
      shell: bash

    - name: GHCR Login
      if: ${{ inputs.ghToken != '' }}
      env:
        GH_TOKEN: ${{ inputs.ghToken }}
      run: echo "${{ env.GH_TOKEN }}" | uds zarf tools registry login -u "dummy" --password-stdin ghcr.io
      shell: bash

    - name: Maru Login
      if: ${{ inputs.ghToken != '' }}
      run: |
        echo "MARU_AUTH=\"{\"raw.githubusercontent.com\": \"${{ inputs.ghToken }}\"}\"" >> "GITHUB_ENV"
      shell: bash

    - name: Rapidfort Login
      if: ${{ inputs.rapidfortUsername != '' }}
      shell: bash
      env:
        RAPIDFORT_USERNAME: ${{ inputs.rapidfortUsername }}
        RAPIDFORT_PASSWORD: ${{ inputs.rapidfortPassword }}
      run: echo "${{ env.RAPIDFORT_PASSWORD }}" | uds zarf tools registry login -u "${{ env.RAPIDFORT_USERNAME }}" --password-stdin quay.io
