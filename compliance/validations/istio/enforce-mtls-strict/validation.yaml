# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

metadata:
  name: enforce-mtls-strict
  uuid: ca49ac97-487a-446a-a0b7-92b20e2c83cb
domain:
  type: kubernetes
  kubernetes-spec:
    resources:
      - name: peerAuths
        resource-rule:
          group: security.istio.io
          version: v1beta1
          resource: peerauthentications
          namespaces: []
provider:
  type: opa
  opa-spec:
    rego: |
      package validate
      import rego.v1

      # Default values
      default validate := false
      default all_strict := false
      default msg := "Not evaluated"

      validate if {
        result_all_strict.result
      }

      msg = concat(" ", [result_all_strict.msg])

      # Evaluate if all PeerAuthentications have mtls mode set to STRICT
      peer_auths := {sprintf("%s/%s", [pa.metadata.namespace, pa.metadata.name]) | pa := input.peerAuths[_]}
      peer_auths_strict := {sprintf("%s/%s", [pa.metadata.namespace, pa.metadata.name]) | pa := input.peerAuths[_]; mtls_strict(pa)}

      result_all_strict = {"result": true, "msg": msg} if {
          peer_auths == peer_auths_strict
          msg := "All PeerAuthentications have mtls mode set to STRICT."
      } else = {"result": false, "msg": msg} if {
          msg := sprintf("Not all PeerAuthentications have mtls mode set to STRICT: %s.", [peer_auths - peer_auths_strict])
      }

      mtls_strict(pa) if {
        pa.spec.mtls.mode == "STRICT"
      }
    output:
      validation: validate.validate
      observations:
        - validate.msg
