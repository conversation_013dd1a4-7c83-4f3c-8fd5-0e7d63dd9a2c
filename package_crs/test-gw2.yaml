apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: test1
  namespace: istio-egress-gateway
spec:
  selector:
    app: egressgateway
  servers:
  - hosts:
    - foo.elastic.com
    port:
      name: tls
      number: 9200
      protocol: TLS
    tls:
      mode: PASSTHROUGH
---
apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: test2
  namespace: istio-egress-gateway
spec:
  selector:
    app: egressgateway
  servers:
  - hosts:
    - bar.elastic.com
    port:
      name: tls
      number: 9200
      protocol: TLS
    tls:
      mode: PASSTHROUGH
---
apiVersion: networking.istio.io/v1
kind: DestinationRule
metadata:
  name: egressgateway-desination-rule
  namespace: istio-egress-gateway
spec:
  host: egressgateway.istio-egress-gateway.svc.cluster.local
  subsets:
    - name: egressgateway
---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: test1-vs
  namespace: istio-egress-gateway
spec:
  hosts:
  - foo.elastic.com
  gateways:
  - test1
  - mesh
  tls:
  - match:
    - gateways:
      - mesh
      port: 9200
      sniHosts:
      - foo.elastic.com
    route:
    - destination:
        host: egressgateway.istio-egress-gateway.svc.cluster.local
        subset: egressgateway
        port:
          number: 9200
  - match:
    - gateways:
      - egressgateway
      port: 9200
      sniHosts:
      - foo.elastic.com
    route:
    - destination:
        host: foo.elastic.com
        port:
          number: 9200
---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: test2-vs
  namespace: istio-egress-gateway
spec:
  hosts:
  - bar.elastic.com
  gateways:
  - test2
  - mesh
  tls:
  - match:
    - gateways:
      - mesh
      port: 9200
      sniHosts:
      - bar.elastic.com
    route:
    - destination:
        host: egressgateway.istio-egress-gateway.svc.cluster.local
        subset: egressgateway
        port:
          number: 9200
  - match:
    - gateways:
      - egressgateway
      port: 9200
      sniHosts:
      - bar.elastic.com
    route:
    - destination:
        host: bar.elastic.com
        port:
          number: 9200