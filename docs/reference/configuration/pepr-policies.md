---
title: Pepr Policies
---

## Common Pepr Policies for UDS Core

### Pepr Policy Exemptions
These policies are based on the [Big Bang](https://p1.dso.mil/services/big-bang) policies created with Kyverno. You can find the source policies [here](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies), Policy Names below also have links to the referenced Big Bang policy.

Exemptions can be specified by a [UDS Exemption CR](/reference/configuration/uds-operator/exemption/#exemption). These take the place of Kyverno Exceptions.

If a resource is exempted, it will be annotated as `uds-core.pepr.dev/uds-core-policies.<POLICY>: exempted`

### Pepr Policy Mutations

:::note
Mutations can be exempted using the same [Pepr Policy Exemptions](#pepr-policy-exemptions) references as the validations.
:::

| Pepr Mutation🔗 | Mutated Fields | Mutation Logic |
| --------------- | -------------- | -------------- |
| [Disallow Privilege Escalation](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/security.ts#L14-L75) | `containers[].securityContext.allowPrivilegeEscalation` | Mutates `allowPrivilegeEscalation` to `false` if undefined, unless the container is privileged or `CAP_SYS_ADMIN` is added. |
| [Require Non-root User](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/security.ts#L77-L167) | `securityContext.runAsUser`,<br> `securityContext.runAsGroup`,<br> `securityContext.fsGroup`,<br> `securityContext.runAsNonRoot` | Pods are mutated to ensure workloads do not run as root, mutating `runAsNonRoot: true`. Users can define user, group, and fsGroup IDs to run the pod as by using the `uds/user`, `uds/group`, `uds/fsgroup` pod labels. If not provided these default to `runAsUser: 1000` and `runAsGroup: 1000`. |
| [Drop All Capabilities](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/security.ts#L329-L376) | `containers[].securityContext.capabilities.drop` | Ensures all capabilities are dropped by setting `capabilities.drop` to `["ALL"]` for all containers. |

### Pepr Policy Validations

| Policy Name🔗 | Exemption Reference🔗 | Policy Description |
| ------------- | :-------------------: | ------------------ |
| [Disallow Host Namespaces](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/disallow-host-namespaces.yaml) | [`DisallowHostNamespaces`](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/networking.ts#L7-L35) | Implemented: ✅ <br> Subject: **Pod** <br> Severity: **high** <br><br> Host namespaces (Process ID namespace, Inter-Process Communication namespace, and network namespace) allow access to shared information and can be used to elevate privileges. Pods should not be allowed access to host namespaces. This policy ensures fields which make use of these host namespaces are set to `false`. |
|[Disallow NodePort Services](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/disallow-nodeport-services.yaml) | [`DisallowNodePortServices`](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/networking.ts#L88-L110) | Implemented: ✅ <br> Subject: **Service** <br> Severity: **medium** <br><br> A Kubernetes Service of type NodePort uses a host port to receive traffic from any source. A NetworkPolicy cannot be used to control traffic to host ports. Although NodePort Services can be useful, their use must be limited to Services with additional upstream security checks. This policy validates that any new Services do not use the `NodePort` type. |
|Disallow Privileged [Escalation](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/disallow-privilege-escalation.yaml) and [Pods](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/disallow-privileged-containers.yaml) | [`DisallowPrivileged`](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/security.ts#L14-L75) | Implemented: ✅ <br> Subject: **Pod** <br> Severity: **high** <br><br> Privilege escalation, such as via set-user-ID or set-group-ID file mode, should not be allowed. Privileged mode also disables most security mechanisms and must not be allowed. This policy ensures the `allowPrivilegeEscalation` field is set to false and `privileged` is set to false or undefined. |
|[Disallow SELinux Options](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/disallow-selinux-options.yaml) | [`DisallowSELinuxOptions`](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/security.ts#L244-L285) | Implemented: ✅ <br> Subject: **Pod** <br> Severity: **high** <br><br> SELinux options can be used to escalate privileges. This policy ensures that the `seLinuxOptions` specified are not used. |
|[Drop All Capabilities](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/require-drop-all-capabilities.yaml) | [`DropAllCapabilities`](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/security.ts#L329-L376) | Implemented: ✅ <br> Subject: **Pod** <br> Severity: **medium** <br><br> Capabilities permit privileged actions without giving full root access. All capabilities should be dropped from a Pod, with only those required added back. This policy ensures that all containers explicitly specify `drop: ["ALL"]`. |
|[Require Non-root User](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/require-non-root-user.yaml) | [`RequireNonRootUser`](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/security.ts#L77-L167) | Implemented: ✅ <br> Subject: **Pod** <br> Severity: **high** <br><br> Following the least privilege principle, containers should not be run as root. This policy ensures containers either have `runAsNonRoot` set to `true` or `runAsUser` > 0. |
|[Restrict Capabilities](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/restrict-capabilities.yaml) | [`RestrictCapabilities`](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/security.ts#L378-L413) | Implemented: ✅ <br> Subject: **Pod** <br> Severity: **high** <br><br> Capabilities permit privileged actions without giving full root access. Adding capabilities beyond the default set must not be allowed. This policy ensures users cannot add additional capabilities beyond the allowed list to a Pod. |
|[Restrict External Names (CVE-2020-8554)](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/restrict-external-names.yaml) | [`RestrictExternalNames`](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/networking.ts#L67-L86) | Implemented: ✅ <br> Subject: **Service** <br> Severity: **medium** <br><br> Service external names can be used for a MITM attack (CVE-2020-8554). External names can be used by an attacker to point back to localhost or internal IP addresses for exploitation. This policy restricts services using external names to a specified list. |
|[Restrict hostPath Volume Writable Paths](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/restrict-host-path-write.yaml) | [`RestrictHostPathWrite`](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/storage.ts#L54-L92) | Implemented: ✅ <br> Subject: **Pod** <br> Severity: **medium** <br><br> hostPath volumes consume the underlying node's file system. If hostPath volumes are not universally disabled, they should be required to be read-only. Pods which are allowed to mount hostPath volumes in read/write mode pose a security risk even if confined to a "safe" file system on the host and may escape those confines. This policy checks containers for hostPath volumes and validates they are explicitly mounted in readOnly mode. |
|[Restrict Host Ports](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/restrict-host-ports.yaml) | [`RestrictHostPorts`](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/networking.ts#L37-L65) | Implemented: ✅ <br> Subject: **Pod** <br> Severity: **high** <br><br> Access to host ports allows potential snooping of network traffic and should not be allowed, or at minimum restricted to a known list. This policy ensures only approved ports are defined in container's `hostPort` field. |
|[Restrict Proc Mount](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/restrict-proc-mount.yaml) | [`RestrictProcMount`](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/security.ts#L169-L198) | Implemented: ✅ <br> Subject: **Pod** <br> Severity: **high** <br><br> The default /proc masks are set up to reduce the attack surface. This policy ensures nothing but the specified procMount can be used. By default only "Default" is allowed. |
|[Restrict Seccomp](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/restrict-seccomp.yaml) | [`RestrictSeccomp`](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/security.ts#L200-L242) | Implemented: ✅ <br> Subject: **Pod** <br> Severity: **high** <br><br> The SecComp profile should not be explicitly set to Unconfined. This policy, requiring Kubernetes v1.19 or later, ensures that the `seccompProfile.Type` is undefined or restricted to the values in the allowed list. By default, this is `RuntimeDefault` or `Localhost`. |
|[Restrict SELinux Type](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/restrict-selinux-type.yaml) | [`RestrictSELinuxType`](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/security.ts#L287-L327) | Implemented: ✅ <br> Subject: **Pod** <br> Severity: **high** <br><br> SELinux options can be used to escalate privileges. This policy ensures that the `seLinuxOptions` type field is undefined or restricted to the allowed list. |
|[Restrict Volume Types](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/restrict-volume-types.yaml) | [`RestrictVolumeTypes`](https://github.com/defenseunicorns/uds-core/blob/v0.27.0/src/pepr/policies/storage.ts#L7-L52) | Implemented: ✅ <br> Subject: **Pod** <br> Severity: **medium** <br><br> Volume types, beyond the core set, should be restricted to limit exposure to potential vulnerabilities in Container Storage Interface (CSI) drivers. In addition, HostPath volumes should not be. |
|[Restrict Sysctls](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/restrict-sysctls.yaml) | Not Implemented | Implemented: ❌ <br> Subject: **Pod** <br> Severity: **high** <br><br> Sysctl can disable security mechanisms or affect all containers on a host, and should be restricted to an allowed "safe" subset. A sysctl is considered safe if it is namespaced and is isolated from other Pods and processes on the same Node. This policy ensures that all sysctls are in the allowed list.
|[Restrict Image Registries](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/restrict-image-registries.yaml) | Not Implemented | Implemented: ❌ <br> Subject: **Pod** <br> Severity: **high** <br><br> Images from unknown, public registries can be of dubious quality and may not be scanned and secured, representing a high degree of risk. Requiring use of known, approved registries helps reduce threat exposure by ensuring image pulls only come from them. This policy validates that all images originate from a registry in the approved list.|
|[Restrict hostPath Volume PV Paths](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/restrict-host-path-mount-pv.yaml) | Not Implemented | Implemented: ❌ <br> Subject: **PersistentVolume** <br> Severity: **medium** <br><br> PersistentVolume using hostPath consume the underlying node's file system. If not universally disabled, they should be restricted to specific host paths to prevent access to sensitive information. This policy ensures that PV hostPath is in the allowed list. |
|[Restrict hostPath Volume Mountable Paths](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/restrict-host-path-mount.yaml) | Not Implemented | Implemented: ❌ <br> Subject: **Pod** <br> Severity: **medium** <br><br>  hostPath volumes consume the underlying node's file system. If hostPath volumes are not universally disabled, they should be restricted to specific host paths to prevent access to sensitive information. This policy ensures that hostPath volume paths are in the allowed list. |
|[Restrict External IPs (CVE-2020-8554)](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/restrict-external-ips.yaml) | Not Implemented | Implemented: ❌ <br> Subject: **Service** <br> Severity: **medium** <br><br>  Service externalIPs can be used for a MITM attack (CVE-2020-8554). This policy restricts externalIPs to a specified list. |
|[Restrict AppArmor Profile](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/restrict-apparmor.yaml) | Not Implemented | Implemented: ❌ <br> Subject: **Pod** <br> Severity: **high** <br><br> On hosts using Debian Linux distros, AppArmor is used as an access control framework. AppArmor uses the 'runtime/default' profile by default. This policy ensures Pods do not override the AppArmor profile with values outside of the allowed list. |
|[Require Image Signature](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/require-image-signature.yaml) | Not Implemented | Implemented: ❌ <br> Subject: **Pod** <br> Severity: **high** <br><br> Using the Cosign project, OCI images may be signed to ensure supply chain security is maintained. Those signatures can be verified before pulling into a cluster. This policy checks the signature to ensure it has been signed by verifying its signature against the public key. |
|[Require Non-root Group](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/require-non-root-group.yaml) | Not Implemented | Implemented: ❌ <br> Subject: **Pod** <br> Severity: **high** <br><br> Following the least privilege principle, access to the root group ID should be forbidden in containers. This policy ensures containers are running with groups > 0. |
|[Disallow AutoMount Service Account Tokens](https://repo1.dso.mil/big-bang/product/packages/kyverno-policies/-/blob/main/chart/templates/disallow-auto-mount-service-account-token.yaml) | Not Implemented | Implemented: ❌ <br> Subject: **Pod, ServiceAccount** <br> Severity: **high** <br><br> Auto-mounting of Kubernetes API credentials is not ideal in all circumstances. This policy finds Pods and Service Accounts that automount kubernetes api credentials. |
