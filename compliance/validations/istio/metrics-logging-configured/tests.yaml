# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

pass:
  - test: default
    validation: validation.yaml
    resources: resources.json
    expected-validation: true
  - test: change_enablePrometheusMerge_to_false
    validation: validation.yaml
    resources: resources.json
    permutation: ".istioConfig.enablePrometheusMerge = false"
    expected-validation: false
  - test: change_enablePrometheusMerge_to_false
    validation: validation.yaml
    resources: resources.json
    permutation: "del(.istioConfig.enablePrometheusMerge)"
    expected-validation: true
