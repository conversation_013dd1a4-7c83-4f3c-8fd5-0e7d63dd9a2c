# Add the gateway, service entry, destination rule, and virtual service
apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: different-host-same-ns-gateway-1
  namespace: different-host-same-ns-test
spec:
  selector:
    app: egressgateway
  servers:
  - port:
      number: 443
      name: tls
      protocol: TLS
    hosts:
      - httpbin.org
    tls:
      mode: PASSTHROUGH
---
apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: different-host-same-ns-service-entry-1
  namespace: different-host-same-ns-test
spec:
  location: MESH_EXTERNAL
  hosts:
  - httpbin.org
  ports: 
  - number: 443
    name: tls
    protocol: TLS
  resolution: DNS
---
apiVersion: networking.istio.io/v1
kind: DestinationRule
metadata:
  name: egressgateway-for-different-host-same-ns-1
  namespace: different-host-same-ns-test
spec:
  host: egressgateway.istio-egress-gateway.svc.cluster.local
  subsets:
    - name: different-host-same-ns-1
---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: different-host-same-ns-through-egress-gateway-1
  namespace: different-host-same-ns-test
spec:
  hosts:
  - httpbin.org
  gateways:
  - different-host-same-ns-gateway-1
  - mesh
  tls:
  - match:
    - gateways:
      - mesh
      port: 443
      sniHosts:
      - httpbin.org
    route:
    - destination:
        host: egressgateway.istio-egress-gateway.svc.cluster.local
        subset: different-host-same-ns-1
        port:
          number: 443
  - match:
    - gateways:
      - different-host-same-ns-gateway-1
      port: 443
      sniHosts:
      - httpbin.org
    route:
    - destination:
        host: httpbin.org
        port:
          number: 443
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-egress-gateway-access-1
  namespace: different-host-same-ns-test
spec:
  podSelector: 
    matchLabels:
      app: curl1
  policyTypes:
  - Egress
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: istio-egress-gateway
      podSelector:
        matchLabels:
          app: egressgateway
    ports:
    - protocol: TCP
      port: 443