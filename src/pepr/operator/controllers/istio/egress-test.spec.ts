// Testing to mock applySidecarEgressResources properly

import { kind } from "pepr";
import {
  afterEach,
  beforeEach,
  describe,
  expect,
  it,
  Mock,
  MockedFunction,
  test,
  vi,
} from "vitest";
import {HostResourceMap, PackageAction} from "./types";
import { IstioState } from "./namespace";
import { RemoteProtocol } from "../../crd";
import { defaultEgressMocks, updateEgressMocks } from "./defaultTestMocks";
import { purgeOrphans } from "../utils";
// import * as egressModule from "./egress";

const mockPurgeOrphans: MockedFunction<() => Promise<void>> = vi.fn();
vi.mock("../utils", async () => {
  const originalModule = (await vi.importActual("../utils")) as object;
  return {
    ...originalModule,
    purgeOrphans: vi.fn(async <T>(fn: () => Promise<T>) => fn()),
  };
});

// Mock pepr functions
vi.mock("pepr", () => ({
  K8s: vi.fn(),
  Log: {
    child: vi.fn(() => ({
      info: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      level: "info",
    })),
  },
  kind: {
    Gateway: "Gateway",
    VirtualService: "VirtualService",
    ServiceEntry: "ServiceEntry",
    Namespace: "Namespace",
    Service: "Service",
    ServiceAccount: "ServiceAccount",
    Waypoint: "Waypoint",
  },
}));

// Mock the applyStuff function
vi.mock('./egress-sidecar-test.ts', async () => {
  const actual = await vi.importActual('./egress')
  return {
    ...actual,
    applySidecarEgressResources: vi.fn(),
  }
})
import { applySidecarEgressResources } from "./egress-sidecar-test";
import { inMemoryPackageMap, reconcileSharedEgressResources, applyAmbientEgressResources } from "./egress-test";

describe("test reconcileSharedEgressResources", () => {
  const hostResourceMapMock: HostResourceMap = {
    "example.com": {
      portProtocol: [{ port: 443, protocol: RemoteProtocol.TLS }],
    },
  };
  const packageIdMock = "test-package-test-namespace";

  beforeEach(async () => {
    process.env.PEPR_WATCH_MODE = "true";
    vi.useFakeTimers();
    vi.clearAllMocks();
    // Reset the map before each test
    for (const key in inMemoryPackageMap) {
      delete inMemoryPackageMap[key];
    }

    (purgeOrphans as Mock).mockImplementation(mockPurgeOrphans);
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
    vi.clearAllMocks();
  });

  it("should create egress resources on action AddOrUpdate, sidecar", async () => {    
    updateEgressMocks(defaultEgressMocks);

    await reconcileSharedEgressResources(
      hostResourceMapMock,
      packageIdMock,
      PackageAction.AddOrUpdate,
      IstioState.Sidecar,
    );

    // Test that purgeOrphans is called
    expect(purgeOrphans).toHaveBeenCalledTimes(4);


    // Check sidecar apply function is called
    expect(applySidecarEgressResources).toHaveBeenCalledTimes(1);

    // Check ambient apply function is not called
    expect(applyAmbientEgressResources).not.toHaveBeenCalled();

    // Validate inMemoryPackageMap
    expect(inMemoryPackageMap).toEqual({ "test-package-test-namespace": hostResourceMapMock });
  });
});