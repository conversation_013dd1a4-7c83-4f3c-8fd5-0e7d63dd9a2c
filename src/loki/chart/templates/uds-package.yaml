# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: loki
  namespace: {{ .Release.Namespace }}
spec:
  network:
    serviceMesh:
      mode: ambient
    allow:
      # Permit intra-namespace communication for gateway -> loki read/write
      - direction: Ingress
        remoteGenerated: IntraNamespace

      - direction: Egress
        remoteGenerated: IntraNamespace

      - direction: Ingress
        selector:
          app.kubernetes.io/name: loki
        remoteNamespace: grafana
        remoteSelector:
          app.kubernetes.io/name: grafana
        remoteServiceAccount: grafana
        ports:
          - 8080
        description: "Grafana Log Queries"

      - direction: Ingress
        selector:
          app.kubernetes.io/name: loki
        remoteNamespace: monitoring
        remoteSelector:
          app.kubernetes.io/name: prometheus
        remoteServiceAccount: kube-prometheus-stack-prometheus
        ports:
          - 3100
        description: "Prometheus Metrics"

      - direction: Ingress
        selector:
          app.kubernetes.io/name: loki
        remoteNamespace: vector
        remoteSelector:
          app.kubernetes.io/name: vector
        remoteServiceAccount: vector
        ports:
          - 8080
        description: "Vector Log Storage"

      # Egress for S3 connections
      - direction: Egress
        selector:
          app.kubernetes.io/name: loki
        description: Storage
        {{- if .Values.storage.internal.enabled }}
        remoteSelector:
          {{- .Values.storage.internal.remoteSelector | toYaml | nindent 10 }}
        remoteNamespace: {{ .Values.storage.internal.remoteNamespace }}
        {{- else if .Values.storage.egressCidr }}
        remoteCidr: {{ .Values.storage.egressCidr }}
        {{- else }}
        remoteGenerated: Anywhere
        {{- end }}

      # Custom rules for additional networking access
      {{- with .Values.additionalNetworkAllow }}
      {{ toYaml . | nindent 6 }}
      {{- end }}
