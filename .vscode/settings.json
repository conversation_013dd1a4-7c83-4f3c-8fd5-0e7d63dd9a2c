{
  "debug.javascript.terminalOptions": {
    "enableTurboSourcemaps": true,
    "resolveSourceMapLocations": [
      "${workspaceFolder}/**",
      "node_modules/kubernetes-fluent-client/**",
      "node_modules/pepr/**"
    ]
  },
  "yaml.schemas": {
    // renovate: datasource=github-tags depName=defenseunicorns/uds-cli versioning=semver
    "https://raw.githubusercontent.com/defenseunicorns/uds-cli/v0.27.8/uds.schema.json": [
      "uds-bundle.yaml"
    ],
    // renovate: datasource=github-tags depName=defenseunicorns/uds-cli versioning=semver
    "https://raw.githubusercontent.com/defenseunicorns/uds-cli/v0.27.8/tasks.schema.json": [
      "tasks.yaml",
      "tasks/**/*.yaml",
      "src/**/validate.yaml"
    ],
    // renovate: datasource=github-tags depName=defenseunicorns/uds-cli versioning=semver
    "https://raw.githubusercontent.com/defenseunicorns/uds-cli/v0.27.8/zarf.schema.json": [
      "zarf.yaml"
    ],
    "https://raw.githubusercontent.com/defenseunicorns/uds-core/refs/heads/main/schemas/package-v1alpha1.schema.json": [
      "**/uds-package.yaml",
      "**/package.yaml"
    ],
    "https://raw.githubusercontent.com/defenseunicorns/uds-core/refs/heads/main/schemas/exemption-v1alpha1.schema.json": [
      "**/uds-exemption.yaml",
      "**/exemptions.yaml",
      "**/exemption.yaml"
    ],
  },
  "cSpell.words": [
    "alertmanager",
    "Authservice",
    "automount",
    "controlplane",
    "crds",
    "distros",
    "ironbank",
    "Kyverno",
    "MITM",
    "neuvector",
    "opensource",
    "Quarkus",
    "Quickstart",
    "seccomp",
    "Sysctls",
    "Velero"
  ],
  "cSpell.enabled": true,
  "[typescript]": {
    "editor.codeActionsOnSave": {
      "source.organizeImports": "always"
    }
  },
}
