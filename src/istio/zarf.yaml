# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: uds-core-istio
  description: "UDS Core Istio package deploys Istio with admin, tenant and passthrough gateways."
  url: https://istio.io/latest/

variables:
  - name: DOMAIN
    description: "Cluster domain"
    default: "uds.dev"

  - name: ADMIN_DOMAIN
    description: "Domain for admin services, defaults to `admin.DOMAIN`"

components:
  - name: istio-controlplane
    required: true
    only:
      flavor: upstream
    import:
      path: common
    charts:
      - name: istiod
        valuesFiles:
          - "values/upstream/istiod.yaml"
      - name: cni
        valuesFiles:
          - "values/upstream/cni.yaml"
      - name: ztunnel
        valuesFiles:
          - "values/upstream/ztunnel.yaml"
    images:
      - "docker.io/istio/pilot:1.26.2-distroless"
      - "docker.io/istio/proxyv2:1.26.2-distroless"
      - "docker.io/istio/install-cni:1.26.2-distroless"
      - "docker.io/istio/ztunnel:1.26.2-distroless"

  - name: istio-controlplane
    required: true
    only:
      flavor: registry1
    import:
      path: common
    charts:
      - name: istiod
        valuesFiles:
          - "values/registry1/istiod.yaml"
      - name: cni
        valuesFiles:
          - "values/registry1/cni.yaml"
      - name: ztunnel
        valuesFiles:
          - "values/registry1/ztunnel.yaml"
    images:
      - registry1.dso.mil/ironbank/tetrate/istio/proxyv2:1.26.2-fips
      - registry1.dso.mil/ironbank/tetrate/istio/pilot:1.26.2-fips
      - registry1.dso.mil/ironbank/tetrate/istio/install-cni:1.26.2-fips
      - registry1.dso.mil/ironbank/tetrate/istio/ztunnel:1.26.2-fips

  - name: istio-controlplane
    required: true
    only:
      flavor: unicorn
    import:
      path: common
    charts:
      - name: istiod
        valuesFiles:
          - "values/unicorn/istiod.yaml"
      - name: cni
        valuesFiles:
          - "values/unicorn/cni.yaml"
      - name: ztunnel
        valuesFiles:
          - "values/unicorn/ztunnel.yaml"
    images:
      - quay.io/rfcurated/istio/pilot:1.26.2-jammy-fips-rfcurated-rfhardened
      - quay.io/rfcurated/istio/proxyv2:1.26.2-jammy-fips-rfcurated-rfhardened
      - quay.io/rfcurated/istio/install-cni:1.26.2-jammy-fips-rfcurated-rfhardened
      - quay.io/rfcurated/istio/ztunnel:1.26.2-jammy-scratch-fips-rfcurated

  - name: gateway-api-crds
    required: true
    import:
      path: common

  - name: istio-admin-gateway
    required: true
    charts:
      - name: gateway
        url: https://istio-release.storage.googleapis.com/charts
        version: 1.26.2
        releaseName: admin-ingressgateway
        namespace: istio-admin-gateway
      - name: uds-istio-config
        version: 0.2.0
        localPath: charts/uds-istio-config
        namespace: istio-admin-gateway
        valuesFiles:
          - "values/config-admin.yaml"

  - name: istio-tenant-gateway
    required: true
    charts:
      - name: gateway
        url: https://istio-release.storage.googleapis.com/charts
        version: 1.26.2
        releaseName: tenant-ingressgateway
        namespace: istio-tenant-gateway
      - name: uds-istio-config
        version: 0.2.0
        localPath: charts/uds-istio-config
        namespace: istio-tenant-gateway
        valuesFiles:
          - "values/config-tenant.yaml"

  - name: istio-egress-waypoint
    required: true
    charts:
      - name: uds-istio-egress-config
        version: 0.1.0
        localPath: charts/uds-istio-egress-config
        namespace: istio-egress-waypoint

  - name: istio-passthrough-gateway
    required: false
    charts:
      - name: gateway
        url: https://istio-release.storage.googleapis.com/charts
        version: 1.26.2
        releaseName: passthrough-ingressgateway
        namespace: istio-passthrough-gateway
      - name: uds-istio-config
        version: 0.2.0
        localPath: charts/uds-istio-config
        namespace: istio-passthrough-gateway
        valuesFiles:
          - "values/config-passthrough.yaml"

  - name: istio-egress-gateway
    required: false
    charts:
      - name: gateway
        url: https://istio-release.storage.googleapis.com/charts
        version: 1.26.2
        releaseName: egressgateway
        namespace: istio-egress-gateway
        valuesFiles:
          - "values/base-egress.yaml"
