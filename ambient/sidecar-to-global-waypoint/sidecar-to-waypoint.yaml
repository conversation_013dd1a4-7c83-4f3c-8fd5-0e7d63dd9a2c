# Need: 2 service entries (in each ns) + virtual service?

apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: egress-vs-httpbin-org
  namespace: istio-egress
spec:
  hosts:
  - httpbin.org
  tls:
  - match:
    - gateways:
      - mesh
      port: 443
      sniHosts:
      - httpbin.org
    route:
    - destination:
        host: egressgateway.istio-egress-gateway.svc.cluster.local
        port:
          number: 443
  - match:
    - gateways:
      - gateway-httpbin-org
      port: 443
      sniHosts:
      - httpbin.org
    route:
    - destination:
        host: httpbin.org
        port:
          number: 443