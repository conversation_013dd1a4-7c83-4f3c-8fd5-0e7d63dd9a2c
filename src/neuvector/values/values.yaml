# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

leastPrivilege: true
autoGenerateCert: false
rbac: true
manager:
  env:
    ssl: false
  svc:
    type: ClusterIP
  probes:
    enabled: true

internal:
  autoRotateCert: true

controller:
  apisvc:
    type: ClusterIP
  configmap:
    enabled: true
    data:
      sysinitcfg.yaml: |
        always_reload: true
        no_telemetry_report: true
        scan_config:
          auto_scan: true
        mode_auto_d2m: true
        mode_auto_d2m_duration: 129600
  secret:
    enabled: false
  env:
    - name: NO_DEFAULT_ADMIN
      value: "1"
  internal:
    certificate:
      secret: neuvector-internal-cert

enforcer:
  internal:
    certificate:
      secret: neuvector-internal-cert

cve:
  updater:
    enabled: true
  scanner:
    internal:
      certificate:
        secret: neuvector-internal-cert

crdwebhook:
  enabled: false
  type: ClusterIP
