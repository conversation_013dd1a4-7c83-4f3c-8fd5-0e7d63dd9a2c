# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

component-definition:
  components:
    - control-implementations:
        - description: Control Implementation Description
          implemented-requirements:
            - control-id: ac-14
              description: Istio implements with service to service and provides authorization policies that require authentication to access any non-public features.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that <PERSON>ti<PERSON> is healthy
                - href: file:../../compliance/validations/istio/authorized-keycloak-access/validation.yaml
                  rel: lula
                  text: Validates that <PERSON><PERSON><PERSON> is used to authorize access to Keycloak admin console only from admin gateway
              remarks: |
                ASSESSMENT-OBJECTIVE:
                AC-14a.  [Assignment: organization-defined user actions] that can be performed on the system without identification or authentication consistent with organizational mission and business functions are identified;
                AC-14b.
                	AC-14b.[01] user actions not requiring identification or authentication are documented in the security plan for the system;
                	AC-14b.[02] a rationale for user actions not requiring identification or authentication is provided in the security plan for the system.
              uuid: 41c51dc3-7db1-4717-b071-83e57897f478
            - control-id: ac-4
              description: Istio encrypts all in-mesh communication at runtime using FIPS verified mTLS in addition to ingress and egress gateways for controlling communication.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/enforce-mtls-strict/validation.yaml
                  rel: lula
                  text: Check that Istio is enforcing mtls STRICT
                - href: file:../../compliance/validations/istio/all-pods-istio-injected/validation.yaml
                  rel: lula
                  text: All pods are istio injected with proxyv2 sidecar
                - href: file:../../compliance/validations/istio/ingress-traffic-encrypted/validation.yaml
                  rel: lula
                  text: Check ingress traffic is encrypted
              remarks: |-
                ASSESSMENT-OBJECTIVE:
                approved authorizations are enforced for controlling the flow of information within the system and between connected systems based on [Assignment: organization-defined information flow control policies].
              uuid: 210f730b-7fed-42dd-99b4-42466951b080
            - control-id: ac-4.21
              description: Istio is configured to use ingress and egress gateways to provide logical flow separation.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/all-namespaces-istio-injected/validation.yaml
                  rel: lula
                  text: Check namespaces are istio injected
                - href: file:../../compliance/validations/istio/check-istio-admin-gateway-and-usage/validation.yaml
                  rel: lula
                  text: Check that Istio is configured with an admin gateway and admin services use it
                - href: file:../../compliance/validations/istio/gateway-configuration-check/validation.yaml
                  rel: lula
                  text: Validates that Istio Gateways are available and expected VirtualServices using each Gateway.
              remarks: |
                ASSESSMENT-OBJECTIVE:
                AC-04(21)[01] information flows are separated logically using [Assignment: organization-defined mechanisms and/or techniques] to accomplish [Assignment: organization-defined required separations];
                AC-04(21)[02] information flows are separated physically using [Assignment: organization-defined mechanisms and/or techniques] to accomplish [Assignment: organization-defined required separations].
              uuid: ee9e5fae-1c95-46c7-9265-dc0035e2bb05
            - control-id: ac-6.9
              description: Istio produces logs for all traffic in the information system.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/check-istio-logging-all-traffic/validation.yaml
                  rel: lula
                  text: Check that Istio is logging all traffic which could contain privileged function calls
                - href: file:../../compliance/validations/istio/all-pods-istio-injected/validation.yaml
                  rel: lula
                  text: All pods are istio injected with proxyv2 sidecar
              remarks: |-
                ASSESSMENT-OBJECTIVE:
                the execution of privileged functions is logged.
              uuid: c6d9abd2-0136-468a-908d-181d9bd51962
            - control-id: au-12
              description: Istio provides audit record generation capabilities for a variety of event types, including session, connection, transaction, or activity durations, and the number of bytes received and sent.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/check-istio-logging-all-traffic/validation.yaml
                  rel: lula
                  text: Check that Istio is logging all traffic which could contain audit events
              remarks: |
                ASSESSMENT-OBJECTIVE:
                AU-12a. audit record generation capability for the event types the system is capable of auditing (defined in AU-02_ODP[01]) is provided by [Assignment: organization-defined system components];
                AU-12b.  [Assignment: organization-defined personnel or roles] is/are allowed to select the event types that are to be logged by specific components of the system;
                AU-12c. audit records for the event types defined in AU-02_ODP[02] that include the audit record content defined in AU-03 are generated.
              uuid: 87f99f34-6980-49e1-91cf-c0264fa3407c
            - control-id: au-2
              description: Istio logs all Istio event logs within the system's mesh network.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/check-istio-logging-all-traffic/validation.yaml
                  rel: lula
                  text: Check that Istio is logging all traffic which could contain audit events
              remarks: |
                ASSESSMENT-OBJECTIVE:
                AU-02a.  [Assignment: organization-defined event types] that the system is capable of logging are identified in support of the audit logging function;
                AU-02b. the event logging function is coordinated with other organizational entities requiring audit-related information to guide and inform the selection criteria for events to be logged;
                AU-02c.
                	AU-02c.[01]  [Assignment: organization-defined event types (subset of AU-02_ODP[01])] are specified for logging within the system;
                	AU-02c.[02] the specified event types are logged within the system [Assignment: organization-defined frequency or situation];
                AU-02d. a rationale is provided for why the event types selected for logging are deemed to be adequate to support after-the-fact investigations of incidents;
                AU-02e. the event types selected for logging are reviewed and updated [Assignment: organization-defined frequency].
              uuid: b3ed3dba-3164-4785-98db-ef22c96c7c62
            - control-id: au-3
              description: Istio logs all Istio event logs within the system's mesh network.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/tracing-logging-support/validation.yaml
                  rel: lula
                  text: Check that Istio is configured to provide tracing data
              remarks: |
                ASSESSMENT-OBJECTIVE:
                AU-03a. audit records contain information that establishes what type of event occurred;
                AU-03b. audit records contain information that establishes when the event occurred;
                AU-03c. audit records contain information that establishes where the event occurred;
                AU-03d. audit records contain information that establishes the source of the event;
                AU-03e. audit records contain information that establishes the outcome of the event;
                AU-03f. audit records contain information that establishes the identity of any individuals, subjects, or objects/entities associated with the event.
              uuid: 79dee0b0-5848-4b1e-826b-a2e4ec567b90
            - control-id: au-3.1
              description: Istio has been configured to implement event logging within our environment. This includes capturing metrics related to the duration of sessions, connections, transactions, or activities. Specifically, Istio's telemetry features are utilized to capture these metrics, which provide valuable data that can be used to infer the duration of sessions or connections.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/metrics-logging-configured/validation.yaml
                  rel: lula
                  text: Check that Istio is configured to provide metrics data
                - href: file:../../compliance/validations/istio/all-pods-istio-injected/validation.yaml
                  rel: lula
                  text: All pods are istio injected with proxyv2 sidecar
                - href: file:../../compliance/validations/istio/prometheus-annotations-validation/validation.yaml
                  rel: lula
                  text: Check that pods running sidecar have the correct annotations for prometheus metrics scrape
              remarks: |-
                ASSESSMENT-OBJECTIVE:
                generated audit records contain the following [Assignment: organization-defined additional information].
              uuid: b855fff0-5f57-4ea0-b9a7-52973e81784d
            - control-id: cm-5
              description: Istio enforces logical access restrictions associated with changes to the system. Istio's Role-Based Access Control (RBAC) features are used to define and enforce access controls, ensuring that only approved personnel can make changes to the system.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/rbac-enforcement-check/validation.yaml
                  rel: lula
                  text: Check that Istio is enforcing RBAC
                - href: file:../../compliance/validations/istio/rbac-for-approved-personnel/validation.yaml
                  rel: lula
                  text: Check that particular RBAC is ensuring only approved personnel can make changes to the system [PLACEHOLDER]
              remarks: |
                ASSESSMENT-OBJECTIVE:
                CM-05[01] physical access restrictions associated with changes to the system are defined and documented;
                CM-05[02] physical access restrictions associated with changes to the system are approved;
                CM-05[03] physical access restrictions associated with changes to the system are enforced;
                CM-05[04] logical access restrictions associated with changes to the system are defined and documented;
                CM-05[05] logical access restrictions associated with changes to the system are approved;
                CM-05[06] logical access restrictions associated with changes to the system are enforced.
              uuid: 80a456cf-c642-4b02-a0fb-18b416e90481
            - control-id: sc-10
              description: Istio is configured to manage network connections associated with specific communication sessions. It can be set up to automatically terminate these connections after periods of inactivity, providing an additional layer of security.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/communications-terminated-after-inactivity/validation.yaml
                  rel: lula
                  text: Istio terminates communication sessions after inactivity [PLACEHOLDER]
              remarks: |-
                ASSESSMENT-OBJECTIVE:
                the network connection associated with a communication session is terminated at the end of the session or after [Assignment: organization-defined time period] of inactivity.
              uuid: ad919a09-d186-4edd-9234-ead04f959fff
            - control-id: sc-13
              description: Istio provides FIPS encryption in transit for all applications in the mesh, TLS termination at ingress, and TLS origination at egress.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/enforce-mtls-strict/validation.yaml
                  rel: lula
                  text: Check that Istio is enforcing mtls STRICT
                - href: file:../../compliance/validations/istio/ingress-traffic-encrypted/validation.yaml
                  rel: lula
                  text: Istio is encrypting ingress traffic
                - href: file:../../compliance/validations/istio/tls-origination-at-egress/validation.yaml
                  rel: lula
                  text: Istio is providing TLS origination at egress [PLACEHOLDER]
                - href: file:../../compliance/validations/istio/fips-evaluation/validation.yaml
                  rel: lula
                  text: System is using FIPS-compliant Istio distribution [PLACEHOLDER]
              remarks: |
                ASSESSMENT-OBJECTIVE:
                SC-13a.  [Assignment: organization-defined cryptographic uses] are identified;
                SC-13b.  [Assignment: organization-defined types of cryptography] for each specified cryptographic use (defined in SC-13_ODP[01]) are implemented.
              uuid: 675c0823-8e94-4910-9f61-5266d7e7b38c
            - control-id: sc-23
              description: Istio is configured to protect session authenticity, establishing confidence in the ongoing identities of other parties and the validity of transmitted information. This is achieved through Istio's mutual TLS, which ensures secure communication.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/enforce-mtls-strict/validation.yaml
                  rel: lula
                  text: Check that Istio is enforcing mtls STRICT
              remarks: |-
                ASSESSMENT-OBJECTIVE:
                the authenticity of communication sessions is protected.
              uuid: dac01dde-3bdf-4e70-9d4d-4081c88de380
            - control-id: sc-39
              description: Istio is configured to maintain separate execution domains for each executing process. This is achieved through Istio's sidecar proxy design, where each service in the mesh has its own dedicated sidecar proxy to handle its inbound and outbound traffic. This ensures that communication between processes is controlled and one process cannot modify the executing code of another process.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/all-pods-istio-injected/validation.yaml
                  rel: lula
                  text: All pods are istio injected with proxyv2 sidecar
              remarks: |-
                ASSESSMENT-OBJECTIVE:
                a separate execution domain is maintained for each executing system process.
              uuid: 0e72ca49-e9cb-4a74-8701-6f81091197b6
            - control-id: sc-7.4
              description: Istio is configured to provide managed interfaces for external telecommunication services, establish traffic flow policies, and protect the confidentiality and integrity of transmitted information. It also prevents unauthorized exchange of control plane traffic and filters unauthorized control plane traffic.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/secure-communication-with-istiod/validation.yaml
                  rel: lula
                  text: Resources in namespaces can securely communicate with Istio control plane via network policies
                - href: file:../../compliance/validations/istio/authorized-keycloak-access/validation.yaml
                  rel: lula
                  text: Validates that Istio is used to authorize access to Keycloak admin console only from admin gateway
                - href: file:../../compliance/validations/istio/gateway-configuration-check/validation.yaml
                  rel: lula
                  text: Validates that Istio Gateways are available and expected VirtualServices using each Gateway.
              remarks: |
                ASSESSMENT-OBJECTIVE:
                SC-07(04)(a) a managed interface is implemented for each external telecommunication service;
                SC-07(04)(b) a traffic flow policy is established for each managed interface;
                SC-07(04)(c)
                	SC-07(04)(c)[01] the confidentiality of the information being transmitted across each interface is protected;
                	SC-07(04)(c)[02] the integrity of the information being transmitted across each interface is protected;
                SC-07(04)(d) each exception to the traffic flow policy is documented with a supporting mission or business need and duration of that need;
                SC-07(04)(e)
                	SC-07(04)(e)[01] exceptions to the traffic flow policy are reviewed [Assignment: organization-defined frequency];
                	SC-07(04)(e)[02] exceptions to the traffic flow policy that are no longer supported by an explicit mission or business need are removed;
                SC-07(04)(f) unauthorized exchanges of control plan traffic with external networks are prevented;
                SC-07(04)(g) information is published to enable remote networks to detect unauthorized control plane traffic from internal networks;
                SC-07(04)(h) unauthorized control plane traffic is filtered from external networks.
              uuid: a5bac410-d674-431d-b5fc-2f904842c29c
            - control-id: sc-7.8
              description: Istio is configured to route internal communications traffic to external networks through authenticated proxy servers at managed interfaces, using its Egress Gateway.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/egress-gateway-exists-and-configured/validation.yaml
                  rel: lula
                  text: Egress Gateway exists and is configured [PLACEHOLDER]
                - href: file:../../compliance/validations/istio/external-traffic-managed/validation.yaml
                  rel: lula
                  text: Check that external traffic is managed [PLACEHOLDER]
              remarks: |-
                ASSESSMENT-OBJECTIVE:
                 [Assignment: organization-defined internal communications traffic] is routed to [Assignment: organization-defined external networks] through authenticated proxy servers at managed interfaces.
              uuid: 3f409103-880e-4180-81e7-54f85a6143ae
            - control-id: sc-8
              description: Istio is configured to protect the confidentiality and integrity of transmitted information across both internal and external networks. This is achieved through Istio's mutual TLS, which encrypts service-to-service communication, ensuring that data in transit is not exposed to the possibility of interception and modification.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/enforce-mtls-strict/validation.yaml
                  rel: lula
                  text: Check that Istio is enforcing mtls STRICT
                - href: file:../../compliance/validations/istio/all-pods-istio-injected/validation.yaml
                  rel: lula
                  text: All pods are istio injected with proxyv2 sidecar
              remarks: |-
                ASSESSMENT-OBJECTIVE:
                the [Selection: (one-or-more) organization-defined confidentiality; integrity] of transmitted information is/are protected.
              uuid: e97a451e-44c7-4240-a7a7-adaadd26f01c
            - control-id: sc-8.1
              description: Istio is configured to protect the confidentiality and integrity of transmitted information across both internal and external networks. This is achieved through Istio's mutual TLS, which encrypts service-to-service communication, ensuring that data in transit is not exposed to the possibility of interception and modification.
              links:
                - href: file:../../compliance/validations/istio/healthcheck/validation.yaml
                  rel: lula
                  text: Check that Istio is healthy
                - href: file:../../compliance/validations/istio/enforce-mtls-strict/validation.yaml
                  rel: lula
                  text: Check that Istio is enforcing mtls STRICT
                - href: file:../../compliance/validations/istio/all-pods-istio-injected/validation.yaml
                  rel: lula
                  text: All pods are istio injected with proxyv2 sidecar
              remarks: |-
                ASSESSMENT-OBJECTIVE:
                cryptographic mechanisms are implemented to [Selection: (one-or-more) organization-defined prevent unauthorized disclosure of information; detect changes to information] during transmission.
              uuid: f3b38f79-9bf7-4024-a1b2-00befd67fda7
          props:
            - name: generation
              ns: https://docs.lula.dev/oscal/ns
              value: lula generate component --catalog-source https://raw.githubusercontent.com/GSA/fedramp-automation/refs/tags/fedramp-2.0.0-oscal-1.0.4/dist/content/rev5/baselines/json/FedRAMP_rev5_MODERATE-baseline-resolved-profile_catalog.json --component 'Istio' --requirements ac-4,ac-4.21,ac-6.9,ac-14,au-2,au-3,au-3.1,au-12,cm-5,sc-7.4,sc-7.8,sc-8,sc-8.1,sc-10,sc-13,sc-23,sc-39 --remarks assessment-objective --framework il4
            - name: framework
              ns: https://docs.lula.dev/oscal/ns
              value: il4
          source: https://raw.githubusercontent.com/GSA/fedramp-automation/refs/tags/fedramp-2.0.0-oscal-1.0.4/dist/content/rev5/baselines/json/FedRAMP_rev5_MODERATE-baseline-resolved-profile_catalog.json
          uuid: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c
      description: Component Description
      title: Istio
      type: software
      uuid: 3fad668e-7751-44e6-b1be-4fe773971370
  metadata:
    last-modified: 2024-09-17T20:24:07.548382887Z
    oscal-version: 1.1.2
    parties:
      - links:
          - href: https://uds.defenseunicorns.com/
            rel: website
        name: Unicorn Delivery Service
        type: organization
        uuid: bf31d461-82af-529a-8bdf-09aa488e5b7e
    published: 2024-09-03T21:02:56.440962532Z
    remarks: Lula Generated Component Definition
    title: Istio
    version: 0.0.1
  uuid: b0395313-5d50-4c64-b0e7-43014a47ead9
