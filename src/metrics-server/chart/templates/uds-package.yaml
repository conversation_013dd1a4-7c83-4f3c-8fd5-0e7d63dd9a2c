# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: metrics-server
  namespace: {{ .Release.Namespace }}
spec:
  network:
    serviceMesh:
      mode: ambient
    allow:
      - direction: Egress
        selector:
          app.kubernetes.io/name: metrics-server
        # todo: evaluate an "all nodes" generated rule
        remoteGenerated: Anywhere
        port: 10250
      - direction: Egress
        selector:
          app.kubernetes.io/name: metrics-server
        remoteGenerated: KubeAPI
      - direction: Ingress
        selector:
          app.kubernetes.io/name: metrics-server
        # todo: evaluate a "KubeAPI" _ingress_ generated rule
        remoteGenerated: Anywhere
        port: 10250
