# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

tasks:
  - name: validate
    inputs:
      validate_passthrough:
        description: Whether to validate the passthrough gateway

    actions:
      - description: Validate the Istio Admin Gateway
        wait:
          cluster:
            kind: gateways.networking.istio.io
            name: admin-gateway
            namespace: istio-admin-gateway

      - description: Validate the Istio Passthrough Gateway
        if: ${{ eq .inputs.validate_passthrough "true" }}
        wait:
          cluster:
            kind: gateways.networking.istio.io
            name: passthrough-gateway
            namespace: istio-passthrough-gateway

      - description: Validate the Istio Tenant Gateway
        wait:
          cluster:
            kind: gateways.networking.istio.io
            name: tenant-gateway
            namespace: istio-tenant-gateway

  - name: e2e-test
    actions:
      - description: "Run Istio E2E tests"
