# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: Compliance Evaluation

on:
  # Manual trigger
  workflow_dispatch:
    inputs:
      flavor:
        type: string
        description: "Flavor of the source package to test"
        required: true
    # Triggered by pull-request-conditionals.yaml
  workflow_call:
    inputs:
      flavor:
        type: string
        description: "Flavor of the source package to test"
        required: true

permissions:
  contents: read
  pull-requests: write

jobs:
  evaluate:
    runs-on: ubuntu-latest
    name: Evaluate
    continue-on-error: true
    steps:
      # Used to execute the uds run command
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Environment setup
        uses: ./.github/actions/setup

      - name: review compliance directory
        run: ls -al ./compliance/
        shell: bash

      - name: remove overlapping file
        run: rm ./compliance/oscal-assessment-results.yaml
        shell: bash

      - name: Download assessment
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: ${{ inputs.flavor }}-assessment-results
          path: ./compliance

      - name: review compliance directory again
        run: ls -al ./compliance/
        shell: bash

      - name: Evaluate compliance
        id: compliance-evaluation
        run: uds run test-compliance-evaluate --no-progress

      # steps in this action only run when there has been a previous failure - will indicate success thereafter
      # need to think about how much noise this could create - noise currently = good
      - name: Notify Lula Team of Compliance Assessment Results
        if: ${{ always() }}
        uses: ./.github/actions/notify-lula
        with:
          state: ${{ steps.compliance-evaluation.outcome }}
          flavor: ${{ inputs.flavor }}
          ghToken: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload Evaluated Assessment
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: ${{ inputs.flavor }}-assessment-results
          path: ./compliance/oscal-assessment-results.yaml
          overwrite: true
