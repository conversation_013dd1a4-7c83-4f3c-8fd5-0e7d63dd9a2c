# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: Metadata

# Permissions for the GITHUB_TOKEN used by the workflow.
permissions:
  contents: read # Allows reading the content of the repository.

on:
  pull_request:
    branches: [main]
    # milestoned is added here as a workaround for release-please not triggering PR workflows (PRs should be added to a milestone to trigger the workflow).
    types: [milestoned, opened, reopened, edited, synchronize]

jobs:
  title_check:
    runs-on: ubuntu-latest
    name: Validate PR Title
    permissions:
      pull-requests: read

    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0

      - name: Install commitlint
        run: |
          npm install --save-dev @commitlint/config-conventional@19.8.1
          npm install --save-dev @commitlint/cli@19.8.1

      - name: Lint PR title
        env:
          pull_request_title: ${{ github.event.pull_request.title }}
        run: echo "$pull_request_title" | npx commitlint
