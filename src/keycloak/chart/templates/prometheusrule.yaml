# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

{{- with .Values.prometheusRule -}}
{{- if .enabled }}
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ include "keycloak.fullname" $ }}
  namespace: {{ $.Release.Namespace }}  
  {{- with .annotations }}
  annotations:
  {{- range $key, $value := . }}
  {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
  {{- end }}
  {{- end }}
  labels:
  {{- include "keycloak.labels" $ | nindent 4 }}
  {{- range $key, $value := .labels }}
  {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
  {{- end }}
spec:
  groups:
    - name: {{ include "keycloak.fullname" $ }}
      rules:
        {{- toYaml .rules | nindent 8 }}
{{- end }}
{{- end -}}
