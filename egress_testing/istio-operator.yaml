# k3d cluster create my-cluster --k3s-arg "--disable=traefik@server:0"
apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
spec:
  profile: default
  hub: gcr.io/istio-testing
  tag: latest
  revision: 1-8-0
  meshConfig:
    accessLogFile: /dev/stdout
    enableTracing: true
    outboundTrafficPolicy:
      mode: "REGISTRY_ONLY"
  components:
    egressGateways:
    - name: istio-egressgateway
      enabled: true
