# Package 1
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-1
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-1
  namespace: test-app-1
spec:
  network:
    allow:
      - direction: Egress
        remoteHost: "api.github.com"
        port: 443
      - direction: Egress
        remoteHost: "api.github.com"
        port: 80
        description: "github-80-tls"
      - direction: Egress
        remoteHost: "api.github.com"
        port: 80
        remoteProtocol: "HTTP"
        description: "github-80-http"