yaml-files:
  - "**/*.y*ml"
  - ".yamllint"

# Ignore files from upstream
ignore:
  - "k3d/local/manifests/metallb/metallb-native.yaml"
  - "**/.terraform/**"
  - "**/chart/templates/**"
  - "**/charts/**/templates/**"
  - "**/node_modules/**"
  - "dist/**"
  - "src/pepr/uds-operator-config/templates/**"
  - ".codespellrc"
  - ".lintstagedrc.json"
  - "**/package-lock.json"
  - "uds-docs/**"

rules:
  anchors: enable
  braces: enable
  brackets: enable
  colons: enable
  commas: enable
  comments:
    level: warning
  comments-indentation:
    level: warning
  document-end: disable
  document-start:
    level: warning
  empty-lines: enable
  empty-values: disable
  float-values: disable
  hyphens: enable
  indentation: enable
  key-duplicates: enable
  key-ordering: disable
  line-length: disable
  new-line-at-end-of-file: enable
  new-lines: enable
  octal-values: disable
  quoted-strings: disable
  trailing-spaces: enable
  truthy:
    level: warning
