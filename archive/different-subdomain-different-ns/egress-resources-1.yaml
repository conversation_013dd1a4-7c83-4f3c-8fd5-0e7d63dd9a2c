# Add the gateway, service entry, destination rule, and virtual service
apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: different-subdomain-gateway-1
  namespace: different-subdomain-ns-1
spec:
  selector:
    app: egressgateway
  servers:
  - port:
      number: 443
      name: tls
      protocol: TLS
    hosts:
      - en.wikipedia.org
    tls:
      mode: PASSTHROUGH
---
apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: different-subdomain-service-entry-1
  namespace: different-subdomain-ns-1
spec:
  location: MESH_EXTERNAL
  hosts:
  - en.wikipedia.org
  ports: 
  - number: 443
    name: tls
    protocol: TLS
  resolution: DNS
---
apiVersion: networking.istio.io/v1
kind: DestinationRule
metadata:
  name: egressgateway-for-different-subdomain-1
  namespace: different-subdomain-ns-1
spec:
  host: egressgateway.istio-egress-gateway.svc.cluster.local
  subsets:
    - name: different-subdomain-1
---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: different-subdomain-through-egress-gateway-1
  namespace: different-subdomain-ns-1
spec:
  hosts:
  - en.wikipedia.org
  gateways:
  - different-subdomain-gateway-1
  - mesh
  tls:
  - match:
    - gateways:
      - mesh
      port: 443
      sniHosts:
      - en.wikipedia.org
    route:
    - destination:
        host: egressgateway.istio-egress-gateway.svc.cluster.local
        subset: different-subdomain-1
        port:
          number: 443
  - match:
    - gateways:
      - different-subdomain-gateway-1
      port: 443
      sniHosts:
      - en.wikipedia.org
    route:
    - destination:
        host: en.wikipedia.org
        port:
          number: 443
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-egress-gateway-access-1
  namespace: different-subdomain-ns-1
spec:
  podSelector: 
    matchLabels:
      app: curl1
  policyTypes:
  - Egress
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: istio-egress-gateway
      podSelector:
        matchLabels:
          app: egressgateway
    ports:
    - protocol: TCP
      port: 443