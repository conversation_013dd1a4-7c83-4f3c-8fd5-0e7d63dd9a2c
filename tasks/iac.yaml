# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

includes:
  - util: ./utils.yaml

variables:
  - name: CLUSTER_NAME
  - name: K8S_DISTRO
  - name: CLOUD
  - name: REGION
  - name: PERMISSIONS_BOUNDARY_NAME
  - name: PERMISSIONS_BOUNDARY_ARN
  - name: STATE_BUCKET_NAME
  - name: STATE_DYNAMODB_TABLE_NAME
  - name: STATE_KEY
  - name: RESOURCE_GROUP_NAME
  - name: STORAGE_ACCOUNT_NAME
  - name: CONTAINER_NAME

tasks:
  - name: rke2-get-kubeconfig
    actions:
      - cmd: chmod +x ./scripts/get-kubeconfig.sh && ./scripts/get-kubeconfig.sh
        dir: .github/test-infra/aws/rke2/

  - name: rke2-nodes-ready
    actions:
      - cmd: sleep 30
      - wait:
          cluster:
            kind: nodes
            condition: Ready
            name: kubernetes.io/os=linux
        maxTotalSeconds: 900

  - name: rke2-cluster-ready
    actions:
      - task: rke2-nodes-ready
      - cmd: |
          # wait for nodes to be ready
          echo "Waiting for all cluster nodes to be ready...";
          while true; do
            if [ $(uds zarf tools kubectl get nodes | grep Ready | wc -l) -lt 4 ]; then
              sleep 5;
            else
              break
            fi
          done
          # wait for cluster components
          echo "Waiting for Helm Controller to install cluster components..."
          while true; do
            if [ $(uds zarf tools kubectl get po -n kube-system -l batch.kubernetes.io/controller-uid --no-headers | egrep -v Completed | wc -l) -gt 0 ]; then
               sleep 5;
            else
              break
            fi
          done
          echo "Waiting for cluster components to be ready...";
          while true; do
            if [ $(uds zarf tools kubectl get po,job -A --no-headers=true | egrep -v 'Running|Complete' | wc -l) -gt 0 ]; then
              sleep 5;
            else
              echo "Cluster is ready!"
              break
            fi
          done

  - name: create-iac
    actions:
      - task: apply-tofu

  - name: destroy-iac
    actions:
      - cmd: tofu destroy -auto-approve
        dir: .github/test-infra/${CLOUD}/${K8S_DISTRO}

  - name: apply-tofu
    actions:
      - cmd: |
          if [ ${CLOUD} = "aws" ]; then
            tofu init -force-copy \
              -backend-config="bucket=${STATE_BUCKET_NAME}" \
              -backend-config="key=${STATE_KEY}" \
              -backend-config="region=${REGION}" \
              -backend-config="dynamodb_table=${STATE_DYNAMODB_TABLE_NAME}"
          elif [ ${CLOUD} = "azure" ]; then
            tofu init -force-copy \
              -backend=true \
              -backend-config="resource_group_name=$RESOURCE_GROUP_NAME" \
              -backend-config="storage_account_name=$STORAGE_ACCOUNT_NAME" \
              -backend-config="container_name=$CONTAINER_NAME" \
              -backend-config="key=${STATE_KEY}"
          else
            echo "Invalid cloud provider specified."; return 1; fi
        dir: .github/test-infra/${CLOUD}/${K8S_DISTRO}
      - cmd: tofu apply -auto-approve
        dir: .github/test-infra/${CLOUD}/${K8S_DISTRO}

  - name: eks-get-kubeconfig
    actions:
      - cmd: |
          aws eks update-kubeconfig --name ${CLUSTER_NAME} --region ${REGION}
