# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: keycloak
  namespace: {{ .Release.Namespace }}
spec:
  monitor:
    - selector:
        app.kubernetes.io/name: keycloak
        app.kubernetes.io/component: http
      podSelector:
        app.kubernetes.io/name: keycloak
      targetPort: 9000
      portName: http-metrics
      description: Metrics

  network:
    serviceMesh:
      mode: ambient
    allow:
      - description: "Waypoint health"
        direction: Ingress
        selector:
          app.kubernetes.io/name: keycloak
        remoteNamespace: monitoring
        remoteSelector:
          app.kubernetes.io/name: prometheus
        remoteServiceAccount: kube-prometheus-stack-prometheus
        port: 15020

      - description: "Keycloak backchannel access"
        direction: Ingress
        selector:
          app.kubernetes.io/name: keycloak
        # Allow access from anything in cluster using an empty namespace selector
        remoteNamespace: "*"
        port: 8080

      # Keycloak OCSP to check certs cannot guarantee a static IP
      - description: "OCSP Lookup"
        direction: Egress
        selector:
          app.kubernetes.io/name: keycloak
        ports:
          - 443
          - 80
        remoteGenerated: Anywhere

      {{- if .Values.smtp.enabled }}
      - description: "SMTP access"
        direction: Egress
        selector:
          app.kubernetes.io/name: keycloak
        port: {{ .Values.smtp.port }}
        remoteGenerated: Anywhere
      {{- end }}

      {{- if eq (include "keycloak.postgresql.config" .) "true" }}
      - description: "PostgreSQL Database access"
        direction: Egress
        selector:
          app.kubernetes.io/name: keycloak
        port: {{ .Values.postgresql.port }}
        {{- if .Values.postgresql.internal.enabled }}
        remoteSelector:
          {{- .Values.postgresql.internal.remoteSelector | toYaml | nindent 10 }}
        remoteNamespace: {{ .Values.postgresql.internal.remoteNamespace }}
        {{- else if .Values.postgresql.egressCidr }}
        remoteCidr: {{ .Values.postgresql.egressCidr }}
        {{- else }}
        remoteGenerated: Anywhere
        {{- end }}
      {{- end }}

      - description: "Keycloak to Waypoint"
        direction: Ingress
        remoteGenerated: IntraNamespace
        ports:
          - 8080
      - description: "Waypoint to Keycloak"
        direction: Egress
        remoteGenerated: IntraNamespace
        ports:
          - 8080

      - description: "Waypoint to Istio Control Plane"
        direction: Egress
        selector:
          gateway.networking.k8s.io/gateway-name: {{ include "keycloak.fullname" . }}-waypoint
        remoteNamespace: istio-system
        remoteSelector:
          app: istiod
        port: 15012

      {{- if .Values.autoscaling.enabled }}
      # HA for keycloak
      - direction: Ingress
        remoteGenerated: IntraNamespace
        ports:
          - 7800
          # Below array of ports is required as JGroups probe the entire range of Transport Ports with DNS_PING.
          # Other cluster members might listen on any of them.
          # See:
          # - https://github.com/belaban/JGroups/blob/master/src/org/jgroups/protocols/dns/DNS_PING.java#L168-L169
          # - Trace logs from a living cluster:
          #   2025-05-06 07:34:23,226 DEBUG [org.jgroups.protocols.dns.DNS_PING] () keycloak-2-51586: sending discovery requests to hosts [**********:0, **********:0, **********:0] on ports [7800 .. 7810]
          # This will go away once we switch to JDBC_PING2
          - 7801
          - 7802
          - 7803
          - 7804
          - 7805
          - 7806
          - 7807
          - 7808
          - 7809
          - 7810
          - 57800
      - direction: Egress
        remoteGenerated: IntraNamespace
        ports:
          - 7800
          # Below array of ports is required as JGroups probe the entire range of Transport Ports with DNS_PING.
          - 7801
          - 7802
          - 7803
          - 7804
          - 7805
          - 7806
          - 7807
          - 7808
          - 7809
          - 7810
          - 57800
      {{- end }}

      # Custom rules for additional networking access
      {{- with .Values.additionalNetworkAllow }}
      {{ toYaml . | nindent 6 }}
      {{- end }}

    expose:
      - description: "remove private paths from public gateway"
        host: sso
        service: keycloak-http
        selector:
          app.kubernetes.io/name: keycloak
        port: 8080
        advancedHTTP:
          match:
            - name: redirect-welcome
              uri:
                exact: /
            - name: redirect-admin
              uri:
                prefix: /admin
            - name: redirect-master-realm
              uri:
                prefix: /realms/master
            - name: redirect-metrics
              uri:
                prefix: /metrics
          redirect:
            uri: "/realms/{{ .Values.realm }}/account"
          headers:
            request:
              remove:
                - istio-mtls-client-certificate
              add:
                istio-mtls-client-certificate: "{{ .Values.mtlsClientCert }}"

      - description: "public auth access with optional client certificate"
        service: keycloak-http
        selector:
          app.kubernetes.io/name: keycloak
        host: sso
        port: 8080
        advancedHTTP:
          headers:
            request:
              remove:
                - istio-mtls-client-certificate
              add:
                istio-mtls-client-certificate: "{{ .Values.mtlsClientCert }}"

      - description: "admin access with optional client certificate"
        service: keycloak-http
        selector:
          app.kubernetes.io/name: keycloak
        gateway: admin
        host: keycloak
        port: 8080
        advancedHTTP:
          headers:
            request:
              remove:
                - istio-mtls-client-certificate
              add:
                istio-mtls-client-certificate: "{{ .Values.mtlsClientCert }}"
