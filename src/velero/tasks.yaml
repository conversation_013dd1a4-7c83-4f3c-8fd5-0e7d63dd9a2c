# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

tasks:
  - name: validate
    actions:
      - task: health-check
      - task: execute-backup

  - name: health-check
    actions:
      - description: Velero StatefulSet Health Check
        wait:
          cluster:
            kind: Deployment
            name: velero
            namespace: velero

  - name: execute-backup
    actions:
      - description: generates a unique backup name
        cmd: "echo \"velero-test-$(date +'%Y%m%d%H%M%S')\""
        mute: true
        setVariables:
          - name: BACKUP_NAME
      - description: "creates a velero backup object (equivalent of `velero backup create --from-schedule ...`)"
        cmd: |-
          uds zarf tools kubectl apply -f - <<-EOF
            apiVersion: velero.io/v1
            kind: Backup
            metadata:
              name: ${BACKUP_NAME}
              namespace: velero
            spec:
              storageLocation: default
              csiSnapshotTimeout: 0s
              excludedNamespaces:
              - kube-system
              - flux
              - velero
              hooks: {}
              includeClusterResources: true
              itemOperationTimeout: 0s
              metadata: {}
              snapshotVolumes: true
              ttl: 240h0m0s
          EOF
      - description: wait for the backup object
        wait:
          cluster:
            kind: backup.velero.io
            name: ${BACKUP_NAME}
            namespace: velero
      - description: check the status of the backup object
        cmd: |-
          debug_velero_backup() {
            # get backup object
            uds zarf tools kubectl get backup.velero.io -n velero ${BACKUP_NAME} -o yaml
            uds zarf tools kubectl get backup.velero.io -A -o yaml
            echo "::endgroup::"

            # get backupstoragelocations
            uds zarf tools kubectl get backupstoragelocation -A -o yaml

            # describe backup
            uds zarf tools kubectl describe backup -n velero ${BACKUP_NAME}

            # velero pod logs
            uds zarf tools kubectl logs -n velero -l name=velero
          }

          STATUS=$(uds zarf tools kubectl get backup.velero.io -n velero ${BACKUP_NAME} -o jsonpath='{.status.phase}')
          if [ ${STATUS} != "Completed" ]; then
            echo "Status is '$STATUS'... waiting to see if it changes"

            # local testing indicates the status is "Finalizing" for a few seconds after completion
            sleep 30

            # check again...
            STATUS=$(uds zarf tools kubectl get backup.velero.io -n velero ${BACKUP_NAME} -o jsonpath='{.status.phase}')
            if [ ${STATUS} != "Completed" ]; then
              echo "Status is $STATUS... something isn't right.."
              debug_velero_backup
              # be a quitter
              exit 1
            fi
          fi

          echo "Checking Velero Volume Snapshot counts in the backup status..."
          SNAPSHOT_INFO=$(uds zarf tools kubectl describe backup.velero.io -n velero ${BACKUP_NAME})
          ATTEMPTED=$(echo "$SNAPSHOT_INFO" | grep 'Volume Snapshots Attempted:' | awk '{print $4}')
          COMPLETED=$(echo "$SNAPSHOT_INFO" | grep 'Volume Snapshots Completed:' | awk '{print $4}')

          echo "Velero Volume Snapshots Attempted: ${ATTEMPTED}"
          echo "Velero Volume Snapshots Completed: ${COMPLETED}"

          if [ "$ATTEMPTED" != "$COMPLETED" ]; then
            echo "Mismatch detected: Only ${COMPLETED} out of ${ATTEMPTED} Velero volume snapshots completed."
            debug_velero_backup
            exit 1
          fi
          echo "Status is Complete! Great success!"

  - name: e2e-test
    actions:
      - description: "Run Velero E2E tests"
