# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: v1
kind: Service
metadata:
  name: curl
  namespace: egress-gw-1
  labels:
    name: curl
    namespace: egress-gw-1
spec:
  ports:
    - name: port8080
      port: 8080
      targetPort: 8080
  selector:
    app: curl
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl
  namespace: egress-gw-1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl
  template:
    metadata:
      labels:
        app: curl
      annotations:
        sidecar.istio.io/proxyCPU: "10m"
        sidecar.istio.io/proxyMemory: "16Mi"
    spec:
      containers:
        - name: curl
          image: curlimages/curl
          imagePullPolicy: IfNotPresent
          command: ["sleep", "3600"]
          resources:
            limits:
              cpu: 50m
              memory: 64Mi
            requests:
              cpu: 10m
              memory: 16Mi
          ports:
            - containerPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: curl
  namespace: egress-gw-2
  labels:
    name: curl
    namespace: egress-gw-2
spec:
  ports:
    - name: port8080
      port: 8080
      targetPort: 8080
  selector:
    app: curl
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl
  namespace: egress-gw-2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl
  template:
    metadata:
      labels:
        app: curl
      annotations:
        sidecar.istio.io/proxyCPU: "10m"
        sidecar.istio.io/proxyMemory: "16Mi"
    spec:
      containers:
        - name: curl
          image: curlimages/curl
          imagePullPolicy: IfNotPresent
          command: ["sleep", "3600"]
          resources:
            limits:
              cpu: 50m
              memory: 64Mi
            requests:
              cpu: 10m
              memory: 16Mi
          ports:
            - containerPort: 8080
