# Test 1a - Change the port/protocol
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-1
  namespace: ns-1
spec:
  network: 
    allow:
    - direction: Egress
      selector:
        app: curl1
      description: "Curl to example.com"
      remoteHost: "example.com"
      remoteProtocol: HTTP
      port: 80
# ---
# # Test 1b - Change the host name
# apiVersion: uds.dev/v1alpha1
# kind: Package
# metadata:
#   name: pkg-1
#   namespace: ns-1
# spec:
#   network: 
#     allow:
#     - direction: Egress
#       selector:
#         app: curl1
#       description: "Curl to httpbin.org"
#       remoteHost: "httpbin.org"
#       remoteProtocol: HTTP
#       port: 80
# ---
# # Test 1c - Change the selector
# apiVersion: uds.dev/v1alpha1
# kind: Package
# metadata:
#   name: pkg-1
#   namespace: ns-1
# spec:
#   network: 
#     allow:
#     - direction: Egress
#       selector:
#         app: curl1
#       description: "Curl to httpbin.org"
#       remoteHost: "httpbin.org"
#       remoteProtocol: HTTP
#       port: 80
