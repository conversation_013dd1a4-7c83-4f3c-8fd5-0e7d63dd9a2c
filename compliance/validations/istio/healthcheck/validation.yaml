# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

metadata:
  name: istio-health-check
  uuid: 67456ae8-4505-4c93-b341-d977d90cb125
domain:
  type: kubernetes
  kubernetes-spec:
    resources:
      - name: istioddeployment
        resource-rule:
          group: apps
          name: istiod
          namespaces:
            - istio-system
          resource: deployments
          version: v1
      - name: istiodhpa
        resource-rule:
          group: autoscaling
          name: istiod
          namespaces:
            - istio-system
          resource: horizontalpodautoscalers
          version: v2
provider:
  type: opa
  opa-spec:
    output:
      validation: validate.validate
      observations:
        - validate.msg
        - validate.deployment_message
        - validate.hpa_message
    rego: |
      package validate
      import rego.v1

      # Default values
      default validate := false
      default msg := "Not evaluated"

      # Check if the Istio Deployment is healthy
      validate if {
          check_deployment_health.result
          check_hpa_health.result
      }

      msg = concat(" ", [check_deployment_health.msg, check_hpa_health.msg])

      check_deployment_health = {"result": true, "msg": msg} if {
        input.istioddeployment.status.replicas > 0
        input.istioddeployment.status.availableReplicas == input.istioddeployment.status.replicas
        msg := "Istiod Deployment is healthy."
      } else = {"result": false, "msg": msg} if {
        msg := "Istiod Deployment is not healthy."
      }

      check_hpa_health = {"result": true, "msg": msg} if {
        input.istiodhpa.status.currentReplicas >= input.istiodhpa.spec.minReplicas
        msg := "HPA has sufficient replicas."
      } else = {"result": false, "msg": msg} if {
        msg := "HPA does not have sufficient replicas."
      }
