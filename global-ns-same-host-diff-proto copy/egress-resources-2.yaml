apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-egress-gateway-access-2
  namespace: ns-2
spec:
  podSelector: 
    matchLabels:
      app: curl2
  policyTypes:
  - Egress
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: istio-egress-gateway
      podSelector:
        matchLabels:
          app: egressgateway
    ports:
    - protocol: TCP
      port: 443
---
apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: service-entry-2
  namespace: ns-2
spec:
  location: MESH_EXTERNAL
  hosts:
  - example.com
  ports: 
  - number: 443
    name: http
    protocol: HTTP
  resolution: DNS
  exportTo:
    - "."
    - "istio-egress-gateway"
---
# Global gateway, destination rule, virtual service
apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: example-com-443-http-gateway
  namespace: istio-egress-gateway
spec:
  selector:
    app: egressgateway
  servers:
  - port:
      number: 443
      name: http
      protocol: HTTP
    hosts:
      - example.com
    tls:
      mode: PASSTHROUGH
---
apiVersion: networking.istio.io/v1
kind: DestinationRule
metadata:
  name: egressgateway-for-example-com-443-http
  namespace: istio-egress-gateway
  annotations:
    uds-core.pepr.dev/egress: '{"pkgs":["pkg-1", "pkg-2"], "route": {"host": "example.com", "port": 443, "protcol": "tls"}}' 
spec:
  host: egressgateway.istio-egress-gateway.svc.cluster.local
  subsets:
    - name: egress-to-example-com-443-http
---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: through-egress-gateway-2
  namespace: istio-egress-gateway
  annotations:
    uds-core.pepr.dev/egress: '{"pkgs":["pkg-1", "pkg-2"], "route": {"host": "example.com", "port": 443, "protcol": "tls"}}' 
spec:
  hosts:
  - example.com
  gateways:
  - example-com-443-http-gateway
  - mesh
  http:
  - match:
    - gateways:
      - mesh
      port: 443
    route:
    - destination:
        host: egressgateway.istio-egress-gateway.svc.cluster.local
        subset: egress-to-example-com-443-http
        port:
          number: 443
  - match:
    - gateways:
      - example-com-443-http-gateway
      port: 443
    route:
    - destination:
        host: example.com
        port:
          number: 443