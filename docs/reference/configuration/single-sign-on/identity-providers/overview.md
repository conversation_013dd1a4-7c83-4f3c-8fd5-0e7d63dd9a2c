---
title: Overview
sidebar:
  order: 2
---


UDS Core utilizes [Keycloak](https://www.keycloak.org/) to simplify identity brokering, enabling seamless integration
with external identity providers through industry-standard protocols such as SAML, OAuth 2.0, and OpenID Connect (OIDC).
This functionality allows users to authenticate using their existing credentials from third-party services.

The identity brokering process by automating provider configuration, managing
dependencies, and supporting advanced customization options. For more details, refer to
the [Identity Brokering documentation](https://www.keycloak.org/docs/latest/server_admin/index.html#_identity_broker).

## Identity Providers Contents

1. [Azure Entra ID](/reference/configuration/single-sign-on/identity-providers/azure-idp/)
2. [Google IdP](/reference/configuration/single-sign-on/identity-providers/google-idp/)