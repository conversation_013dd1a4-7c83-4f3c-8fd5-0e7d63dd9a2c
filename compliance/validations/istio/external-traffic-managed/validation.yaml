# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

metadata:
  name: external-traffic-managed-PLACEHOLDER
  uuid: 19faf69a-de74-4b78-a628-64a9f244ae13
domain:
  type: kubernetes
  kubernetes-spec:
    resources: []
provider:
  type: opa
  opa-spec:
    rego: |
      package validate

      default validate := false

      # This policy could check meshConfig.outboundTrafficPolicy.mode (default is ALLOW_ANY)
      # Possibly would need a ServiceEntry(?)
      # (https://istio.io/latest/docs/tasks/traffic-management/egress/egress-control/#envoy-passthrough-to-external-services)
