# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: core-runtime-security
  description: "UDS Core (Runtime Security)"
  authors: "Defense Unicorns - Product"
  # x-release-please-start-version
  version: "0.45.1"
  # x-release-please-end
  x-uds-dependencies: ["base", "identity-authorization"]
  annotations:
    dev.uds.title: UDS Core (Runtime Security)
    dev.uds.tagline: Enable more advanced security with Runtime inspection
    dev.uds.category: UDS-Core,Runtime Security
    dev.uds.keywords: uds,neuvector

components:
  # Neuvector
  - name: neuvector
    required: true
    import:
      path: ../../src/neuvector
