# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

pass:
  - test: default
    validation: validation.yaml
    resources: resources.json
    expected-validation: false
  - test: add-tracing
    validation: validation.yaml
    resources: resources.json
    permutation: '(.istioConfig.defaultConfig.tracing.zipkin.address) |= "zipkin.istio-system.svc.cluster.local:9411"'
    expected-validation: true
