# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

alertmanager:
  alertmanagerSpec:
    image:
      registry: quay.io
      repository: rfcurated/prometheus/alertmanager
      tag: 0.28.1-jammy-fips-rfcurated-rfhardened
kube-state-metrics:
  image:
    registry: quay.io
    repository: rfcurated/kube-state-metrics
    tag: 2.15.0-jammy-scratch-fips-rfcurated-rfhardened
  securityContext:
    enabled: true
    fsGroup: 65532
    runAsGroup: 65532
    runAsNonRoot: true
    runAsUser: 65532
prometheus:
  prometheusSpec:
    image:
      registry: quay.io
      repository: rfcurated/prometheus
      tag: 3.4.1-jammy-fips-rfcurated-rfhardened
prometheus-node-exporter:
  image:
    registry: quay.io
    repository: rfcurated/prometheus/node-exporter
    tag: 1.9.1-jammy-scratch-fips-rfcurated
prometheusOperator:
  admissionWebhooks:
    containerSecurityContext:
      capabilities:
        drop:
          - ALL
    patch:
      enabled: true
      image:
        registry: quay.io
        repository: rfcurated/ingress-nginx/kube-webhook-certgen
        tag: 1.5.4-jammy-fips-rfcurated-rfhardened
      securityContext:
        runAsGroup: 65532
        runAsNonRoot: true
        runAsUser: 65532
  image:
    registry: quay.io
    repository: rfcurated/prometheus-operator
    tag: 0.83.0-jammy-scratch-fips-rfcurated
  prometheusConfigReloader:
    image:
      registry: quay.io
      repository: rfcurated/prometheus-operator/prometheus-config-reloader
      tag: 0.83.0-jammy-scratch-fips-rfcurated
