# Copyright 2025 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: autogenerated-check
description: "Check Project for out of date autogenerated files"

runs:
  using: composite
  steps:
    - name: Environment setup
      uses: ./.github/actions/setup

    - name: Autogenerate Files
      shell: bash
      run: |
        # Install Pepr/Dependencies
        npm ci
        # renovate: datasource=github-tags depName=google/addlicense versioning=semver
        GOPATH="$HOME/go" go install github.com/google/addlicense@v1.1.1

        # We need a cluster for generating our CRD files - args used to simplify cluster setup
        k3d cluster create --k3s-arg "--disable=traefik@server:*" \
          --k3s-arg "--disable=metrics-server@server:*" \
          --k3s-arg "--disable=servicelb@server:*" \
          --k3s-arg "--disable=local-storage@server:*" \
          --no-lb

        # Generate CRD files
        uds run -f src/pepr/tasks.yaml gen-crds

        # Generate CACert values
        uds run -f src/keycloak/tasks.yaml cacert

    - name: Check generated files for diffs
      shell: bash
      run: |
        # Check for specific diffs
        DIFFS=false

        if [ ! -z "$(git status -s src/pepr/operator/crd/generated/ schemas/ docs/reference/configuration/custom-resources/)" ]; then
          # Diffs for CRDs
          DIFFS=true
          echo -e "\033[33m⚠️ Autogenerated CRD files are not up to date, please run \`uds run -f src/pepr/tasks.yaml gen-crds\` (with an active cluster) and commit the changes.\033[0m"
        fi

        if [ ! -z "$(git status -s src/istio/values/)" ]; then
          # Diffs for CACerts
          DIFFS=true
          echo -e "\033[33m⚠️ Autogenerated CACerts are not up to date, please run \`uds run -f src/keycloak/tasks.yaml cacert\` and commit the changes.\033[0m"
        fi

        if [ "${DIFFS}" = "true" ]; then
          echo -e "\033[31m❌ ERROR: Ensure that all generated file changes are up to date and committed (see warnings above).\033[0m"
          exit 1
        fi
