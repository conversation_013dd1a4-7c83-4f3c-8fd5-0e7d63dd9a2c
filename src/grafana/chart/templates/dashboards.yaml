# Copyright 2025 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: v1
kind: ConfigMap
metadata:
  name: uds-grafana-dashboards
  namespace: {{ .Release.Namespace }}
  labels:
    grafana_dashboard: "1"
data:
  istio-sidecar-ambient-compare.json: |
    {
        "annotations": {
            "list": [
                {
                    "builtIn": 1,
                    "datasource": {
                        "type": "grafana",
                        "uid": "-- Grafana --"
                    },
                    "enable": true,
                    "hide": true,
                    "iconColor": "rgba(0, 211, 255, 1)",
                    "name": "Annotations & Alerts",
                    "type": "dashboard"
                }
            ]
        },
        "editable": true,
        "fiscalYearStartMonth": 0,
        "graphTooltip": 0,
        "id": 28,
        "links": [],
        "panels": [
            {
                "datasource": "Prometheus",
                "fieldConfig": {
                    "defaults": {
                        "mappings": [
                            {
                                "options": {
                                    "0": {
                                        "text": "Not in Mesh"
                                    },
                                    "1": {
                                        "text": "Sidecar Mode"
                                    },
                                    "2": {
                                        "text": "Ambient Mode"
                                    },
                                    "3": {
                                        "text": "Hybrid"
                                    }
                                },
                                "type": "value"
                            }
                        ],
                        "thresholds": {
                            "mode": "absolute",
                            "steps": [
                                {
                                    "color": "gray"
                                },
                                {
                                    "color": "blue",
                                    "value": 0
                                },
                                {
                                    "color": "orange",
                                    "value": 1
                                },
                                {
                                    "color": "green",
                                    "value": 2
                                },
                                {
                                    "color": "red",
                                    "value": 3
                                }
                            ]
                        },
                        "unit": "none"
                    },
                    "overrides": []
                },
                "gridPos": {
                    "h": 3,
                    "w": 5,
                    "x": 0,
                    "y": 0
                },
                "id": 1,
                "options": {
                    "colorMode": "value",
                    "graphMode": "none",
                    "justifyMode": "center",
                    "orientation": "auto",
                    "percentChangeColorMode": "standard",
                    "reduceOptions": {
                        "calcs": [
                            "lastNotNull"
                        ],
                        "fields": "",
                        "values": false
                    },
                    "showPercentChange": false,
                    "textMode": "value",
                    "wideLayout": true
                },
                "pluginVersion": "11.6.1",
                "targets": [
                    {
                        "editorMode": "code",
                        "expr": "clamp_max(count(kube_namespace_labels{label_istio_injection=\"enabled\", namespace=\"$namespace\"}) or vector(0), 1) * 1",
                        "instant": false,
                        "legendFormat": "isSidecar",
                        "refId": "A"
                    },
                    {
                        "editorMode": "code",
                        "expr": "clamp_max(count(kube_namespace_labels{label_istio_io_dataplane_mode=\"ambient\", namespace=\"$namespace\"}) or vector(0), 1) * 2",
                        "instant": false,
                        "legendFormat": "isAmbient",
                        "refId": "B"
                    }
                ],
                "title": "Istio Mode",
                "transformations": [
                    {
                        "id": "calculateField",
                        "options": {
                            "binary": {
                                "left": {
                                    "matcher": {
                                        "id": "byName",
                                        "options": "isSidecar"
                                    }
                                },
                                "right": {
                                    "matcher": {
                                        "id": "byName",
                                        "options": "isAmbient"
                                    }
                                }
                            },
                            "mode": "binary",
                            "reduce": {
                                "reducer": "sum"
                            },
                            "replaceFields": true
                        }
                    }
                ],
                "type": "stat"
            },
            {
                "datasource": "Prometheus",
                "fieldConfig": {
                    "defaults": {
                        "color": {
                            "mode": "palette-classic"
                        },
                        "custom": {
                            "axisBorderShow": false,
                            "axisCenteredZero": false,
                            "axisColorMode": "text",
                            "axisLabel": "",
                            "axisPlacement": "auto",
                            "barAlignment": 0,
                            "barWidthFactor": 0.6,
                            "drawStyle": "line",
                            "fillOpacity": 0,
                            "gradientMode": "none",
                            "hideFrom": {
                                "legend": false,
                                "tooltip": false,
                                "viz": false
                            },
                            "insertNulls": false,
                            "lineInterpolation": "linear",
                            "lineWidth": 1,
                            "pointSize": 5,
                            "scaleDistribution": {
                                "type": "linear"
                            },
                            "showPoints": "auto",
                            "spanNulls": false,
                            "stacking": {
                                "group": "A",
                                "mode": "none"
                            },
                            "thresholdsStyle": {
                                "mode": "off"
                            }
                        },
                        "mappings": [],
                        "thresholds": {
                            "mode": "absolute",
                            "steps": [
                                {
                                    "color": "green"
                                },
                                {
                                    "color": "red",
                                    "value": 80
                                }
                            ]
                        },
                        "unit": "cores"
                    },
                    "overrides": []
                },
                "gridPos": {
                    "h": 10,
                    "w": 24,
                    "x": 0,
                    "y": 3
                },
                "id": 3,
                "options": {
                    "legend": {
                        "calcs": [],
                        "displayMode": "table",
                        "placement": "bottom",
                        "showLegend": true
                    },
                    "tooltip": {
                        "hideZeros": false,
                        "mode": "single",
                        "sort": "none"
                    }
                },
                "pluginVersion": "11.6.1",
                "targets": [
                    {
                        "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"$namespace\", container!=\"\", pod!~\".*waypoint.*\"}[1m]))",
                        "legendFormat": "With Sidecar",
                        "refId": "A"
                    },
                    {
                        "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"$namespace\", container!=\"\", container!=\"istio-proxy\"}[1m]))",
                        "legendFormat": "Without Sidecar",
                        "refId": "B"
                    }
                ],
                "title": "CPU Usage - All Workloads (With vs Without Istio Sidecar)",
                "type": "timeseries"
            },
            {
                "datasource": "Prometheus",
                "fieldConfig": {
                    "defaults": {
                        "color": {
                            "mode": "palette-classic"
                        },
                        "custom": {
                            "axisBorderShow": false,
                            "axisCenteredZero": false,
                            "axisColorMode": "text",
                            "axisLabel": "",
                            "axisPlacement": "auto",
                            "barAlignment": 0,
                            "barWidthFactor": 0.6,
                            "drawStyle": "line",
                            "fillOpacity": 0,
                            "gradientMode": "none",
                            "hideFrom": {
                                "legend": false,
                                "tooltip": false,
                                "viz": false
                            },
                            "insertNulls": false,
                            "lineInterpolation": "linear",
                            "lineWidth": 1,
                            "pointSize": 5,
                            "scaleDistribution": {
                                "type": "linear"
                            },
                            "showPoints": "auto",
                            "spanNulls": false,
                            "stacking": {
                                "group": "A",
                                "mode": "none"
                            },
                            "thresholdsStyle": {
                                "mode": "off"
                            }
                        },
                        "mappings": [],
                        "thresholds": {
                            "mode": "absolute",
                            "steps": [
                                {
                                    "color": "green"
                                },
                                {
                                    "color": "red",
                                    "value": 80
                                }
                            ]
                        },
                        "unit": "bytes"
                    },
                    "overrides": []
                },
                "gridPos": {
                    "h": 10,
                    "w": 24,
                    "x": 0,
                    "y": 13
                },
                "id": 2,
                "options": {
                    "legend": {
                        "calcs": [],
                        "displayMode": "table",
                        "placement": "bottom",
                        "showLegend": true
                    },
                    "tooltip": {
                        "hideZeros": false,
                        "mode": "single",
                        "sort": "none"
                    }
                },
                "pluginVersion": "11.6.1",
                "targets": [
                    {
                        "expr": "sum(container_memory_usage_bytes{namespace=\"$namespace\", container!=\"\", pod!~\".*waypoint.*\"})",
                        "legendFormat": "With Sidecar",
                        "refId": "A"
                    },
                    {
                        "expr": "sum(container_memory_usage_bytes{namespace=\"$namespace\", container!=\"\", container!=\"istio-proxy\"})",
                        "legendFormat": "Without Sidecar",
                        "refId": "B"
                    }
                ],
                "title": "Memory Usage - All Workloads (With vs Without Istio Sidecar)",
                "type": "timeseries"
            }
        ],
        "preload": false,
        "refresh": "",
        "schemaVersion": 41,
        "tags": [
            "istio",
            "uds"
        ],
        "templating": {
            "list": [
                {
                    "datasource": "Prometheus",
                    "definition": "label_values(kube_pod_info, namespace)",
                    "includeAll": false,
                    "label": "Namespace",
                    "name": "namespace",
                    "query": {
                        "query": "label_values(kube_pod_info{namespace!~\"kube-system|istio-system|istio-admin-gateway|istio-tenant-gateway|istio-passthrough-gateway|pepr-system|neuvector|loki|monitoring|grafana|vector|velero|keycloak|authservice|zarf|uds-dev-stack\"}, namespace)",
                        "refId": "StandardVariableQuery"
                    },
                    "type": "query"
                }
            ]
        },
        "time": {
            "from": "now-1h",
            "to": "now"
        },
        "timepicker": {
            "refresh_intervals": [
                "5s",
                "10s",
                "30s",
                "1m",
                "5m",
                "15m",
                "30m",
                "1h"
            ]
        },
        "timezone": "",
        "title": "Istio Sidecar vs Ambient Resource Comparison",
        "description": "A summary of CPU and Memory usage by namespace. Provides estimated resource savings when switching from Sidecar to Ambient mode.",
        "uid": "cemp0hgd41ssge",
        "version": 1
    }
