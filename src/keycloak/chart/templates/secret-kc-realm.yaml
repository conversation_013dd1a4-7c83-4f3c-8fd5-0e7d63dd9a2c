# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: v1
kind: Secret
metadata:
  name: {{ include "keycloak.fullname" . }}-realm-env
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
type: Opaque
data:
  {{- range $key, $value := .Values.realmInitEnv }}
  {{- if eq (typeOf $value) "bool" }}
  REALM_{{ $key }}: {{ toString $value | b64enc }}
  {{- else }}
  REALM_{{ $key }}: {{ $value | b64enc }}
  {{- end }}
  {{- end }}

  SOCIAL_LOGIN_ENABLED: {{ .Values.realmAuthFlows.SOCIAL_AUTH_ENABLED | toString | b64enc }}
  X509_LOGIN_ENABLED: {{ .Values.realmAuthFlows.X509_AUTH_ENABLED | toString | b64enc }}
  USERNAME_PASSWORD_AUTH_ENABLED: {{ .Values.realmAuthFlows.USERNAME_PASSWORD_AUTH_ENABLED | toString | b64enc }}
  REGISTER_BUTTON_ENABLED: {{ or .Values.realmAuthFlows.USERNAME_PASSWORD_AUTH_ENABLED .Values.realmAuthFlows.X509_AUTH_ENABLED | toString | b64enc }}
  DENY_USERNAME_PASSWORD_ENABLED: {{ ternary "DISABLED" "REQUIRED" (.Values.realmAuthFlows.USERNAME_PASSWORD_AUTH_ENABLED) | b64enc }}
  RESET_CREDENTIAL_FLOW_ENABLED: {{ ternary "REQUIRED" "DISABLED" (.Values.realmAuthFlows.USERNAME_PASSWORD_AUTH_ENABLED) | b64enc }}
  REGISTRATION_FORM_ENABLED: {{ ternary "REQUIRED" "DISABLED" (or .Values.realmAuthFlows.USERNAME_PASSWORD_AUTH_ENABLED .Values.realmAuthFlows.X509_AUTH_ENABLED) | b64enc }}
  OTP_ENABLED: {{ .Values.realmAuthFlows.OTP_ENABLED | toString | b64enc }}
  OTP_FLOW_ENABLED: {{ ternary "REQUIRED" "DISABLED" (.Values.realmAuthFlows.OTP_ENABLED) | b64enc}}
  WEBAUTHN_ENABLED: {{ .Values.realmAuthFlows.WEBAUTHN_ENABLED | toString | b64enc }}
  WEBAUTHN_FLOW_ENABLED: {{ ternary "REQUIRED" "DISABLED" (.Values.realmAuthFlows.WEBAUTHN_ENABLED) | b64enc }}
  X509_MFA_ENABLED: {{ .Values.realmAuthFlows.X509_MFA_ENABLED | toString | b64enc }}
  X509_MFA_FLOW_ENABLED: {{ ternary "REQUIRED" "DISABLED" (.Values.realmAuthFlows.X509_MFA_ENABLED) | b64enc }}
  MFA_ENABLED: {{ or .Values.realmAuthFlows.OTP_ENABLED .Values.realmAuthFlows.WEBAUTHN_ENABLED | toString | b64enc }}
  MFA_FLOW_ENABLED: {{ ternary "REQUIRED" "DISABLED" (or .Values.realmAuthFlows.OTP_ENABLED .Values.realmAuthFlows.WEBAUTHN_ENABLED) | b64enc }}
  ENABLE_REGISTRATION_FIELDS: {{ .Values.themeCustomizations.settings.enableRegistrationFields | toString | b64enc }}
  FIPS_ENABLED: {{ .Values.fips | toString | b64enc }}
