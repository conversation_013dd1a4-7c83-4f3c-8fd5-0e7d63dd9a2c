# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: authservice
  namespace: {{ .Release.Namespace }}
spec:
  network:
    serviceMesh:
      mode: ambient
    allow:
      # Permit intra-namespace communication for multi-replica setup
      - direction: Ingress
        remoteGenerated: IntraNamespace

      - direction: Egress
        remoteGenerated: IntraNamespace

      # Egress must be allowed to the external facing Keycloak endpoint
      - direction: Egress
        remoteSelector:
          app: tenant-ingressgateway
        remoteNamespace: istio-tenant-gateway
        description: "SSO Provider"

      {{- if .Values.redis.uri }}
      - direction: Egress
        description: Redis Session Store
        {{- if .Values.redis.internal.enabled }}
        remoteSelector:
          {{- .Values.redis.internal.remoteSelector | toYaml | nindent 10 }}
        remoteNamespace: {{ .Values.redis.internal.remoteNamespace }}
        {{- else if .Values.redis.egressCidr }}
        remoteCidr: {{ .Values.redis.egressCidr }}
        {{- else }}
        remoteGenerated: Anywhere
        {{- end }}
      {{- end }}

      - direction: Ingress
        selector:
          app.kubernetes.io/name: authservice
        remoteNamespace: "" # Any namespace could have a protected app
        port: 10003
        description: "Protected Apps"
