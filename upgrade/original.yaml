apiVersion: v1
kind: Namespace
metadata:
  labels:
    uds: curl-testing-namespace
  name: curl-ns-deny-all-1
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: curl-pkg-deny-all-1
  namespace: curl-ns-deny-all-1
spec:
  network:
    allow: []
    expose: []
---
apiVersion: v1
kind: Service
metadata:
  name: curl-pkg-deny-all-1
  namespace: curl-ns-deny-all-1
  labels:
    name: curl-pkg-deny-all-1
    namespace: curl-ns-deny-all-1
spec:
  ports:
    - name: port8080
      port: 8080
      targetPort: 8080
  selector:
    app: curl-pkg-deny-all-1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl-pkg-deny-all-1
  namespace: curl-ns-deny-all-1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl-pkg-deny-all-1
  template:
    metadata:
      labels:
        app: curl-pkg-deny-all-1
      annotations:
        sidecar.istio.io/proxyCPU: "10m"
        sidecar.istio.io/proxyMemory: "16Mi"
    spec:
      containers:
        - name: curl-pkg-deny-all-1
          image: curlimages/curl
          imagePullPolicy: IfNotPresent
          command: ["sleep", "3600"]
          resources:
            limits:
              cpu: 50m
              memory: 64Mi
            requests:
              cpu: 10m
              memory: 16Mi
          ports:
            - containerPort: 8080