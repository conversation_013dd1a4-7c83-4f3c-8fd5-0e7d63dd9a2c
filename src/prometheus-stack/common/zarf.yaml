# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: uds-core-prometheus-stack-common
  description: "UDS Core Prometheus-Stack Common"

components:
  - name: kube-prometheus-stack
    required: true
    description: "Install kube-prometheus-stack using the helm chart https://github.com/prometheus-community/helm-charts/tree/main/charts/kube-prometheus-stack"
    charts:
      - name: uds-prometheus-config
        namespace: monitoring
        version: 0.1.0
        localPath: ../chart
      - name: kube-prometheus-stack
        namespace: monitoring
        url: https://prometheus-community.github.io/helm-charts
        version: 75.4.0
        valuesFiles:
          - "../values/values.yaml"
    actions:
      onDeploy:
        after:
          - description: Annotate all service and pod monitors to ensure they are mutated for ambient http metrics
            cmd: |
              # This ensures that all monitors go through the latest Pepr mutation code to use the correct http metrics
              ./zarf tools kubectl annotate servicemonitors -A --all uds.dev/prometheus-ambient=true
              ./zarf tools kubectl annotate podmonitors -A --all uds.dev/prometheus-ambient=true
