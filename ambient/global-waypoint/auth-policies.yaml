apiVersion: security.istio.io/v1
kind: AuthorizationPolicy
metadata:
  name: to-httpbin-from-curl1
  namespace: curl-ns
  # Add owner refs to the package
spec:
  targetRefs:
    - kind: ServiceEntry
      group: networking.istio.io
      name: httpbin-service-entry
  action: ALLOW
  rules:
    - from:
        - source:
            serviceAccounts: ["curl1"]
# Ok can we try again on what works/fails for the "to" or "when"?
# FAIL
# when:
#   - key: connection.sni
#     values: ["httpbin.org"]