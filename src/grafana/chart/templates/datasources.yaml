# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasources
  namespace: {{ .Release.Namespace }}
  labels:
    grafana_datasource: "1"
data:
  datasources.yaml: |
    apiVersion: 1
    datasources:
    - access: proxy
      editable: true
      name: Prometheus
      type: prometheus
      url: http://kube-prometheus-stack-prometheus.monitoring.svc.cluster.local:9090
    - access: proxy
      editable: true
      name: Loki
      type: loki
      url: http://loki-gateway.loki.svc.cluster.local:80
    - access: proxy
      editable: true
      name: Alertmanager
      type: alertmanager
      jsonData:
        implementation: prometheus
        handleGrafanaManagedAlerts: true
      url: http://kube-prometheus-stack-alertmanager.monitoring.svc.cluster.local:9093
    {{- if .Values.extraDatasources }}
    {{- toYaml .Values.extraDatasources | nindent 4 }}
    {{- end }}
