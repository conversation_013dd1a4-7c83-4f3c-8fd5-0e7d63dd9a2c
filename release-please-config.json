{"packages": {".": {"release-type": "simple", "draft": false, "changelog-path": "CHANGELOG.md", "changelog-sections": [{"type": "feat", "section": "Features", "hidden": false}, {"type": "fix", "section": "Bug Fixes", "hidden": false}, {"type": "chore", "section": "Miscellaneous", "hidden": false}, {"type": "docs", "section": "Documentation", "hidden": false}], "bump-minor-pre-major": true, "versioning": "default", "extra-files": ["README.md", "packages/checkpoint-dev/zarf.yaml", "packages/base/zarf.yaml", "packages/identity-authorization/zarf.yaml", "packages/logging/zarf.yaml", "packages/backup-restore/zarf.yaml", "packages/runtime-security/zarf.yaml", "packages/monitoring/zarf.yaml", "packages/metrics-server/zarf.yaml", "packages/standard/zarf.yaml", "bundles/k3d-slim-dev/uds-bundle.yaml", "bundles/k3d-standard/uds-bundle.yaml", ".github/bundles/rke2/uds-bundle.yaml", ".github/bundles/eks/uds-bundle.yaml", ".github/bundles/aks/uds-bundle.yaml", "tasks/deploy.yaml", "tasks/publish.yaml"]}}}