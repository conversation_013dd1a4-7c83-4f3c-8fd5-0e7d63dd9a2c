# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: Publish UDS Core

# Permissions for the GITHUB_TOKEN used by the workflow.
permissions:
  contents: read # Allows reading the content of the repository.
  packages: read # Allows reading the content of the repository's packages.
  id-token: write

on:
  # triggered by tag-and-release.yaml and snapshot-release.yaml
  workflow_call:
    inputs:
      snapshot:
        description: 'true - for snapshot release'
        required: true
        type: boolean

jobs:
  publish-uds-core:
    strategy:
      fail-fast: false
      matrix:
        flavor: [upstream, registry1, unicorn]
    runs-on: "uds-ubuntu-big-boy-8-core"
    name: Publish packages

    permissions:
      contents: read
      packages: write
      id-token: write  # This is needed for OIDC federation.

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Environment setup
        uses: ./.github/actions/setup
        with:
          registry1Username: ${{ secrets.IRON_BANK_ROBOT_USERNAME }}
          registry1Password: ${{ secrets.IRON_BANK_ROBOT_PASSWORD }}
          ghToken: ${{ secrets.GITHUB_TOKEN }}
          rapidfortUsername: ${{ secrets.RAPIDFORT_USERNAME }}
          rapidfortPassword: ${{ secrets.RAPIDFORT_PASSWORD }}

      - name: (Snapshot) Get snapshot version using git commit short sha and date
        if: ${{ inputs.snapshot }}
        run: |
          SHORT_SHA=$(git rev-parse --short HEAD)
          RELEASE_DATE=$(date +'%Y-%m-%d')
          echo "SNAPSHOT_VERSION=${RELEASE_DATE}-${SHORT_SHA}" >> $GITHUB_ENV
          echo "PUBLISH_ARGS=--set VERSION=${RELEASE_DATE}-${SHORT_SHA}" >> $GITHUB_ENV

      - name: (Snapshot) Set versions to snapshot
        if: ${{ inputs.snapshot }}
        run: |
          yq -ei '.metadata.version=env(SNAPSHOT_VERSION), (.packages[]|select(has("ref"))|select(.name=="core")).ref=env(SNAPSHOT_VERSION)' bundles/k3d-standard/uds-bundle.yaml
          yq -ei '.metadata.version=env(SNAPSHOT_VERSION), (.packages[]|select(has("ref"))|select(.name=="core-base")).ref=env(SNAPSHOT_VERSION)' bundles/k3d-slim-dev/uds-bundle.yaml
          yq -ei '.metadata.version=env(SNAPSHOT_VERSION), (.packages[]|select(has("ref"))|select(.name=="core-identity-authorization")).ref=env(SNAPSHOT_VERSION)' bundles/k3d-slim-dev/uds-bundle.yaml
          yq -ei '.metadata.version=env(SNAPSHOT_VERSION)' packages/standard/zarf.yaml
          yq -ei '.metadata.version=env(SNAPSHOT_VERSION)' packages/base/zarf.yaml
          yq -ei '.metadata.version=env(SNAPSHOT_VERSION)' packages/identity-authorization/zarf.yaml

      - name: Create Packages and Bundles
        run: |
          ZARF_ARCHITECTURE=amd64 uds run -f tasks/create.yaml standard-package --no-progress --set FLAVOR=${{ matrix.flavor }}
          ZARF_ARCHITECTURE=amd64 uds run -f tasks/create.yaml k3d-standard-bundle --no-progress
          ZARF_ARCHITECTURE=amd64 uds run -f tasks/create.yaml k3d-slim-dev-bundle --no-progress --set FLAVOR=${{ matrix.flavor }}

          ZARF_ARCHITECTURE=arm64 uds run -f tasks/create.yaml standard-package --no-progress --set FLAVOR=${{ matrix.flavor }}
          ZARF_ARCHITECTURE=arm64 uds run -f tasks/create.yaml k3d-standard-bundle --no-progress
          ZARF_ARCHITECTURE=arm64 uds run -f tasks/create.yaml k3d-slim-dev-bundle --no-progress --set FLAVOR=${{ matrix.flavor }}

      # Standard Package by default tests full core
      - name: Test amd64 Bundle
        if: ${{ !inputs.snapshot }}
        run: |
          uds run deploy-standard-bundle --no-progress
          uds run -f tasks/test.yaml validate-packages --no-progress

      - name: Debug Output
        if: ${{ always() && !inputs.snapshot }}
        uses: ./.github/actions/debug-output

      # Publish package and bundle to destination repository
      - name: Publish Standard Package
        run: uds run -f tasks/publish.yaml standard-package --set FLAVOR=${{ matrix.flavor }} --set SNAPSHOT="${{ inputs.snapshot }}" ${PUBLISH_ARGS} --no-progress

      - name: Publish Upstream Flavored Bundles
        if: ${{ matrix.flavor == 'upstream' }}
        run: uds run -f tasks/publish.yaml bundles --set SNAPSHOT="${{ inputs.snapshot }}" ${PUBLISH_ARGS} --no-progress

      - name: Save logs
        if: always()
        uses: ./.github/actions/save-logs
        with:
          suffix: -${{ matrix.flavor }}

  publish-uds-core-layers:
    if: ${{ !inputs.snapshot }}
    strategy:
      fail-fast: false
      matrix:
        flavor: [upstream, registry1, unicorn]
        layer: [base, identity-authorization, runtime-security, backup-restore, logging, metrics-server, monitoring]
        arch: [amd64, arm64]
    runs-on: ${{ matrix.arch == 'arm64' && 'uds-ubuntu-24-arm64-4-core' || 'uds-ubuntu-big-boy-4-core' }}
    name: Publish package layers

    permissions:
      contents: read
      packages: write
      id-token: write  # This is needed for OIDC federation.

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Environment setup
        uses: ./.github/actions/setup
        with:
          registry1Username: ${{ secrets.IRON_BANK_ROBOT_USERNAME }}
          registry1Password: ${{ secrets.IRON_BANK_ROBOT_PASSWORD }}
          ghToken: ${{ secrets.GITHUB_TOKEN }}
          rapidfortUsername: ${{ secrets.RAPIDFORT_USERNAME }}
          rapidfortPassword: ${{ secrets.RAPIDFORT_PASSWORD }}

      - name: Test and Publish Core Package Layer
        run: uds run -f tasks/publish.yaml single-layer --set FLAVOR=${{ matrix.flavor }} --set LAYER=${{ matrix.layer }} --no-progress

      - name: Debug Output
        if: ${{ always() && !inputs.snapshot }}
        uses: ./.github/actions/debug-output

      - name: Save logs
        if: always()
        uses: ./.github/actions/save-logs
        with:
          suffix: -${{ matrix.flavor }}-${{ matrix.layer }}-${{ matrix.arch }}
