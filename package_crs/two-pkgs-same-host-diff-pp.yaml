apiVersion: v1
kind: Namespace
metadata:
  name: ns-1
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-1
  namespace: ns-1
spec:
  network: 
    allow:
    - direction: Egress
      selector:
        app.kubernetes.io/name: vector
      description: "Elastic Storage"
      remoteHost: "svc.elastic.com"
      remoteProtocol: TLS
      port: 443
---
apiVersion: v1
kind: Namespace
metadata:
  name: ns-2
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-2
  namespace: ns-2
spec:
  network: 
    allow:
    - direction: Egress
      selector:
        app.kubernetes.io/name: vector
      description: "Elastic Storage"
      remoteHost: "svc.elastic.com"
      remoteProtocol: HTTP
      port: 80