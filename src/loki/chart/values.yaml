# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

storage:
  internal:
    enabled: false
    remoteSelector: {}
    remoteNamespace: ""
  egressCidr: ""

dashboardAnnotations: {}

# Support for custom `network.allow` entries on the Package CR, useful for extra datasources
additionalNetworkAllow: []
# ref: https://uds.defenseunicorns.com/reference/configuration/custom-resources/packages-v1alpha1-cr/#allow
#   - direction: Egress
#     selector:
#       app.kubernetes.io/name: loki
#     remoteCidr: **************
#     description: "Cache"
#     port: 6379
