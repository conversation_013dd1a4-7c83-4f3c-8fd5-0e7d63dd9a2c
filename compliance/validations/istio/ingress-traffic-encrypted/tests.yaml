# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

pass:
  - test: default
    validation: validation.yaml
    resources: resources.json
    expected-validation: true
  - test: change_admin_gateway_protocol_to_tcp
    validation: validation.yaml
    resources: resources.json
    permutation: '.gateways |= map(if .metadata.name == "admin-gateway" then .spec.servers[0].port.protocol = "TCP" else . end)'
    expected-validation: false
  - test: change_admin_gateway_httpsRedirect_to_false
    validation: validation.yaml
    resources: resources.json
    permutation: '.gateways |= map(if .metadata.name == "admin-gateway" then .spec.servers[0].tls.httpsRedirect = false else . end)'
    expected-validation: false
