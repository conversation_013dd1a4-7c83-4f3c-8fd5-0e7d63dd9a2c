# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

pass:
  - test: default
    validation: validation.yaml
    resources: resources.json
    expected-validation: true
  - test: remove_first_gateway
    validation: validation.yaml
    resources: resources.json
    permutation: "del(.gateways[0])"
    expected-validation: false
  - test: add_new_gateway
    validation: validation.yaml
    resources: resources.json
    permutation: '.gateways += [{"apiVersion": "networking.istio.io/v1beta1", "kind": "Gateway", "metadata": {"name": "new-gateway", "namespace": "new-namespace"}}]'
    expected-validation: false
