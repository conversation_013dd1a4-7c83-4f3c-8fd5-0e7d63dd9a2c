# Should this be added, per remoteHost
# Then something else in "allow" that specifies the egress gateway selector (i.e., the actual node LB)
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: change-me-egressgateway
  namespace: default
  labels:
    app: istio-egressgateway
    release: release-name
spec:
  selector:
    app: egressgateway
  servers:
    - port:
        number: 443
        name: https
        protocol: HTTPS
      hosts:
        - "change-me.com"
      tls:
        mode: PASSTHROUGH