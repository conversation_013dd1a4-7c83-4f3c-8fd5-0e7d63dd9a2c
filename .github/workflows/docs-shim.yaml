# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: CI Docs

# Permissions for the GITHUB_TOKEN used by the workflow.
permissions:
  contents: read # Allows reading the content of the repository.

on:
  pull_request:
    # milestoned is added here as a workaround for release-please not triggering PR workflows (PRs should be added to a milestone to trigger the workflow).
    # labeled is added to support renovate-ready labelling on PRs
    types: [milestoned, labeled, opened, reopened, synchronize]
    paths:
      - "**.md"
      - "**.jpg"
      - "**.png"
      - "**.gif"
      - "**.svg"
      - docs/**
      - .vscode/**
      - .gitignore
      - renovate.json
      - .codespellrc
      - .release-please-config.json
      - release-please-config.json
      - CODEOWNERS
      - LICENSE
      - scripts/** # scripts/hacks that are used specifically for non-testing workflows

jobs:
  lint-check:
    runs-on: ubuntu-latest
    # Job-level permissions override workflow-level permissions
    permissions:
      contents: read # Allows reading the content of the repository.
      pull-requests: write # Allows writing to pull requests (needed for renovate-readiness)
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Check renovate readiness
        if: startsWith(github.event.pull_request.head.ref, 'renovate/') # Only call for Renovate PRs
        uses: ./.github/actions/renovate-readiness
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: lint-check
        uses: ./.github/actions/lint-check

  autogenerated-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: autogenerated-check
        uses: ./.github/actions/autogenerated-check

  run-package-test:
    needs: [lint-check, autogenerated-check]
    name: Schedule
    strategy:
      matrix:
        package: [all]
        flavor: [upstream, registry1, unicorn]
        test_type: [install, upgrade]
    uses: ./.github/workflows/test-shim.yaml
    with:
      package: ${{ matrix.package }}
      flavor: ${{ matrix.flavor }}
      test_type: ${{ matrix.test_type }}
    secrets: inherit # Inherits all secrets from the parent workflow.
