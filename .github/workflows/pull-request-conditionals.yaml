# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: Filter

# This workflow is triggered on pull requests
on:
  pull_request:
    # milestoned is added here as a workaround for release-please not triggering PR workflows (PRs should be added to a milestone to trigger the workflow).
    # labeled is added to support renovate-ready labelling on PRs
    types: [milestoned, labeled, opened, reopened, synchronize]
    paths-ignore:
      - "**.md"
      - "**.jpg"
      - "**.png"
      - "**.gif"
      - "**.svg"
      - docs/**
      - .vscode/**
      - .gitignore
      - renovate.json
      - .release-please-config.json
      - .codespellrc
      - release-please-config.json
      - CODEOWNERS
      - LICENSE
      - scripts/** # scripts/hacks that are used specifically for non-testing workflows

# Permissions for the GITHUB_TOKEN used by the workflow.
permissions:
  id-token: write # Needed for OIDC-related operations.
  contents: read # Allows reading the content of the repository.
  pull-requests: write # Allows writing pull request metadata.
  packages: read # Allows reading the published GHCR packages

# Default settings for all run commands in the workflow jobs.
defaults:
  run:
    shell: bash -e -o pipefail {0} # Ensures that scripts fail on error and pipefail is set.

# Abort prior jobs in the same workflow / PR
concurrency:
  group: test-${{ github.ref }}
  cancel-in-progress: true

jobs:
  lint-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: lint-check
        uses: ./.github/actions/lint-check

  autogenerated-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: autogenerated-check
        uses: ./.github/actions/autogenerated-check

  # This job checks if there are changes in specific paths source packages.
  check-paths:
    needs: [lint-check, autogenerated-check]
    runs-on: ubuntu-latest
    name: Select Jobs
    outputs:
      combined: ${{ steps.combine-path-filters.outputs.combined }}
      distros: ${{ steps.path-filter-iac.outputs.changes }}

    steps:
      - name: Checkout the code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Check renovate readiness
        if: startsWith(github.event.pull_request.head.ref, 'renovate/') # Only call for Renovate PRs
        uses: ./.github/actions/renovate-readiness
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

      # Add a custom filter for all packages that excludes any changes to IaC testing related config
      # Noting that adding `predicate-quantifier` produces a false warning message about an unexpected input, despite still being evaluated by the action
      - name: Check src paths - all
        id: path-filter-all
        uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36 # v3
        with:
          predicate-quantifier: "every"
          filters: |
            all:
              - "**"
              - "!.github/workflows/test-aks.yaml"
              - "!.github/workflows/test-eks.yaml"
              - "!.github/workflows/test-rke2.yaml"
              - "!.github/bundles/**"
              - "!.github/test-infra/**"
              - "!tasks/iac.yaml"

      # Uses a custom action to filter paths for source packages.
      - name: Check src paths - packages
        id: path-filter
        uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36 # v3
        with:
          filters: .github/filters.yaml

      - name: Merge Path Filter Outputs for Packages
        id: combine-path-filters
        run: |
          allPackageChanges='${{ steps.path-filter-all.outputs.changes }}'
          packageChanges='${{ steps.path-filter.outputs.changes }}'
          COMBINED=$(echo $allPackageChanges $packageChanges | jq -s 'add')
          echo combined=$COMBINED >> $GITHUB_OUTPUT
          echo The following packages will be triggered: $COMBINED

  # This job triggers a separate workflow for each changed source package, if any.
  run-package-test:
    needs: check-paths
    if: ${{ fromJson(needs.check-paths.outputs.combined) }}
    name: Schedule
    strategy:
      matrix:
        package: ${{ fromJson(needs.check-paths.outputs.combined) }}
        flavor: [upstream, registry1, unicorn]
        test_type: [install]
        # Upgrade tests are included for all flavors, but ONLY for `all` package tests
        include:
          - package: all
            flavor: registry1
            test_type: upgrade
          - package: all
            flavor: upstream
            test_type: upgrade
          - package: all
            flavor: unicorn
            test_type: upgrade
    uses: ./.github/workflows/test.yaml
    with:
      package: ${{ matrix.package }}
      flavor: ${{ matrix.flavor }}
      test_type: ${{ matrix.test_type }}
    secrets: inherit # Inherits all secrets from the parent workflow.

# evaluate-package-compliance:
#   needs: run-package-test
#   name: Compliance Evaluation
#   strategy:
#     matrix:
#       flavor: [upstream, registry1, unicorn]
#     fail-fast: false
#   uses: ./.github/workflows/compliance.yaml
#   with:
#     flavor: ${{ matrix.flavor }}
#   secrets: inherit # Inherits all secrets from the parent workflow.
