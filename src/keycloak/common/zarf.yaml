# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: uds-core-keycloak-common
  description: "UDS Keycloak Common"
  url: https://github.com/keycloak/keycloak

components:
  - name: keycloak
    charts:
      - name: keycloak
        namespace: keycloak
        # renovate: datasource=docker depName=quay.io/keycloak/keycloak versioning=semver
        version: 26.2.5
        localPath: ../chart
        valuesFiles:
          - ../chart/values.yaml
    actions:
      onDeploy:
        after:
          - description: Validate Keycloak Pods
            wait:
              cluster:
                kind: Pod
                name: app.kubernetes.io/name=keycloak
                condition: Ready
                namespace: keycloak
