# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial
profile: ambient

meshConfig:
  accessLogFile: /dev/stdout
  pathNormalization:
    normalization: MERGE_SLASHES
  defaultConfig:
    holdApplicationUntilProxyStarts: true
    gatewayTopology:
      forwardClientCertDetails: SANITIZE
  extensionProviders:
    - name: "authservice"
      envoyExtAuthzGrpc:
        service: "authservice.authservice.svc.cluster.local"
        port: "10003"
  outboundTrafficPolicy:
    mode: "ALLOW_ANY"

pilot:
  env:
    PILOT_JWT_ENABLE_REMOTE_JWKS: hybrid
    ENABLE_NATIVE_SIDECARS: true

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
              - key: app
                operator: In
                values:
                  - istiod
          topologyKey: kubernetes.io/hostname
