# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

{{- if .Capabilities.APIVersions.Has "security.istio.io/v1beta1" }}
apiVersion: "security.istio.io/v1beta1"
kind: PeerAuthentication
metadata:
  name: controller-neuvector
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: neuvector-controller-pod
  mtls:
    mode: STRICT
  portLevelMtls:
    "18300":
      mode: PERMISSIVE
    # Allow webhooks to operate permissive since ingress originates from the nodes
    "30443":
      mode: PERMISSIVE
{{- end }}
