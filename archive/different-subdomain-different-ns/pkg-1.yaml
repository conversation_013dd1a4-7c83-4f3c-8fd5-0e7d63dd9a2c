apiVersion: v1
kind: Namespace
metadata:
  name: different-subdomain-ns-1
  labels:
    istio-injection: enabled
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: different-subdomain-ns-pkg-1
  namespace: different-subdomain-ns-1
spec:
  network: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: curl1
  namespace: different-subdomain-ns-1
---
apiVersion: v1
kind: Service
metadata:
  name: curl1
  namespace: different-subdomain-ns-1
  labels:
    app: curl1
    service: curl1
spec:
  ports:
  - port: 80
    name: http
  selector:
    app: curl1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl1
  namespace: different-subdomain-ns-1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl1
  template:
    metadata:
      labels:
        app: curl1
    spec:
      terminationGracePeriodSeconds: 0
      serviceAccountName: curl1
      containers:
      - name: curl
        image: curlimages/curl
        command: ["/bin/sleep", "infinity"]
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - mountPath: /etc/curl/tls
          name: secret-volume
      volumes:
      - name: secret-volume
        secret:
          secretName: curl-secret
          optional: true
