# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: Release UDS Core

# Permissions for the GITHUB_TOKEN used by the workflow.
permissions:
  contents: read # Allows reading the content of the repository.
  packages: read # Allows reading the content of the repository's packages.
  id-token: write

on:
  push:
    branches:
      - main

jobs:
  tag-new-version:
    permissions: write-all
    runs-on: ubuntu-latest
    outputs:
      release_created: ${{ steps.release-flag.outputs.release_created }}
    steps:
      - name: Create release tag
        id: tag
        uses: googleapis/release-please-action@a02a34c4d625f9be7cb89156071d8567266a2445 # v4.2.0
      - id: release-flag
        run: echo "release_created=${{ steps.tag.outputs.release_created || false }}" >> $GITHUB_OUTPUT

  publish-uds-core-release:
    needs: tag-new-version
    if: ${{ needs.tag-new-version.outputs.release_created == 'true'}}
    permissions:
      contents: write
      packages: write
      id-token: write
    uses: ./.github/workflows/publish.yaml
    with:
      snapshot: false
    secrets: inherit

  scan-release:
    needs: publish-uds-core-release
    permissions:
      contents: read
      packages: read
      id-token: write
      issues: write
    uses: ./.github/workflows/cve-scan.yaml
    with:
      release: true
    secrets: inherit

  checkpoint-uds-core-release:
    needs: publish-uds-core-release
    permissions:
      contents: read
      packages: write
      id-token: write
      pull-requests: write
    uses: ./.github/workflows/checkpoint.yaml
    secrets: inherit
