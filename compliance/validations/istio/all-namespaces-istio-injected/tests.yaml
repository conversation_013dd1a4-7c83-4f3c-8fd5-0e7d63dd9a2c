# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

pass:
  - test: default
    validation: validation.yaml
    resources: resources.json
    expected-validation: true
  - test: grafana-no-istio-injection
    validation: validation.yaml
    resources: resources.json
    permutation: '.namespaces |= map(if .metadata.name == "grafana" then del(.metadata.labels["istio-injection"]) else . end)'
    expected-validation: false
