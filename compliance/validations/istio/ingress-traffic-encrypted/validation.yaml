# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

metadata:
  name: ingress-traffic-encrypted
  uuid: fd071676-6b92-4e1c-a4f0-4c8d2bd55aed
domain:
  type: kubernetes
  kubernetes-spec:
    resources:
      - name: gateways
        resource-rule:
          group: networking.istio.io
          version: v1beta1
          resource: gateways
          namespaces: []
provider:
  type: opa
  opa-spec:
    rego: |
      package validate
      import rego.v1

      # Default values
      default validate := false
      default msg := "Not evaluated"

      # Validation
      validate if {
        check_gateways_allowed.result
      }
      msg = check_gateways_allowed.msg
      msg_exempted_gateways = concat(", ", exempt_gateways)

      # Collect gateways that do not encrypt ingress traffic
      gateways_disallowed = {sprintf("%s/%s", [gateway.metadata.namespace, gateway.metadata.name]) |
        gateway := input.gateways[_];
        not allowed_gateway(gateway)
      }

      check_gateways_allowed = {"result": true, "msg": msg} if {
        count(gateways_disallowed) == 0
        msg := "All gateways encrypt ingress traffic"
      } else = {"result": false, "msg": msg} if {
        msg := sprintf("Some gateways do not encrypt ingress traffic: %s", [concat(", ", gateways_disallowed)])
      }

      # Check allowed gateway
      allowed_gateway(gateway) if {
        every server in gateway.spec.servers {
          allowed_server(server)
        }
      }

      exempt_gateways := {"istio-passthrough-gateway/passthrough-gateway"}
      allowed_gateway(gateway) if {
        sprintf("%s/%s", [gateway.metadata.namespace, gateway.metadata.name]) in exempt_gateways
        # *Unchecked condition that exempted gateway is only used by virtual services that route https traffic
        # Find all virtual services that use this gateway
        # Check that vs has https scheme
      }

      # Check allowed server spec in gateway
      allowed_server(server) if {
        server.port.protocol == "HTTP"
        server.tls.httpsRedirect == true
      }

      allowed_server(server) if {
        server.port.protocol == "HTTPS"
        server.tls.mode in {"SIMPLE", "OPTIONAL_MUTUAL"}
      }
    output:
      validation: validate.validate
      observations:
        - validate.msg
        - validate.msg_exempted_gateways
