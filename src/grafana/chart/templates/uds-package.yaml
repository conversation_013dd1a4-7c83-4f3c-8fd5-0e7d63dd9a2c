# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: grafana
  namespace: {{ .Release.Namespace }}
spec:
  sso:
    - name: Grafana
      clientId: uds-core-admin-grafana
      redirectUris:
        {{- if .Values.adminDomain }}
        - "https://grafana.{{ .Values.adminDomain }}/login/generic_oauth"
        - "https://grafana.{{ .Values.adminDomain }}/login"
        {{- else }}
        - "https://grafana.admin.{{ .Values.domain }}/login/generic_oauth"
        - "https://grafana.admin.{{ .Values.domain }}/login"
        {{- end }}

  monitor:
    - selector:
        app.kubernetes.io/name: grafana
      targetPort: 3000
      portName: service
      description: Metrics

  network:
    serviceMesh:
      mode: ambient
    expose:
      - service: grafana
        selector:
          app.kubernetes.io/name: grafana
        host: grafana
        gateway: admin
        port: 80
        targetPort: 3000

    allow:
      # Egress allowed to Loki
      - direction: Egress
        selector:
          app.kubernetes.io/name: grafana
        remoteNamespace: loki
        remoteSelector:
          app.kubernetes.io/name: loki
        description: "Loki Datasource"
        port: 8080

      # Egress allowed to Prometheus
      - direction: Egress
        selector:
          app.kubernetes.io/name: grafana
        remoteNamespace: monitoring
        remoteSelector:
          app.kubernetes.io/name: prometheus
        description: "Prometheus Datasource"
        port: 9090

      # Egress allowed to Alertmanager
      - direction: Egress
        selector:
          app.kubernetes.io/name: grafana
        remoteNamespace: monitoring
        remoteSelector:
          app.kubernetes.io/name: alertmanager
        selector:
          app.kubernetes.io/name: grafana
        description: "Alertmanager Datasource"
        port: 9093

      # Egress must be allowed to the external facing Keycloak endpoint
      - direction: Egress
        selector:
          app.kubernetes.io/name: grafana
        remoteSelector:
          app: tenant-ingressgateway
        remoteNamespace: istio-tenant-gateway
        description: "SSO Provider"

      # Egress allowed to KubeAPI
      - direction: Egress
        selector:
          app.kubernetes.io/name: grafana
        remoteGenerated: KubeAPI
        description: "Datasources Watcher"

      {{- if eq (include "grafana.postgresql.config" .) "true" }}
      - description: "Postgresql access"
        direction: Egress
        selector:
          app.kubernetes.io/name: grafana
        port: {{ .Values.postgresql.port }}
        {{- if .Values.postgresql.internal.enabled }}
        remoteSelector:
          {{- .Values.postgresql.internal.remoteSelector | toYaml | nindent 10 }}
        remoteNamespace: {{ .Values.postgresql.internal.remoteNamespace }}
        {{- else if .Values.postgresql.egressCidr }}
        remoteCidr: {{ .Values.postgresql.egressCidr }}
        {{- else }}
        remoteGenerated: Anywhere
        {{- end }}
      {{- end }}

      # HA for Grafana
      - direction: Ingress
        remoteGenerated: IntraNamespace
        ports:
          - 3000
      - direction: Egress
        remoteGenerated: IntraNamespace
        ports:
          - 3000

      # Custom rules for additional networking access
      {{- with .Values.additionalNetworkAllow }}
      {{ toYaml . | nindent 6 }}
      {{- end }}
