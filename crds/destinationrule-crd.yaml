apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    helm.sh/resource-policy: keep
    meta.helm.sh/release-name: base
    meta.helm.sh/release-namespace: istio-system
  creationTimestamp: "2025-03-10T19:25:39Z"
  generation: 1
  labels:
    app.kubernetes.io/instance: base
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: istio
    app.kubernetes.io/version: 1.24.3
    helm.sh/chart: base-1.24.3
  name: destinationrules.networking.istio.io
  resourceVersion: "1206"
  uid: e4a9653a-eaac-4396-90cf-3cadbf083f62
spec:
  conversion:
    strategy: None
  group: networking.istio.io
  names:
    categories:
    - istio-io
    - networking-istio-io
    kind: DestinationRule
    listKind: DestinationRuleList
    plural: destinationrules
    shortNames:
    - dr
    singular: destinationrule
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The name of a service from the service registry
      jsonPath: .spec.host
      name: Host
      type: string
    - description: 'CreationTimestamp is a timestamp representing the server time
        when this object was created. It is not guaranteed to be set in happens-before
        order across separate operations. Clients may not set this value. It is represented
        in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for
        lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata'
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        properties:
          spec:
            description: 'Configuration affecting load balancing, outlier detection,
              etc. See more details at: https://istio.io/docs/reference/config/networking/destination-rule.html'
            properties:
              exportTo:
                description: A list of namespaces to which this destination rule is
                  exported.
                items:
                  type: string
                type: array
              host:
                description: The name of a service from the service registry.
                type: string
              subsets:
                description: One or more named sets that represent individual versions
                  of a service.
                items:
                  properties:
                    labels:
                      additionalProperties:
                        type: string
                      description: Labels apply a filter over the endpoints of a service
                        in the service registry.
                      type: object
                    name:
                      description: Name of the subset.
                      type: string
                    trafficPolicy:
                      description: Traffic policies that apply to this subset.
                      properties:
                        connectionPool:
                          properties:
                            http:
                              description: HTTP connection pool settings.
                              properties:
                                h2UpgradePolicy:
                                  description: |-
                                    Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                                    Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                                  enum:
                                  - DEFAULT
                                  - DO_NOT_UPGRADE
                                  - UPGRADE
                                  type: string
                                http1MaxPendingRequests:
                                  description: Maximum number of requests that will
                                    be queued while waiting for a ready connection
                                    pool connection.
                                  format: int32
                                  type: integer
                                http2MaxRequests:
                                  description: Maximum number of active requests to
                                    a destination.
                                  format: int32
                                  type: integer
                                idleTimeout:
                                  description: The idle timeout for upstream connection
                                    pool connections.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConcurrentStreams:
                                  description: The maximum number of concurrent streams
                                    allowed for a peer on one HTTP/2 connection.
                                  format: int32
                                  type: integer
                                maxRequestsPerConnection:
                                  description: Maximum number of requests per connection
                                    to a backend.
                                  format: int32
                                  type: integer
                                maxRetries:
                                  description: Maximum number of retries that can
                                    be outstanding to all hosts in a cluster at a
                                    given time.
                                  format: int32
                                  type: integer
                                useClientProtocol:
                                  description: If set to true, client protocol will
                                    be preserved while initiating connection to backend.
                                  type: boolean
                              type: object
                            tcp:
                              description: Settings common to both HTTP and TCP upstream
                                connections.
                              properties:
                                connectTimeout:
                                  description: TCP connection timeout.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                idleTimeout:
                                  description: The idle timeout for TCP connections.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConnectionDuration:
                                  description: The maximum duration of a connection.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConnections:
                                  description: Maximum number of HTTP1 /TCP connections
                                    to a destination host.
                                  format: int32
                                  type: integer
                                tcpKeepalive:
                                  description: If set then set SO_KEEPALIVE on the
                                    socket to enable TCP Keepalives.
                                  properties:
                                    interval:
                                      description: The time duration between keep-alive
                                        probes.
                                      type: string
                                      x-kubernetes-validations:
                                      - message: must be a valid duration greater
                                          than 1ms
                                        rule: duration(self) >= duration('1ms')
                                    probes:
                                      description: Maximum number of keepalive probes
                                        to send without response before deciding the
                                        connection is dead.
                                      maximum: **********
                                      minimum: 0
                                      type: integer
                                    time:
                                      description: The time duration a connection
                                        needs to be idle before keep-alive probes
                                        start being sent.
                                      type: string
                                      x-kubernetes-validations:
                                      - message: must be a valid duration greater
                                          than 1ms
                                        rule: duration(self) >= duration('1ms')
                                  type: object
                              type: object
                          type: object
                        loadBalancer:
                          description: Settings controlling the load balancer algorithms.
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - simple
                              - required:
                                - consistentHash
                          - required:
                            - simple
                          - required:
                            - consistentHash
                          properties:
                            consistentHash:
                              allOf:
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - httpHeaderName
                                    - required:
                                      - httpCookie
                                    - required:
                                      - useSourceIp
                                    - required:
                                      - httpQueryParameterName
                                - required:
                                  - httpHeaderName
                                - required:
                                  - httpCookie
                                - required:
                                  - useSourceIp
                                - required:
                                  - httpQueryParameterName
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - ringHash
                                    - required:
                                      - maglev
                                - required:
                                  - ringHash
                                - required:
                                  - maglev
                              properties:
                                httpCookie:
                                  description: Hash based on HTTP cookie.
                                  properties:
                                    name:
                                      description: Name of the cookie.
                                      type: string
                                    path:
                                      description: Path to set for the cookie.
                                      type: string
                                    ttl:
                                      description: Lifetime of the cookie.
                                      type: string
                                  required:
                                  - name
                                  type: object
                                httpHeaderName:
                                  description: Hash based on a specific HTTP header.
                                  type: string
                                httpQueryParameterName:
                                  description: Hash based on a specific HTTP query
                                    parameter.
                                  type: string
                                maglev:
                                  description: The Maglev load balancer implements
                                    consistent hashing to backend hosts.
                                  properties:
                                    tableSize:
                                      description: The table size for Maglev hashing.
                                      minimum: 0
                                      type: integer
                                  type: object
                                minimumRingSize:
                                  description: Deprecated.
                                  minimum: 0
                                  type: integer
                                ringHash:
                                  description: The ring/modulo hash load balancer
                                    implements consistent hashing to backend hosts.
                                  properties:
                                    minimumRingSize:
                                      description: The minimum number of virtual nodes
                                        to use for the hash ring.
                                      minimum: 0
                                      type: integer
                                  type: object
                                useSourceIp:
                                  description: Hash based on the source IP address.
                                  type: boolean
                              type: object
                            localityLbSetting:
                              properties:
                                distribute:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating locality, '/' separated,
                                          e.g.
                                        type: string
                                      to:
                                        additionalProperties:
                                          maximum: **********
                                          minimum: 0
                                          type: integer
                                        description: Map of upstream localities to
                                          traffic distribution weights.
                                        type: object
                                    type: object
                                  type: array
                                enabled:
                                  description: Enable locality load balancing.
                                  nullable: true
                                  type: boolean
                                failover:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating region.
                                        type: string
                                      to:
                                        description: Destination region the traffic
                                          will fail over to when endpoints in the
                                          'from' region becomes unhealthy.
                                        type: string
                                    type: object
                                  type: array
                                failoverPriority:
                                  description: failoverPriority is an ordered list
                                    of labels used to sort endpoints to do priority
                                    based load balancing.
                                  items:
                                    type: string
                                  type: array
                              type: object
                            simple:
                              description: |2-


                                Valid Options: LEAST_CONN, RANDOM, PASSTHROUGH, ROUND_ROBIN, LEAST_REQUEST
                              enum:
                              - UNSPECIFIED
                              - LEAST_CONN
                              - RANDOM
                              - PASSTHROUGH
                              - ROUND_ROBIN
                              - LEAST_REQUEST
                              type: string
                            warmup:
                              description: Represents the warmup configuration of
                                Service.
                              properties:
                                aggression:
                                  description: This parameter controls the speed of
                                    traffic increase over the warmup duration.
                                  format: double
                                  minimum: 1
                                  nullable: true
                                  type: number
                                duration:
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                minimumPercent:
                                  format: double
                                  maximum: 100
                                  minimum: 0
                                  nullable: true
                                  type: number
                              required:
                              - duration
                              type: object
                            warmupDurationSecs:
                              description: 'Deprecated: use `warmup` instead.'
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                          type: object
                        outlierDetection:
                          properties:
                            baseEjectionTime:
                              description: Minimum ejection duration.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            consecutive5xxErrors:
                              description: Number of 5xx errors before a host is ejected
                                from the connection pool.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            consecutiveErrors:
                              format: int32
                              type: integer
                            consecutiveGatewayErrors:
                              description: Number of gateway errors before a host
                                is ejected from the connection pool.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            consecutiveLocalOriginFailures:
                              description: The number of consecutive locally originated
                                failures before ejection occurs.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            interval:
                              description: Time interval between ejection sweep analysis.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            maxEjectionPercent:
                              description: Maximum % of hosts in the load balancing
                                pool for the upstream service that can be ejected.
                              format: int32
                              type: integer
                            minHealthPercent:
                              description: Outlier detection will be enabled as long
                                as the associated load balancing pool has at least
                                `minHealthPercent` hosts in healthy mode.
                              format: int32
                              type: integer
                            splitExternalLocalOriginErrors:
                              description: Determines whether to distinguish local
                                origin failures from external errors.
                              type: boolean
                          type: object
                        portLevelSettings:
                          description: Traffic policies specific to individual ports.
                          items:
                            properties:
                              connectionPool:
                                properties:
                                  http:
                                    description: HTTP connection pool settings.
                                    properties:
                                      h2UpgradePolicy:
                                        description: |-
                                          Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                                          Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                                        enum:
                                        - DEFAULT
                                        - DO_NOT_UPGRADE
                                        - UPGRADE
                                        type: string
                                      http1MaxPendingRequests:
                                        description: Maximum number of requests that
                                          will be queued while waiting for a ready
                                          connection pool connection.
                                        format: int32
                                        type: integer
                                      http2MaxRequests:
                                        description: Maximum number of active requests
                                          to a destination.
                                        format: int32
                                        type: integer
                                      idleTimeout:
                                        description: The idle timeout for upstream
                                          connection pool connections.
                                        type: string
                                        x-kubernetes-validations:
                                        - message: must be a valid duration greater
                                            than 1ms
                                          rule: duration(self) >= duration('1ms')
                                      maxConcurrentStreams:
                                        description: The maximum number of concurrent
                                          streams allowed for a peer on one HTTP/2
                                          connection.
                                        format: int32
                                        type: integer
                                      maxRequestsPerConnection:
                                        description: Maximum number of requests per
                                          connection to a backend.
                                        format: int32
                                        type: integer
                                      maxRetries:
                                        description: Maximum number of retries that
                                          can be outstanding to all hosts in a cluster
                                          at a given time.
                                        format: int32
                                        type: integer
                                      useClientProtocol:
                                        description: If set to true, client protocol
                                          will be preserved while initiating connection
                                          to backend.
                                        type: boolean
                                    type: object
                                  tcp:
                                    description: Settings common to both HTTP and
                                      TCP upstream connections.
                                    properties:
                                      connectTimeout:
                                        description: TCP connection timeout.
                                        type: string
                                        x-kubernetes-validations:
                                        - message: must be a valid duration greater
                                            than 1ms
                                          rule: duration(self) >= duration('1ms')
                                      idleTimeout:
                                        description: The idle timeout for TCP connections.
                                        type: string
                                        x-kubernetes-validations:
                                        - message: must be a valid duration greater
                                            than 1ms
                                          rule: duration(self) >= duration('1ms')
                                      maxConnectionDuration:
                                        description: The maximum duration of a connection.
                                        type: string
                                        x-kubernetes-validations:
                                        - message: must be a valid duration greater
                                            than 1ms
                                          rule: duration(self) >= duration('1ms')
                                      maxConnections:
                                        description: Maximum number of HTTP1 /TCP
                                          connections to a destination host.
                                        format: int32
                                        type: integer
                                      tcpKeepalive:
                                        description: If set then set SO_KEEPALIVE
                                          on the socket to enable TCP Keepalives.
                                        properties:
                                          interval:
                                            description: The time duration between
                                              keep-alive probes.
                                            type: string
                                            x-kubernetes-validations:
                                            - message: must be a valid duration greater
                                                than 1ms
                                              rule: duration(self) >= duration('1ms')
                                          probes:
                                            description: Maximum number of keepalive
                                              probes to send without response before
                                              deciding the connection is dead.
                                            maximum: **********
                                            minimum: 0
                                            type: integer
                                          time:
                                            description: The time duration a connection
                                              needs to be idle before keep-alive probes
                                              start being sent.
                                            type: string
                                            x-kubernetes-validations:
                                            - message: must be a valid duration greater
                                                than 1ms
                                              rule: duration(self) >= duration('1ms')
                                        type: object
                                    type: object
                                type: object
                              loadBalancer:
                                description: Settings controlling the load balancer
                                  algorithms.
                                oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - simple
                                    - required:
                                      - consistentHash
                                - required:
                                  - simple
                                - required:
                                  - consistentHash
                                properties:
                                  consistentHash:
                                    allOf:
                                    - oneOf:
                                      - not:
                                          anyOf:
                                          - required:
                                            - httpHeaderName
                                          - required:
                                            - httpCookie
                                          - required:
                                            - useSourceIp
                                          - required:
                                            - httpQueryParameterName
                                      - required:
                                        - httpHeaderName
                                      - required:
                                        - httpCookie
                                      - required:
                                        - useSourceIp
                                      - required:
                                        - httpQueryParameterName
                                    - oneOf:
                                      - not:
                                          anyOf:
                                          - required:
                                            - ringHash
                                          - required:
                                            - maglev
                                      - required:
                                        - ringHash
                                      - required:
                                        - maglev
                                    properties:
                                      httpCookie:
                                        description: Hash based on HTTP cookie.
                                        properties:
                                          name:
                                            description: Name of the cookie.
                                            type: string
                                          path:
                                            description: Path to set for the cookie.
                                            type: string
                                          ttl:
                                            description: Lifetime of the cookie.
                                            type: string
                                        required:
                                        - name
                                        type: object
                                      httpHeaderName:
                                        description: Hash based on a specific HTTP
                                          header.
                                        type: string
                                      httpQueryParameterName:
                                        description: Hash based on a specific HTTP
                                          query parameter.
                                        type: string
                                      maglev:
                                        description: The Maglev load balancer implements
                                          consistent hashing to backend hosts.
                                        properties:
                                          tableSize:
                                            description: The table size for Maglev
                                              hashing.
                                            minimum: 0
                                            type: integer
                                        type: object
                                      minimumRingSize:
                                        description: Deprecated.
                                        minimum: 0
                                        type: integer
                                      ringHash:
                                        description: The ring/modulo hash load balancer
                                          implements consistent hashing to backend
                                          hosts.
                                        properties:
                                          minimumRingSize:
                                            description: The minimum number of virtual
                                              nodes to use for the hash ring.
                                            minimum: 0
                                            type: integer
                                        type: object
                                      useSourceIp:
                                        description: Hash based on the source IP address.
                                        type: boolean
                                    type: object
                                  localityLbSetting:
                                    properties:
                                      distribute:
                                        description: 'Optional: only one of distribute,
                                          failover or failoverPriority can be set.'
                                        items:
                                          properties:
                                            from:
                                              description: Originating locality, '/'
                                                separated, e.g.
                                              type: string
                                            to:
                                              additionalProperties:
                                                maximum: **********
                                                minimum: 0
                                                type: integer
                                              description: Map of upstream localities
                                                to traffic distribution weights.
                                              type: object
                                          type: object
                                        type: array
                                      enabled:
                                        description: Enable locality load balancing.
                                        nullable: true
                                        type: boolean
                                      failover:
                                        description: 'Optional: only one of distribute,
                                          failover or failoverPriority can be set.'
                                        items:
                                          properties:
                                            from:
                                              description: Originating region.
                                              type: string
                                            to:
                                              description: Destination region the
                                                traffic will fail over to when endpoints
                                                in the 'from' region becomes unhealthy.
                                              type: string
                                          type: object
                                        type: array
                                      failoverPriority:
                                        description: failoverPriority is an ordered
                                          list of labels used to sort endpoints to
                                          do priority based load balancing.
                                        items:
                                          type: string
                                        type: array
                                    type: object
                                  simple:
                                    description: |2-


                                      Valid Options: LEAST_CONN, RANDOM, PASSTHROUGH, ROUND_ROBIN, LEAST_REQUEST
                                    enum:
                                    - UNSPECIFIED
                                    - LEAST_CONN
                                    - RANDOM
                                    - PASSTHROUGH
                                    - ROUND_ROBIN
                                    - LEAST_REQUEST
                                    type: string
                                  warmup:
                                    description: Represents the warmup configuration
                                      of Service.
                                    properties:
                                      aggression:
                                        description: This parameter controls the speed
                                          of traffic increase over the warmup duration.
                                        format: double
                                        minimum: 1
                                        nullable: true
                                        type: number
                                      duration:
                                        type: string
                                        x-kubernetes-validations:
                                        - message: must be a valid duration greater
                                            than 1ms
                                          rule: duration(self) >= duration('1ms')
                                      minimumPercent:
                                        format: double
                                        maximum: 100
                                        minimum: 0
                                        nullable: true
                                        type: number
                                    required:
                                    - duration
                                    type: object
                                  warmupDurationSecs:
                                    description: 'Deprecated: use `warmup` instead.'
                                    type: string
                                    x-kubernetes-validations:
                                    - message: must be a valid duration greater than
                                        1ms
                                      rule: duration(self) >= duration('1ms')
                                type: object
                              outlierDetection:
                                properties:
                                  baseEjectionTime:
                                    description: Minimum ejection duration.
                                    type: string
                                    x-kubernetes-validations:
                                    - message: must be a valid duration greater than
                                        1ms
                                      rule: duration(self) >= duration('1ms')
                                  consecutive5xxErrors:
                                    description: Number of 5xx errors before a host
                                      is ejected from the connection pool.
                                    maximum: **********
                                    minimum: 0
                                    nullable: true
                                    type: integer
                                  consecutiveErrors:
                                    format: int32
                                    type: integer
                                  consecutiveGatewayErrors:
                                    description: Number of gateway errors before a
                                      host is ejected from the connection pool.
                                    maximum: **********
                                    minimum: 0
                                    nullable: true
                                    type: integer
                                  consecutiveLocalOriginFailures:
                                    description: The number of consecutive locally
                                      originated failures before ejection occurs.
                                    maximum: **********
                                    minimum: 0
                                    nullable: true
                                    type: integer
                                  interval:
                                    description: Time interval between ejection sweep
                                      analysis.
                                    type: string
                                    x-kubernetes-validations:
                                    - message: must be a valid duration greater than
                                        1ms
                                      rule: duration(self) >= duration('1ms')
                                  maxEjectionPercent:
                                    description: Maximum % of hosts in the load balancing
                                      pool for the upstream service that can be ejected.
                                    format: int32
                                    type: integer
                                  minHealthPercent:
                                    description: Outlier detection will be enabled
                                      as long as the associated load balancing pool
                                      has at least `minHealthPercent` hosts in healthy
                                      mode.
                                    format: int32
                                    type: integer
                                  splitExternalLocalOriginErrors:
                                    description: Determines whether to distinguish
                                      local origin failures from external errors.
                                    type: boolean
                                type: object
                              port:
                                description: Specifies the number of a port on the
                                  destination service on which this policy is being
                                  applied.
                                properties:
                                  number:
                                    maximum: **********
                                    minimum: 0
                                    type: integer
                                type: object
                              tls:
                                description: TLS related settings for connections
                                  to the upstream service.
                                properties:
                                  caCertificates:
                                    description: 'OPTIONAL: The path to the file containing
                                      certificate authority certificates to use in
                                      verifying a presented server certificate.'
                                    type: string
                                  caCrl:
                                    description: 'OPTIONAL: The path to the file containing
                                      the certificate revocation list (CRL) to use
                                      in verifying a presented server certificate.'
                                    type: string
                                  clientCertificate:
                                    description: REQUIRED if mode is `MUTUAL`.
                                    type: string
                                  credentialName:
                                    description: The name of the secret that holds
                                      the TLS certs for the client including the CA
                                      certificates.
                                    type: string
                                  insecureSkipVerify:
                                    description: '`insecureSkipVerify` specifies whether
                                      the proxy should skip verifying the CA signature
                                      and SAN for the server certificate corresponding
                                      to the host.'
                                    nullable: true
                                    type: boolean
                                  mode:
                                    description: |-
                                      Indicates whether connections to this port should be secured using TLS.

                                      Valid Options: DISABLE, SIMPLE, MUTUAL, ISTIO_MUTUAL
                                    enum:
                                    - DISABLE
                                    - SIMPLE
                                    - MUTUAL
                                    - ISTIO_MUTUAL
                                    type: string
                                  privateKey:
                                    description: REQUIRED if mode is `MUTUAL`.
                                    type: string
                                  sni:
                                    description: SNI string to present to the server
                                      during TLS handshake.
                                    type: string
                                  subjectAltNames:
                                    description: A list of alternate names to verify
                                      the subject identity in the certificate.
                                    items:
                                      type: string
                                    type: array
                                type: object
                            type: object
                          maxItems: 4096
                          type: array
                        proxyProtocol:
                          description: The upstream PROXY protocol settings.
                          properties:
                            version:
                              description: |-
                                The PROXY protocol version to use.

                                Valid Options: V1, V2
                              enum:
                              - V1
                              - V2
                              type: string
                          type: object
                        tls:
                          description: TLS related settings for connections to the
                            upstream service.
                          properties:
                            caCertificates:
                              description: 'OPTIONAL: The path to the file containing
                                certificate authority certificates to use in verifying
                                a presented server certificate.'
                              type: string
                            caCrl:
                              description: 'OPTIONAL: The path to the file containing
                                the certificate revocation list (CRL) to use in verifying
                                a presented server certificate.'
                              type: string
                            clientCertificate:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            credentialName:
                              description: The name of the secret that holds the TLS
                                certs for the client including the CA certificates.
                              type: string
                            insecureSkipVerify:
                              description: '`insecureSkipVerify` specifies whether
                                the proxy should skip verifying the CA signature and
                                SAN for the server certificate corresponding to the
                                host.'
                              nullable: true
                              type: boolean
                            mode:
                              description: |-
                                Indicates whether connections to this port should be secured using TLS.

                                Valid Options: DISABLE, SIMPLE, MUTUAL, ISTIO_MUTUAL
                              enum:
                              - DISABLE
                              - SIMPLE
                              - MUTUAL
                              - ISTIO_MUTUAL
                              type: string
                            privateKey:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            sni:
                              description: SNI string to present to the server during
                                TLS handshake.
                              type: string
                            subjectAltNames:
                              description: A list of alternate names to verify the
                                subject identity in the certificate.
                              items:
                                type: string
                              type: array
                          type: object
                        tunnel:
                          description: Configuration of tunneling TCP over other transport
                            or application layers for the host configured in the DestinationRule.
                          properties:
                            protocol:
                              description: Specifies which protocol to use for tunneling
                                the downstream connection.
                              type: string
                            targetHost:
                              description: Specifies a host to which the downstream
                                connection is tunneled.
                              type: string
                            targetPort:
                              description: Specifies a port to which the downstream
                                connection is tunneled.
                              maximum: **********
                              minimum: 0
                              type: integer
                          required:
                          - targetHost
                          - targetPort
                          type: object
                      type: object
                  required:
                  - name
                  type: object
                type: array
              trafficPolicy:
                description: Traffic policies to apply (load balancing policy, connection
                  pool sizes, outlier detection).
                properties:
                  connectionPool:
                    properties:
                      http:
                        description: HTTP connection pool settings.
                        properties:
                          h2UpgradePolicy:
                            description: |-
                              Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                              Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                            enum:
                            - DEFAULT
                            - DO_NOT_UPGRADE
                            - UPGRADE
                            type: string
                          http1MaxPendingRequests:
                            description: Maximum number of requests that will be queued
                              while waiting for a ready connection pool connection.
                            format: int32
                            type: integer
                          http2MaxRequests:
                            description: Maximum number of active requests to a destination.
                            format: int32
                            type: integer
                          idleTimeout:
                            description: The idle timeout for upstream connection
                              pool connections.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          maxConcurrentStreams:
                            description: The maximum number of concurrent streams
                              allowed for a peer on one HTTP/2 connection.
                            format: int32
                            type: integer
                          maxRequestsPerConnection:
                            description: Maximum number of requests per connection
                              to a backend.
                            format: int32
                            type: integer
                          maxRetries:
                            description: Maximum number of retries that can be outstanding
                              to all hosts in a cluster at a given time.
                            format: int32
                            type: integer
                          useClientProtocol:
                            description: If set to true, client protocol will be preserved
                              while initiating connection to backend.
                            type: boolean
                        type: object
                      tcp:
                        description: Settings common to both HTTP and TCP upstream
                          connections.
                        properties:
                          connectTimeout:
                            description: TCP connection timeout.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          idleTimeout:
                            description: The idle timeout for TCP connections.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          maxConnectionDuration:
                            description: The maximum duration of a connection.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          maxConnections:
                            description: Maximum number of HTTP1 /TCP connections
                              to a destination host.
                            format: int32
                            type: integer
                          tcpKeepalive:
                            description: If set then set SO_KEEPALIVE on the socket
                              to enable TCP Keepalives.
                            properties:
                              interval:
                                description: The time duration between keep-alive
                                  probes.
                                type: string
                                x-kubernetes-validations:
                                - message: must be a valid duration greater than 1ms
                                  rule: duration(self) >= duration('1ms')
                              probes:
                                description: Maximum number of keepalive probes to
                                  send without response before deciding the connection
                                  is dead.
                                maximum: **********
                                minimum: 0
                                type: integer
                              time:
                                description: The time duration a connection needs
                                  to be idle before keep-alive probes start being
                                  sent.
                                type: string
                                x-kubernetes-validations:
                                - message: must be a valid duration greater than 1ms
                                  rule: duration(self) >= duration('1ms')
                            type: object
                        type: object
                    type: object
                  loadBalancer:
                    description: Settings controlling the load balancer algorithms.
                    oneOf:
                    - not:
                        anyOf:
                        - required:
                          - simple
                        - required:
                          - consistentHash
                    - required:
                      - simple
                    - required:
                      - consistentHash
                    properties:
                      consistentHash:
                        allOf:
                        - oneOf:
                          - not:
                              anyOf:
                              - required:
                                - httpHeaderName
                              - required:
                                - httpCookie
                              - required:
                                - useSourceIp
                              - required:
                                - httpQueryParameterName
                          - required:
                            - httpHeaderName
                          - required:
                            - httpCookie
                          - required:
                            - useSourceIp
                          - required:
                            - httpQueryParameterName
                        - oneOf:
                          - not:
                              anyOf:
                              - required:
                                - ringHash
                              - required:
                                - maglev
                          - required:
                            - ringHash
                          - required:
                            - maglev
                        properties:
                          httpCookie:
                            description: Hash based on HTTP cookie.
                            properties:
                              name:
                                description: Name of the cookie.
                                type: string
                              path:
                                description: Path to set for the cookie.
                                type: string
                              ttl:
                                description: Lifetime of the cookie.
                                type: string
                            required:
                            - name
                            type: object
                          httpHeaderName:
                            description: Hash based on a specific HTTP header.
                            type: string
                          httpQueryParameterName:
                            description: Hash based on a specific HTTP query parameter.
                            type: string
                          maglev:
                            description: The Maglev load balancer implements consistent
                              hashing to backend hosts.
                            properties:
                              tableSize:
                                description: The table size for Maglev hashing.
                                minimum: 0
                                type: integer
                            type: object
                          minimumRingSize:
                            description: Deprecated.
                            minimum: 0
                            type: integer
                          ringHash:
                            description: The ring/modulo hash load balancer implements
                              consistent hashing to backend hosts.
                            properties:
                              minimumRingSize:
                                description: The minimum number of virtual nodes to
                                  use for the hash ring.
                                minimum: 0
                                type: integer
                            type: object
                          useSourceIp:
                            description: Hash based on the source IP address.
                            type: boolean
                        type: object
                      localityLbSetting:
                        properties:
                          distribute:
                            description: 'Optional: only one of distribute, failover
                              or failoverPriority can be set.'
                            items:
                              properties:
                                from:
                                  description: Originating locality, '/' separated,
                                    e.g.
                                  type: string
                                to:
                                  additionalProperties:
                                    maximum: **********
                                    minimum: 0
                                    type: integer
                                  description: Map of upstream localities to traffic
                                    distribution weights.
                                  type: object
                              type: object
                            type: array
                          enabled:
                            description: Enable locality load balancing.
                            nullable: true
                            type: boolean
                          failover:
                            description: 'Optional: only one of distribute, failover
                              or failoverPriority can be set.'
                            items:
                              properties:
                                from:
                                  description: Originating region.
                                  type: string
                                to:
                                  description: Destination region the traffic will
                                    fail over to when endpoints in the 'from' region
                                    becomes unhealthy.
                                  type: string
                              type: object
                            type: array
                          failoverPriority:
                            description: failoverPriority is an ordered list of labels
                              used to sort endpoints to do priority based load balancing.
                            items:
                              type: string
                            type: array
                        type: object
                      simple:
                        description: |2-


                          Valid Options: LEAST_CONN, RANDOM, PASSTHROUGH, ROUND_ROBIN, LEAST_REQUEST
                        enum:
                        - UNSPECIFIED
                        - LEAST_CONN
                        - RANDOM
                        - PASSTHROUGH
                        - ROUND_ROBIN
                        - LEAST_REQUEST
                        type: string
                      warmup:
                        description: Represents the warmup configuration of Service.
                        properties:
                          aggression:
                            description: This parameter controls the speed of traffic
                              increase over the warmup duration.
                            format: double
                            minimum: 1
                            nullable: true
                            type: number
                          duration:
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          minimumPercent:
                            format: double
                            maximum: 100
                            minimum: 0
                            nullable: true
                            type: number
                        required:
                        - duration
                        type: object
                      warmupDurationSecs:
                        description: 'Deprecated: use `warmup` instead.'
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                    type: object
                  outlierDetection:
                    properties:
                      baseEjectionTime:
                        description: Minimum ejection duration.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      consecutive5xxErrors:
                        description: Number of 5xx errors before a host is ejected
                          from the connection pool.
                        maximum: **********
                        minimum: 0
                        nullable: true
                        type: integer
                      consecutiveErrors:
                        format: int32
                        type: integer
                      consecutiveGatewayErrors:
                        description: Number of gateway errors before a host is ejected
                          from the connection pool.
                        maximum: **********
                        minimum: 0
                        nullable: true
                        type: integer
                      consecutiveLocalOriginFailures:
                        description: The number of consecutive locally originated
                          failures before ejection occurs.
                        maximum: **********
                        minimum: 0
                        nullable: true
                        type: integer
                      interval:
                        description: Time interval between ejection sweep analysis.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      maxEjectionPercent:
                        description: Maximum % of hosts in the load balancing pool
                          for the upstream service that can be ejected.
                        format: int32
                        type: integer
                      minHealthPercent:
                        description: Outlier detection will be enabled as long as
                          the associated load balancing pool has at least `minHealthPercent`
                          hosts in healthy mode.
                        format: int32
                        type: integer
                      splitExternalLocalOriginErrors:
                        description: Determines whether to distinguish local origin
                          failures from external errors.
                        type: boolean
                    type: object
                  portLevelSettings:
                    description: Traffic policies specific to individual ports.
                    items:
                      properties:
                        connectionPool:
                          properties:
                            http:
                              description: HTTP connection pool settings.
                              properties:
                                h2UpgradePolicy:
                                  description: |-
                                    Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                                    Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                                  enum:
                                  - DEFAULT
                                  - DO_NOT_UPGRADE
                                  - UPGRADE
                                  type: string
                                http1MaxPendingRequests:
                                  description: Maximum number of requests that will
                                    be queued while waiting for a ready connection
                                    pool connection.
                                  format: int32
                                  type: integer
                                http2MaxRequests:
                                  description: Maximum number of active requests to
                                    a destination.
                                  format: int32
                                  type: integer
                                idleTimeout:
                                  description: The idle timeout for upstream connection
                                    pool connections.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConcurrentStreams:
                                  description: The maximum number of concurrent streams
                                    allowed for a peer on one HTTP/2 connection.
                                  format: int32
                                  type: integer
                                maxRequestsPerConnection:
                                  description: Maximum number of requests per connection
                                    to a backend.
                                  format: int32
                                  type: integer
                                maxRetries:
                                  description: Maximum number of retries that can
                                    be outstanding to all hosts in a cluster at a
                                    given time.
                                  format: int32
                                  type: integer
                                useClientProtocol:
                                  description: If set to true, client protocol will
                                    be preserved while initiating connection to backend.
                                  type: boolean
                              type: object
                            tcp:
                              description: Settings common to both HTTP and TCP upstream
                                connections.
                              properties:
                                connectTimeout:
                                  description: TCP connection timeout.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                idleTimeout:
                                  description: The idle timeout for TCP connections.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConnectionDuration:
                                  description: The maximum duration of a connection.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConnections:
                                  description: Maximum number of HTTP1 /TCP connections
                                    to a destination host.
                                  format: int32
                                  type: integer
                                tcpKeepalive:
                                  description: If set then set SO_KEEPALIVE on the
                                    socket to enable TCP Keepalives.
                                  properties:
                                    interval:
                                      description: The time duration between keep-alive
                                        probes.
                                      type: string
                                      x-kubernetes-validations:
                                      - message: must be a valid duration greater
                                          than 1ms
                                        rule: duration(self) >= duration('1ms')
                                    probes:
                                      description: Maximum number of keepalive probes
                                        to send without response before deciding the
                                        connection is dead.
                                      maximum: **********
                                      minimum: 0
                                      type: integer
                                    time:
                                      description: The time duration a connection
                                        needs to be idle before keep-alive probes
                                        start being sent.
                                      type: string
                                      x-kubernetes-validations:
                                      - message: must be a valid duration greater
                                          than 1ms
                                        rule: duration(self) >= duration('1ms')
                                  type: object
                              type: object
                          type: object
                        loadBalancer:
                          description: Settings controlling the load balancer algorithms.
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - simple
                              - required:
                                - consistentHash
                          - required:
                            - simple
                          - required:
                            - consistentHash
                          properties:
                            consistentHash:
                              allOf:
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - httpHeaderName
                                    - required:
                                      - httpCookie
                                    - required:
                                      - useSourceIp
                                    - required:
                                      - httpQueryParameterName
                                - required:
                                  - httpHeaderName
                                - required:
                                  - httpCookie
                                - required:
                                  - useSourceIp
                                - required:
                                  - httpQueryParameterName
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - ringHash
                                    - required:
                                      - maglev
                                - required:
                                  - ringHash
                                - required:
                                  - maglev
                              properties:
                                httpCookie:
                                  description: Hash based on HTTP cookie.
                                  properties:
                                    name:
                                      description: Name of the cookie.
                                      type: string
                                    path:
                                      description: Path to set for the cookie.
                                      type: string
                                    ttl:
                                      description: Lifetime of the cookie.
                                      type: string
                                  required:
                                  - name
                                  type: object
                                httpHeaderName:
                                  description: Hash based on a specific HTTP header.
                                  type: string
                                httpQueryParameterName:
                                  description: Hash based on a specific HTTP query
                                    parameter.
                                  type: string
                                maglev:
                                  description: The Maglev load balancer implements
                                    consistent hashing to backend hosts.
                                  properties:
                                    tableSize:
                                      description: The table size for Maglev hashing.
                                      minimum: 0
                                      type: integer
                                  type: object
                                minimumRingSize:
                                  description: Deprecated.
                                  minimum: 0
                                  type: integer
                                ringHash:
                                  description: The ring/modulo hash load balancer
                                    implements consistent hashing to backend hosts.
                                  properties:
                                    minimumRingSize:
                                      description: The minimum number of virtual nodes
                                        to use for the hash ring.
                                      minimum: 0
                                      type: integer
                                  type: object
                                useSourceIp:
                                  description: Hash based on the source IP address.
                                  type: boolean
                              type: object
                            localityLbSetting:
                              properties:
                                distribute:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating locality, '/' separated,
                                          e.g.
                                        type: string
                                      to:
                                        additionalProperties:
                                          maximum: **********
                                          minimum: 0
                                          type: integer
                                        description: Map of upstream localities to
                                          traffic distribution weights.
                                        type: object
                                    type: object
                                  type: array
                                enabled:
                                  description: Enable locality load balancing.
                                  nullable: true
                                  type: boolean
                                failover:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating region.
                                        type: string
                                      to:
                                        description: Destination region the traffic
                                          will fail over to when endpoints in the
                                          'from' region becomes unhealthy.
                                        type: string
                                    type: object
                                  type: array
                                failoverPriority:
                                  description: failoverPriority is an ordered list
                                    of labels used to sort endpoints to do priority
                                    based load balancing.
                                  items:
                                    type: string
                                  type: array
                              type: object
                            simple:
                              description: |2-


                                Valid Options: LEAST_CONN, RANDOM, PASSTHROUGH, ROUND_ROBIN, LEAST_REQUEST
                              enum:
                              - UNSPECIFIED
                              - LEAST_CONN
                              - RANDOM
                              - PASSTHROUGH
                              - ROUND_ROBIN
                              - LEAST_REQUEST
                              type: string
                            warmup:
                              description: Represents the warmup configuration of
                                Service.
                              properties:
                                aggression:
                                  description: This parameter controls the speed of
                                    traffic increase over the warmup duration.
                                  format: double
                                  minimum: 1
                                  nullable: true
                                  type: number
                                duration:
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                minimumPercent:
                                  format: double
                                  maximum: 100
                                  minimum: 0
                                  nullable: true
                                  type: number
                              required:
                              - duration
                              type: object
                            warmupDurationSecs:
                              description: 'Deprecated: use `warmup` instead.'
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                          type: object
                        outlierDetection:
                          properties:
                            baseEjectionTime:
                              description: Minimum ejection duration.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            consecutive5xxErrors:
                              description: Number of 5xx errors before a host is ejected
                                from the connection pool.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            consecutiveErrors:
                              format: int32
                              type: integer
                            consecutiveGatewayErrors:
                              description: Number of gateway errors before a host
                                is ejected from the connection pool.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            consecutiveLocalOriginFailures:
                              description: The number of consecutive locally originated
                                failures before ejection occurs.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            interval:
                              description: Time interval between ejection sweep analysis.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            maxEjectionPercent:
                              description: Maximum % of hosts in the load balancing
                                pool for the upstream service that can be ejected.
                              format: int32
                              type: integer
                            minHealthPercent:
                              description: Outlier detection will be enabled as long
                                as the associated load balancing pool has at least
                                `minHealthPercent` hosts in healthy mode.
                              format: int32
                              type: integer
                            splitExternalLocalOriginErrors:
                              description: Determines whether to distinguish local
                                origin failures from external errors.
                              type: boolean
                          type: object
                        port:
                          description: Specifies the number of a port on the destination
                            service on which this policy is being applied.
                          properties:
                            number:
                              maximum: **********
                              minimum: 0
                              type: integer
                          type: object
                        tls:
                          description: TLS related settings for connections to the
                            upstream service.
                          properties:
                            caCertificates:
                              description: 'OPTIONAL: The path to the file containing
                                certificate authority certificates to use in verifying
                                a presented server certificate.'
                              type: string
                            caCrl:
                              description: 'OPTIONAL: The path to the file containing
                                the certificate revocation list (CRL) to use in verifying
                                a presented server certificate.'
                              type: string
                            clientCertificate:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            credentialName:
                              description: The name of the secret that holds the TLS
                                certs for the client including the CA certificates.
                              type: string
                            insecureSkipVerify:
                              description: '`insecureSkipVerify` specifies whether
                                the proxy should skip verifying the CA signature and
                                SAN for the server certificate corresponding to the
                                host.'
                              nullable: true
                              type: boolean
                            mode:
                              description: |-
                                Indicates whether connections to this port should be secured using TLS.

                                Valid Options: DISABLE, SIMPLE, MUTUAL, ISTIO_MUTUAL
                              enum:
                              - DISABLE
                              - SIMPLE
                              - MUTUAL
                              - ISTIO_MUTUAL
                              type: string
                            privateKey:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            sni:
                              description: SNI string to present to the server during
                                TLS handshake.
                              type: string
                            subjectAltNames:
                              description: A list of alternate names to verify the
                                subject identity in the certificate.
                              items:
                                type: string
                              type: array
                          type: object
                      type: object
                    maxItems: 4096
                    type: array
                  proxyProtocol:
                    description: The upstream PROXY protocol settings.
                    properties:
                      version:
                        description: |-
                          The PROXY protocol version to use.

                          Valid Options: V1, V2
                        enum:
                        - V1
                        - V2
                        type: string
                    type: object
                  tls:
                    description: TLS related settings for connections to the upstream
                      service.
                    properties:
                      caCertificates:
                        description: 'OPTIONAL: The path to the file containing certificate
                          authority certificates to use in verifying a presented server
                          certificate.'
                        type: string
                      caCrl:
                        description: 'OPTIONAL: The path to the file containing the
                          certificate revocation list (CRL) to use in verifying a
                          presented server certificate.'
                        type: string
                      clientCertificate:
                        description: REQUIRED if mode is `MUTUAL`.
                        type: string
                      credentialName:
                        description: The name of the secret that holds the TLS certs
                          for the client including the CA certificates.
                        type: string
                      insecureSkipVerify:
                        description: '`insecureSkipVerify` specifies whether the proxy
                          should skip verifying the CA signature and SAN for the server
                          certificate corresponding to the host.'
                        nullable: true
                        type: boolean
                      mode:
                        description: |-
                          Indicates whether connections to this port should be secured using TLS.

                          Valid Options: DISABLE, SIMPLE, MUTUAL, ISTIO_MUTUAL
                        enum:
                        - DISABLE
                        - SIMPLE
                        - MUTUAL
                        - ISTIO_MUTUAL
                        type: string
                      privateKey:
                        description: REQUIRED if mode is `MUTUAL`.
                        type: string
                      sni:
                        description: SNI string to present to the server during TLS
                          handshake.
                        type: string
                      subjectAltNames:
                        description: A list of alternate names to verify the subject
                          identity in the certificate.
                        items:
                          type: string
                        type: array
                    type: object
                  tunnel:
                    description: Configuration of tunneling TCP over other transport
                      or application layers for the host configured in the DestinationRule.
                    properties:
                      protocol:
                        description: Specifies which protocol to use for tunneling
                          the downstream connection.
                        type: string
                      targetHost:
                        description: Specifies a host to which the downstream connection
                          is tunneled.
                        type: string
                      targetPort:
                        description: Specifies a port to which the downstream connection
                          is tunneled.
                        maximum: **********
                        minimum: 0
                        type: integer
                    required:
                    - targetHost
                    - targetPort
                    type: object
                type: object
              workloadSelector:
                description: Criteria used to select the specific set of pods/VMs
                  on which this `DestinationRule` configuration should be applied.
                properties:
                  matchLabels:
                    additionalProperties:
                      maxLength: 63
                      type: string
                      x-kubernetes-validations:
                      - message: wildcard not allowed in label value match
                        rule: '!self.contains(''*'')'
                    description: One or more labels that indicate a specific set of
                      pods/VMs on which a policy should be applied.
                    maxProperties: 4096
                    type: object
                    x-kubernetes-validations:
                    - message: wildcard not allowed in label key match
                      rule: self.all(key, !key.contains('*'))
                    - message: key must not be empty
                      rule: self.all(key, key.size() != 0)
                type: object
            required:
            - host
            type: object
          status:
            properties:
              conditions:
                description: Current service state of the resource.
                items:
                  properties:
                    lastProbeTime:
                      description: Last time we probed the condition.
                      format: date-time
                      type: string
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      format: date-time
                      type: string
                    message:
                      description: Human-readable message indicating details about
                        last transition.
                      type: string
                    reason:
                      description: Unique, one-word, CamelCase reason for the condition's
                        last transition.
                      type: string
                    status:
                      description: Status is the status of the condition.
                      type: string
                    type:
                      description: Type is the type of the condition.
                      type: string
                  type: object
                type: array
              observedGeneration:
                anyOf:
                - type: integer
                - type: string
                description: Resource Generation to which the Reconciled Condition
                  refers.
                x-kubernetes-int-or-string: true
              validationMessages:
                description: Includes any errors or warnings detected by Istio's analyzers.
                items:
                  properties:
                    documentationUrl:
                      description: A url pointing to the Istio documentation for this
                        specific error type.
                      type: string
                    level:
                      description: |-
                        Represents how severe a message is.

                        Valid Options: UNKNOWN, ERROR, WARNING, INFO
                      enum:
                      - UNKNOWN
                      - ERROR
                      - WARNING
                      - INFO
                      type: string
                    type:
                      properties:
                        code:
                          description: A 7 character code matching `^IST[0-9]{4}$`
                            intended to uniquely identify the message type.
                          type: string
                        name:
                          description: A human-readable name for the message type.
                          type: string
                      type: object
                  type: object
                type: array
            type: object
            x-kubernetes-preserve-unknown-fields: true
        type: object
    served: true
    storage: false
    subresources:
      status: {}
  - additionalPrinterColumns:
    - description: The name of a service from the service registry
      jsonPath: .spec.host
      name: Host
      type: string
    - description: 'CreationTimestamp is a timestamp representing the server time
        when this object was created. It is not guaranteed to be set in happens-before
        order across separate operations. Clients may not set this value. It is represented
        in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for
        lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata'
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha3
    schema:
      openAPIV3Schema:
        properties:
          spec:
            description: 'Configuration affecting load balancing, outlier detection,
              etc. See more details at: https://istio.io/docs/reference/config/networking/destination-rule.html'
            properties:
              exportTo:
                description: A list of namespaces to which this destination rule is
                  exported.
                items:
                  type: string
                type: array
              host:
                description: The name of a service from the service registry.
                type: string
              subsets:
                description: One or more named sets that represent individual versions
                  of a service.
                items:
                  properties:
                    labels:
                      additionalProperties:
                        type: string
                      description: Labels apply a filter over the endpoints of a service
                        in the service registry.
                      type: object
                    name:
                      description: Name of the subset.
                      type: string
                    trafficPolicy:
                      description: Traffic policies that apply to this subset.
                      properties:
                        connectionPool:
                          properties:
                            http:
                              description: HTTP connection pool settings.
                              properties:
                                h2UpgradePolicy:
                                  description: |-
                                    Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                                    Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                                  enum:
                                  - DEFAULT
                                  - DO_NOT_UPGRADE
                                  - UPGRADE
                                  type: string
                                http1MaxPendingRequests:
                                  description: Maximum number of requests that will
                                    be queued while waiting for a ready connection
                                    pool connection.
                                  format: int32
                                  type: integer
                                http2MaxRequests:
                                  description: Maximum number of active requests to
                                    a destination.
                                  format: int32
                                  type: integer
                                idleTimeout:
                                  description: The idle timeout for upstream connection
                                    pool connections.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConcurrentStreams:
                                  description: The maximum number of concurrent streams
                                    allowed for a peer on one HTTP/2 connection.
                                  format: int32
                                  type: integer
                                maxRequestsPerConnection:
                                  description: Maximum number of requests per connection
                                    to a backend.
                                  format: int32
                                  type: integer
                                maxRetries:
                                  description: Maximum number of retries that can
                                    be outstanding to all hosts in a cluster at a
                                    given time.
                                  format: int32
                                  type: integer
                                useClientProtocol:
                                  description: If set to true, client protocol will
                                    be preserved while initiating connection to backend.
                                  type: boolean
                              type: object
                            tcp:
                              description: Settings common to both HTTP and TCP upstream
                                connections.
                              properties:
                                connectTimeout:
                                  description: TCP connection timeout.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                idleTimeout:
                                  description: The idle timeout for TCP connections.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConnectionDuration:
                                  description: The maximum duration of a connection.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConnections:
                                  description: Maximum number of HTTP1 /TCP connections
                                    to a destination host.
                                  format: int32
                                  type: integer
                                tcpKeepalive:
                                  description: If set then set SO_KEEPALIVE on the
                                    socket to enable TCP Keepalives.
                                  properties:
                                    interval:
                                      description: The time duration between keep-alive
                                        probes.
                                      type: string
                                      x-kubernetes-validations:
                                      - message: must be a valid duration greater
                                          than 1ms
                                        rule: duration(self) >= duration('1ms')
                                    probes:
                                      description: Maximum number of keepalive probes
                                        to send without response before deciding the
                                        connection is dead.
                                      maximum: **********
                                      minimum: 0
                                      type: integer
                                    time:
                                      description: The time duration a connection
                                        needs to be idle before keep-alive probes
                                        start being sent.
                                      type: string
                                      x-kubernetes-validations:
                                      - message: must be a valid duration greater
                                          than 1ms
                                        rule: duration(self) >= duration('1ms')
                                  type: object
                              type: object
                          type: object
                        loadBalancer:
                          description: Settings controlling the load balancer algorithms.
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - simple
                              - required:
                                - consistentHash
                          - required:
                            - simple
                          - required:
                            - consistentHash
                          properties:
                            consistentHash:
                              allOf:
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - httpHeaderName
                                    - required:
                                      - httpCookie
                                    - required:
                                      - useSourceIp
                                    - required:
                                      - httpQueryParameterName
                                - required:
                                  - httpHeaderName
                                - required:
                                  - httpCookie
                                - required:
                                  - useSourceIp
                                - required:
                                  - httpQueryParameterName
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - ringHash
                                    - required:
                                      - maglev
                                - required:
                                  - ringHash
                                - required:
                                  - maglev
                              properties:
                                httpCookie:
                                  description: Hash based on HTTP cookie.
                                  properties:
                                    name:
                                      description: Name of the cookie.
                                      type: string
                                    path:
                                      description: Path to set for the cookie.
                                      type: string
                                    ttl:
                                      description: Lifetime of the cookie.
                                      type: string
                                  required:
                                  - name
                                  type: object
                                httpHeaderName:
                                  description: Hash based on a specific HTTP header.
                                  type: string
                                httpQueryParameterName:
                                  description: Hash based on a specific HTTP query
                                    parameter.
                                  type: string
                                maglev:
                                  description: The Maglev load balancer implements
                                    consistent hashing to backend hosts.
                                  properties:
                                    tableSize:
                                      description: The table size for Maglev hashing.
                                      minimum: 0
                                      type: integer
                                  type: object
                                minimumRingSize:
                                  description: Deprecated.
                                  minimum: 0
                                  type: integer
                                ringHash:
                                  description: The ring/modulo hash load balancer
                                    implements consistent hashing to backend hosts.
                                  properties:
                                    minimumRingSize:
                                      description: The minimum number of virtual nodes
                                        to use for the hash ring.
                                      minimum: 0
                                      type: integer
                                  type: object
                                useSourceIp:
                                  description: Hash based on the source IP address.
                                  type: boolean
                              type: object
                            localityLbSetting:
                              properties:
                                distribute:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating locality, '/' separated,
                                          e.g.
                                        type: string
                                      to:
                                        additionalProperties:
                                          maximum: **********
                                          minimum: 0
                                          type: integer
                                        description: Map of upstream localities to
                                          traffic distribution weights.
                                        type: object
                                    type: object
                                  type: array
                                enabled:
                                  description: Enable locality load balancing.
                                  nullable: true
                                  type: boolean
                                failover:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating region.
                                        type: string
                                      to:
                                        description: Destination region the traffic
                                          will fail over to when endpoints in the
                                          'from' region becomes unhealthy.
                                        type: string
                                    type: object
                                  type: array
                                failoverPriority:
                                  description: failoverPriority is an ordered list
                                    of labels used to sort endpoints to do priority
                                    based load balancing.
                                  items:
                                    type: string
                                  type: array
                              type: object
                            simple:
                              description: |2-


                                Valid Options: LEAST_CONN, RANDOM, PASSTHROUGH, ROUND_ROBIN, LEAST_REQUEST
                              enum:
                              - UNSPECIFIED
                              - LEAST_CONN
                              - RANDOM
                              - PASSTHROUGH
                              - ROUND_ROBIN
                              - LEAST_REQUEST
                              type: string
                            warmup:
                              description: Represents the warmup configuration of
                                Service.
                              properties:
                                aggression:
                                  description: This parameter controls the speed of
                                    traffic increase over the warmup duration.
                                  format: double
                                  minimum: 1
                                  nullable: true
                                  type: number
                                duration:
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                minimumPercent:
                                  format: double
                                  maximum: 100
                                  minimum: 0
                                  nullable: true
                                  type: number
                              required:
                              - duration
                              type: object
                            warmupDurationSecs:
                              description: 'Deprecated: use `warmup` instead.'
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                          type: object
                        outlierDetection:
                          properties:
                            baseEjectionTime:
                              description: Minimum ejection duration.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            consecutive5xxErrors:
                              description: Number of 5xx errors before a host is ejected
                                from the connection pool.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            consecutiveErrors:
                              format: int32
                              type: integer
                            consecutiveGatewayErrors:
                              description: Number of gateway errors before a host
                                is ejected from the connection pool.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            consecutiveLocalOriginFailures:
                              description: The number of consecutive locally originated
                                failures before ejection occurs.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            interval:
                              description: Time interval between ejection sweep analysis.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            maxEjectionPercent:
                              description: Maximum % of hosts in the load balancing
                                pool for the upstream service that can be ejected.
                              format: int32
                              type: integer
                            minHealthPercent:
                              description: Outlier detection will be enabled as long
                                as the associated load balancing pool has at least
                                `minHealthPercent` hosts in healthy mode.
                              format: int32
                              type: integer
                            splitExternalLocalOriginErrors:
                              description: Determines whether to distinguish local
                                origin failures from external errors.
                              type: boolean
                          type: object
                        portLevelSettings:
                          description: Traffic policies specific to individual ports.
                          items:
                            properties:
                              connectionPool:
                                properties:
                                  http:
                                    description: HTTP connection pool settings.
                                    properties:
                                      h2UpgradePolicy:
                                        description: |-
                                          Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                                          Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                                        enum:
                                        - DEFAULT
                                        - DO_NOT_UPGRADE
                                        - UPGRADE
                                        type: string
                                      http1MaxPendingRequests:
                                        description: Maximum number of requests that
                                          will be queued while waiting for a ready
                                          connection pool connection.
                                        format: int32
                                        type: integer
                                      http2MaxRequests:
                                        description: Maximum number of active requests
                                          to a destination.
                                        format: int32
                                        type: integer
                                      idleTimeout:
                                        description: The idle timeout for upstream
                                          connection pool connections.
                                        type: string
                                        x-kubernetes-validations:
                                        - message: must be a valid duration greater
                                            than 1ms
                                          rule: duration(self) >= duration('1ms')
                                      maxConcurrentStreams:
                                        description: The maximum number of concurrent
                                          streams allowed for a peer on one HTTP/2
                                          connection.
                                        format: int32
                                        type: integer
                                      maxRequestsPerConnection:
                                        description: Maximum number of requests per
                                          connection to a backend.
                                        format: int32
                                        type: integer
                                      maxRetries:
                                        description: Maximum number of retries that
                                          can be outstanding to all hosts in a cluster
                                          at a given time.
                                        format: int32
                                        type: integer
                                      useClientProtocol:
                                        description: If set to true, client protocol
                                          will be preserved while initiating connection
                                          to backend.
                                        type: boolean
                                    type: object
                                  tcp:
                                    description: Settings common to both HTTP and
                                      TCP upstream connections.
                                    properties:
                                      connectTimeout:
                                        description: TCP connection timeout.
                                        type: string
                                        x-kubernetes-validations:
                                        - message: must be a valid duration greater
                                            than 1ms
                                          rule: duration(self) >= duration('1ms')
                                      idleTimeout:
                                        description: The idle timeout for TCP connections.
                                        type: string
                                        x-kubernetes-validations:
                                        - message: must be a valid duration greater
                                            than 1ms
                                          rule: duration(self) >= duration('1ms')
                                      maxConnectionDuration:
                                        description: The maximum duration of a connection.
                                        type: string
                                        x-kubernetes-validations:
                                        - message: must be a valid duration greater
                                            than 1ms
                                          rule: duration(self) >= duration('1ms')
                                      maxConnections:
                                        description: Maximum number of HTTP1 /TCP
                                          connections to a destination host.
                                        format: int32
                                        type: integer
                                      tcpKeepalive:
                                        description: If set then set SO_KEEPALIVE
                                          on the socket to enable TCP Keepalives.
                                        properties:
                                          interval:
                                            description: The time duration between
                                              keep-alive probes.
                                            type: string
                                            x-kubernetes-validations:
                                            - message: must be a valid duration greater
                                                than 1ms
                                              rule: duration(self) >= duration('1ms')
                                          probes:
                                            description: Maximum number of keepalive
                                              probes to send without response before
                                              deciding the connection is dead.
                                            maximum: **********
                                            minimum: 0
                                            type: integer
                                          time:
                                            description: The time duration a connection
                                              needs to be idle before keep-alive probes
                                              start being sent.
                                            type: string
                                            x-kubernetes-validations:
                                            - message: must be a valid duration greater
                                                than 1ms
                                              rule: duration(self) >= duration('1ms')
                                        type: object
                                    type: object
                                type: object
                              loadBalancer:
                                description: Settings controlling the load balancer
                                  algorithms.
                                oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - simple
                                    - required:
                                      - consistentHash
                                - required:
                                  - simple
                                - required:
                                  - consistentHash
                                properties:
                                  consistentHash:
                                    allOf:
                                    - oneOf:
                                      - not:
                                          anyOf:
                                          - required:
                                            - httpHeaderName
                                          - required:
                                            - httpCookie
                                          - required:
                                            - useSourceIp
                                          - required:
                                            - httpQueryParameterName
                                      - required:
                                        - httpHeaderName
                                      - required:
                                        - httpCookie
                                      - required:
                                        - useSourceIp
                                      - required:
                                        - httpQueryParameterName
                                    - oneOf:
                                      - not:
                                          anyOf:
                                          - required:
                                            - ringHash
                                          - required:
                                            - maglev
                                      - required:
                                        - ringHash
                                      - required:
                                        - maglev
                                    properties:
                                      httpCookie:
                                        description: Hash based on HTTP cookie.
                                        properties:
                                          name:
                                            description: Name of the cookie.
                                            type: string
                                          path:
                                            description: Path to set for the cookie.
                                            type: string
                                          ttl:
                                            description: Lifetime of the cookie.
                                            type: string
                                        required:
                                        - name
                                        type: object
                                      httpHeaderName:
                                        description: Hash based on a specific HTTP
                                          header.
                                        type: string
                                      httpQueryParameterName:
                                        description: Hash based on a specific HTTP
                                          query parameter.
                                        type: string
                                      maglev:
                                        description: The Maglev load balancer implements
                                          consistent hashing to backend hosts.
                                        properties:
                                          tableSize:
                                            description: The table size for Maglev
                                              hashing.
                                            minimum: 0
                                            type: integer
                                        type: object
                                      minimumRingSize:
                                        description: Deprecated.
                                        minimum: 0
                                        type: integer
                                      ringHash:
                                        description: The ring/modulo hash load balancer
                                          implements consistent hashing to backend
                                          hosts.
                                        properties:
                                          minimumRingSize:
                                            description: The minimum number of virtual
                                              nodes to use for the hash ring.
                                            minimum: 0
                                            type: integer
                                        type: object
                                      useSourceIp:
                                        description: Hash based on the source IP address.
                                        type: boolean
                                    type: object
                                  localityLbSetting:
                                    properties:
                                      distribute:
                                        description: 'Optional: only one of distribute,
                                          failover or failoverPriority can be set.'
                                        items:
                                          properties:
                                            from:
                                              description: Originating locality, '/'
                                                separated, e.g.
                                              type: string
                                            to:
                                              additionalProperties:
                                                maximum: **********
                                                minimum: 0
                                                type: integer
                                              description: Map of upstream localities
                                                to traffic distribution weights.
                                              type: object
                                          type: object
                                        type: array
                                      enabled:
                                        description: Enable locality load balancing.
                                        nullable: true
                                        type: boolean
                                      failover:
                                        description: 'Optional: only one of distribute,
                                          failover or failoverPriority can be set.'
                                        items:
                                          properties:
                                            from:
                                              description: Originating region.
                                              type: string
                                            to:
                                              description: Destination region the
                                                traffic will fail over to when endpoints
                                                in the 'from' region becomes unhealthy.
                                              type: string
                                          type: object
                                        type: array
                                      failoverPriority:
                                        description: failoverPriority is an ordered
                                          list of labels used to sort endpoints to
                                          do priority based load balancing.
                                        items:
                                          type: string
                                        type: array
                                    type: object
                                  simple:
                                    description: |2-


                                      Valid Options: LEAST_CONN, RANDOM, PASSTHROUGH, ROUND_ROBIN, LEAST_REQUEST
                                    enum:
                                    - UNSPECIFIED
                                    - LEAST_CONN
                                    - RANDOM
                                    - PASSTHROUGH
                                    - ROUND_ROBIN
                                    - LEAST_REQUEST
                                    type: string
                                  warmup:
                                    description: Represents the warmup configuration
                                      of Service.
                                    properties:
                                      aggression:
                                        description: This parameter controls the speed
                                          of traffic increase over the warmup duration.
                                        format: double
                                        minimum: 1
                                        nullable: true
                                        type: number
                                      duration:
                                        type: string
                                        x-kubernetes-validations:
                                        - message: must be a valid duration greater
                                            than 1ms
                                          rule: duration(self) >= duration('1ms')
                                      minimumPercent:
                                        format: double
                                        maximum: 100
                                        minimum: 0
                                        nullable: true
                                        type: number
                                    required:
                                    - duration
                                    type: object
                                  warmupDurationSecs:
                                    description: 'Deprecated: use `warmup` instead.'
                                    type: string
                                    x-kubernetes-validations:
                                    - message: must be a valid duration greater than
                                        1ms
                                      rule: duration(self) >= duration('1ms')
                                type: object
                              outlierDetection:
                                properties:
                                  baseEjectionTime:
                                    description: Minimum ejection duration.
                                    type: string
                                    x-kubernetes-validations:
                                    - message: must be a valid duration greater than
                                        1ms
                                      rule: duration(self) >= duration('1ms')
                                  consecutive5xxErrors:
                                    description: Number of 5xx errors before a host
                                      is ejected from the connection pool.
                                    maximum: **********
                                    minimum: 0
                                    nullable: true
                                    type: integer
                                  consecutiveErrors:
                                    format: int32
                                    type: integer
                                  consecutiveGatewayErrors:
                                    description: Number of gateway errors before a
                                      host is ejected from the connection pool.
                                    maximum: **********
                                    minimum: 0
                                    nullable: true
                                    type: integer
                                  consecutiveLocalOriginFailures:
                                    description: The number of consecutive locally
                                      originated failures before ejection occurs.
                                    maximum: **********
                                    minimum: 0
                                    nullable: true
                                    type: integer
                                  interval:
                                    description: Time interval between ejection sweep
                                      analysis.
                                    type: string
                                    x-kubernetes-validations:
                                    - message: must be a valid duration greater than
                                        1ms
                                      rule: duration(self) >= duration('1ms')
                                  maxEjectionPercent:
                                    description: Maximum % of hosts in the load balancing
                                      pool for the upstream service that can be ejected.
                                    format: int32
                                    type: integer
                                  minHealthPercent:
                                    description: Outlier detection will be enabled
                                      as long as the associated load balancing pool
                                      has at least `minHealthPercent` hosts in healthy
                                      mode.
                                    format: int32
                                    type: integer
                                  splitExternalLocalOriginErrors:
                                    description: Determines whether to distinguish
                                      local origin failures from external errors.
                                    type: boolean
                                type: object
                              port:
                                description: Specifies the number of a port on the
                                  destination service on which this policy is being
                                  applied.
                                properties:
                                  number:
                                    maximum: **********
                                    minimum: 0
                                    type: integer
                                type: object
                              tls:
                                description: TLS related settings for connections
                                  to the upstream service.
                                properties:
                                  caCertificates:
                                    description: 'OPTIONAL: The path to the file containing
                                      certificate authority certificates to use in
                                      verifying a presented server certificate.'
                                    type: string
                                  caCrl:
                                    description: 'OPTIONAL: The path to the file containing
                                      the certificate revocation list (CRL) to use
                                      in verifying a presented server certificate.'
                                    type: string
                                  clientCertificate:
                                    description: REQUIRED if mode is `MUTUAL`.
                                    type: string
                                  credentialName:
                                    description: The name of the secret that holds
                                      the TLS certs for the client including the CA
                                      certificates.
                                    type: string
                                  insecureSkipVerify:
                                    description: '`insecureSkipVerify` specifies whether
                                      the proxy should skip verifying the CA signature
                                      and SAN for the server certificate corresponding
                                      to the host.'
                                    nullable: true
                                    type: boolean
                                  mode:
                                    description: |-
                                      Indicates whether connections to this port should be secured using TLS.

                                      Valid Options: DISABLE, SIMPLE, MUTUAL, ISTIO_MUTUAL
                                    enum:
                                    - DISABLE
                                    - SIMPLE
                                    - MUTUAL
                                    - ISTIO_MUTUAL
                                    type: string
                                  privateKey:
                                    description: REQUIRED if mode is `MUTUAL`.
                                    type: string
                                  sni:
                                    description: SNI string to present to the server
                                      during TLS handshake.
                                    type: string
                                  subjectAltNames:
                                    description: A list of alternate names to verify
                                      the subject identity in the certificate.
                                    items:
                                      type: string
                                    type: array
                                type: object
                            type: object
                          maxItems: 4096
                          type: array
                        proxyProtocol:
                          description: The upstream PROXY protocol settings.
                          properties:
                            version:
                              description: |-
                                The PROXY protocol version to use.

                                Valid Options: V1, V2
                              enum:
                              - V1
                              - V2
                              type: string
                          type: object
                        tls:
                          description: TLS related settings for connections to the
                            upstream service.
                          properties:
                            caCertificates:
                              description: 'OPTIONAL: The path to the file containing
                                certificate authority certificates to use in verifying
                                a presented server certificate.'
                              type: string
                            caCrl:
                              description: 'OPTIONAL: The path to the file containing
                                the certificate revocation list (CRL) to use in verifying
                                a presented server certificate.'
                              type: string
                            clientCertificate:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            credentialName:
                              description: The name of the secret that holds the TLS
                                certs for the client including the CA certificates.
                              type: string
                            insecureSkipVerify:
                              description: '`insecureSkipVerify` specifies whether
                                the proxy should skip verifying the CA signature and
                                SAN for the server certificate corresponding to the
                                host.'
                              nullable: true
                              type: boolean
                            mode:
                              description: |-
                                Indicates whether connections to this port should be secured using TLS.

                                Valid Options: DISABLE, SIMPLE, MUTUAL, ISTIO_MUTUAL
                              enum:
                              - DISABLE
                              - SIMPLE
                              - MUTUAL
                              - ISTIO_MUTUAL
                              type: string
                            privateKey:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            sni:
                              description: SNI string to present to the server during
                                TLS handshake.
                              type: string
                            subjectAltNames:
                              description: A list of alternate names to verify the
                                subject identity in the certificate.
                              items:
                                type: string
                              type: array
                          type: object
                        tunnel:
                          description: Configuration of tunneling TCP over other transport
                            or application layers for the host configured in the DestinationRule.
                          properties:
                            protocol:
                              description: Specifies which protocol to use for tunneling
                                the downstream connection.
                              type: string
                            targetHost:
                              description: Specifies a host to which the downstream
                                connection is tunneled.
                              type: string
                            targetPort:
                              description: Specifies a port to which the downstream
                                connection is tunneled.
                              maximum: **********
                              minimum: 0
                              type: integer
                          required:
                          - targetHost
                          - targetPort
                          type: object
                      type: object
                  required:
                  - name
                  type: object
                type: array
              trafficPolicy:
                description: Traffic policies to apply (load balancing policy, connection
                  pool sizes, outlier detection).
                properties:
                  connectionPool:
                    properties:
                      http:
                        description: HTTP connection pool settings.
                        properties:
                          h2UpgradePolicy:
                            description: |-
                              Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                              Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                            enum:
                            - DEFAULT
                            - DO_NOT_UPGRADE
                            - UPGRADE
                            type: string
                          http1MaxPendingRequests:
                            description: Maximum number of requests that will be queued
                              while waiting for a ready connection pool connection.
                            format: int32
                            type: integer
                          http2MaxRequests:
                            description: Maximum number of active requests to a destination.
                            format: int32
                            type: integer
                          idleTimeout:
                            description: The idle timeout for upstream connection
                              pool connections.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          maxConcurrentStreams:
                            description: The maximum number of concurrent streams
                              allowed for a peer on one HTTP/2 connection.
                            format: int32
                            type: integer
                          maxRequestsPerConnection:
                            description: Maximum number of requests per connection
                              to a backend.
                            format: int32
                            type: integer
                          maxRetries:
                            description: Maximum number of retries that can be outstanding
                              to all hosts in a cluster at a given time.
                            format: int32
                            type: integer
                          useClientProtocol:
                            description: If set to true, client protocol will be preserved
                              while initiating connection to backend.
                            type: boolean
                        type: object
                      tcp:
                        description: Settings common to both HTTP and TCP upstream
                          connections.
                        properties:
                          connectTimeout:
                            description: TCP connection timeout.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          idleTimeout:
                            description: The idle timeout for TCP connections.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          maxConnectionDuration:
                            description: The maximum duration of a connection.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          maxConnections:
                            description: Maximum number of HTTP1 /TCP connections
                              to a destination host.
                            format: int32
                            type: integer
                          tcpKeepalive:
                            description: If set then set SO_KEEPALIVE on the socket
                              to enable TCP Keepalives.
                            properties:
                              interval:
                                description: The time duration between keep-alive
                                  probes.
                                type: string
                                x-kubernetes-validations:
                                - message: must be a valid duration greater than 1ms
                                  rule: duration(self) >= duration('1ms')
                              probes:
                                description: Maximum number of keepalive probes to
                                  send without response before deciding the connection
                                  is dead.
                                maximum: **********
                                minimum: 0
                                type: integer
                              time:
                                description: The time duration a connection needs
                                  to be idle before keep-alive probes start being
                                  sent.
                                type: string
                                x-kubernetes-validations:
                                - message: must be a valid duration greater than 1ms
                                  rule: duration(self) >= duration('1ms')
                            type: object
                        type: object
                    type: object
                  loadBalancer:
                    description: Settings controlling the load balancer algorithms.
                    oneOf:
                    - not:
                        anyOf:
                        - required:
                          - simple
                        - required:
                          - consistentHash
                    - required:
                      - simple
                    - required:
                      - consistentHash
                    properties:
                      consistentHash:
                        allOf:
                        - oneOf:
                          - not:
                              anyOf:
                              - required:
                                - httpHeaderName
                              - required:
                                - httpCookie
                              - required:
                                - useSourceIp
                              - required:
                                - httpQueryParameterName
                          - required:
                            - httpHeaderName
                          - required:
                            - httpCookie
                          - required:
                            - useSourceIp
                          - required:
                            - httpQueryParameterName
                        - oneOf:
                          - not:
                              anyOf:
                              - required:
                                - ringHash
                              - required:
                                - maglev
                          - required:
                            - ringHash
                          - required:
                            - maglev
                        properties:
                          httpCookie:
                            description: Hash based on HTTP cookie.
                            properties:
                              name:
                                description: Name of the cookie.
                                type: string
                              path:
                                description: Path to set for the cookie.
                                type: string
                              ttl:
                                description: Lifetime of the cookie.
                                type: string
                            required:
                            - name
                            type: object
                          httpHeaderName:
                            description: Hash based on a specific HTTP header.
                            type: string
                          httpQueryParameterName:
                            description: Hash based on a specific HTTP query parameter.
                            type: string
                          maglev:
                            description: The Maglev load balancer implements consistent
                              hashing to backend hosts.
                            properties:
                              tableSize:
                                description: The table size for Maglev hashing.
                                minimum: 0
                                type: integer
                            type: object
                          minimumRingSize:
                            description: Deprecated.
                            minimum: 0
                            type: integer
                          ringHash:
                            description: The ring/modulo hash load balancer implements
                              consistent hashing to backend hosts.
                            properties:
                              minimumRingSize:
                                description: The minimum number of virtual nodes to
                                  use for the hash ring.
                                minimum: 0
                                type: integer
                            type: object
                          useSourceIp:
                            description: Hash based on the source IP address.
                            type: boolean
                        type: object
                      localityLbSetting:
                        properties:
                          distribute:
                            description: 'Optional: only one of distribute, failover
                              or failoverPriority can be set.'
                            items:
                              properties:
                                from:
                                  description: Originating locality, '/' separated,
                                    e.g.
                                  type: string
                                to:
                                  additionalProperties:
                                    maximum: **********
                                    minimum: 0
                                    type: integer
                                  description: Map of upstream localities to traffic
                                    distribution weights.
                                  type: object
                              type: object
                            type: array
                          enabled:
                            description: Enable locality load balancing.
                            nullable: true
                            type: boolean
                          failover:
                            description: 'Optional: only one of distribute, failover
                              or failoverPriority can be set.'
                            items:
                              properties:
                                from:
                                  description: Originating region.
                                  type: string
                                to:
                                  description: Destination region the traffic will
                                    fail over to when endpoints in the 'from' region
                                    becomes unhealthy.
                                  type: string
                              type: object
                            type: array
                          failoverPriority:
                            description: failoverPriority is an ordered list of labels
                              used to sort endpoints to do priority based load balancing.
                            items:
                              type: string
                            type: array
                        type: object
                      simple:
                        description: |2-


                          Valid Options: LEAST_CONN, RANDOM, PASSTHROUGH, ROUND_ROBIN, LEAST_REQUEST
                        enum:
                        - UNSPECIFIED
                        - LEAST_CONN
                        - RANDOM
                        - PASSTHROUGH
                        - ROUND_ROBIN
                        - LEAST_REQUEST
                        type: string
                      warmup:
                        description: Represents the warmup configuration of Service.
                        properties:
                          aggression:
                            description: This parameter controls the speed of traffic
                              increase over the warmup duration.
                            format: double
                            minimum: 1
                            nullable: true
                            type: number
                          duration:
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          minimumPercent:
                            format: double
                            maximum: 100
                            minimum: 0
                            nullable: true
                            type: number
                        required:
                        - duration
                        type: object
                      warmupDurationSecs:
                        description: 'Deprecated: use `warmup` instead.'
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                    type: object
                  outlierDetection:
                    properties:
                      baseEjectionTime:
                        description: Minimum ejection duration.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      consecutive5xxErrors:
                        description: Number of 5xx errors before a host is ejected
                          from the connection pool.
                        maximum: **********
                        minimum: 0
                        nullable: true
                        type: integer
                      consecutiveErrors:
                        format: int32
                        type: integer
                      consecutiveGatewayErrors:
                        description: Number of gateway errors before a host is ejected
                          from the connection pool.
                        maximum: **********
                        minimum: 0
                        nullable: true
                        type: integer
                      consecutiveLocalOriginFailures:
                        description: The number of consecutive locally originated
                          failures before ejection occurs.
                        maximum: **********
                        minimum: 0
                        nullable: true
                        type: integer
                      interval:
                        description: Time interval between ejection sweep analysis.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      maxEjectionPercent:
                        description: Maximum % of hosts in the load balancing pool
                          for the upstream service that can be ejected.
                        format: int32
                        type: integer
                      minHealthPercent:
                        description: Outlier detection will be enabled as long as
                          the associated load balancing pool has at least `minHealthPercent`
                          hosts in healthy mode.
                        format: int32
                        type: integer
                      splitExternalLocalOriginErrors:
                        description: Determines whether to distinguish local origin
                          failures from external errors.
                        type: boolean
                    type: object
                  portLevelSettings:
                    description: Traffic policies specific to individual ports.
                    items:
                      properties:
                        connectionPool:
                          properties:
                            http:
                              description: HTTP connection pool settings.
                              properties:
                                h2UpgradePolicy:
                                  description: |-
                                    Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                                    Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                                  enum:
                                  - DEFAULT
                                  - DO_NOT_UPGRADE
                                  - UPGRADE
                                  type: string
                                http1MaxPendingRequests:
                                  description: Maximum number of requests that will
                                    be queued while waiting for a ready connection
                                    pool connection.
                                  format: int32
                                  type: integer
                                http2MaxRequests:
                                  description: Maximum number of active requests to
                                    a destination.
                                  format: int32
                                  type: integer
                                idleTimeout:
                                  description: The idle timeout for upstream connection
                                    pool connections.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConcurrentStreams:
                                  description: The maximum number of concurrent streams
                                    allowed for a peer on one HTTP/2 connection.
                                  format: int32
                                  type: integer
                                maxRequestsPerConnection:
                                  description: Maximum number of requests per connection
                                    to a backend.
                                  format: int32
                                  type: integer
                                maxRetries:
                                  description: Maximum number of retries that can
                                    be outstanding to all hosts in a cluster at a
                                    given time.
                                  format: int32
                                  type: integer
                                useClientProtocol:
                                  description: If set to true, client protocol will
                                    be preserved while initiating connection to backend.
                                  type: boolean
                              type: object
                            tcp:
                              description: Settings common to both HTTP and TCP upstream
                                connections.
                              properties:
                                connectTimeout:
                                  description: TCP connection timeout.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                idleTimeout:
                                  description: The idle timeout for TCP connections.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConnectionDuration:
                                  description: The maximum duration of a connection.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConnections:
                                  description: Maximum number of HTTP1 /TCP connections
                                    to a destination host.
                                  format: int32
                                  type: integer
                                tcpKeepalive:
                                  description: If set then set SO_KEEPALIVE on the
                                    socket to enable TCP Keepalives.
                                  properties:
                                    interval:
                                      description: The time duration between keep-alive
                                        probes.
                                      type: string
                                      x-kubernetes-validations:
                                      - message: must be a valid duration greater
                                          than 1ms
                                        rule: duration(self) >= duration('1ms')
                                    probes:
                                      description: Maximum number of keepalive probes
                                        to send without response before deciding the
                                        connection is dead.
                                      maximum: **********
                                      minimum: 0
                                      type: integer
                                    time:
                                      description: The time duration a connection
                                        needs to be idle before keep-alive probes
                                        start being sent.
                                      type: string
                                      x-kubernetes-validations:
                                      - message: must be a valid duration greater
                                          than 1ms
                                        rule: duration(self) >= duration('1ms')
                                  type: object
                              type: object
                          type: object
                        loadBalancer:
                          description: Settings controlling the load balancer algorithms.
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - simple
                              - required:
                                - consistentHash
                          - required:
                            - simple
                          - required:
                            - consistentHash
                          properties:
                            consistentHash:
                              allOf:
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - httpHeaderName
                                    - required:
                                      - httpCookie
                                    - required:
                                      - useSourceIp
                                    - required:
                                      - httpQueryParameterName
                                - required:
                                  - httpHeaderName
                                - required:
                                  - httpCookie
                                - required:
                                  - useSourceIp
                                - required:
                                  - httpQueryParameterName
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - ringHash
                                    - required:
                                      - maglev
                                - required:
                                  - ringHash
                                - required:
                                  - maglev
                              properties:
                                httpCookie:
                                  description: Hash based on HTTP cookie.
                                  properties:
                                    name:
                                      description: Name of the cookie.
                                      type: string
                                    path:
                                      description: Path to set for the cookie.
                                      type: string
                                    ttl:
                                      description: Lifetime of the cookie.
                                      type: string
                                  required:
                                  - name
                                  type: object
                                httpHeaderName:
                                  description: Hash based on a specific HTTP header.
                                  type: string
                                httpQueryParameterName:
                                  description: Hash based on a specific HTTP query
                                    parameter.
                                  type: string
                                maglev:
                                  description: The Maglev load balancer implements
                                    consistent hashing to backend hosts.
                                  properties:
                                    tableSize:
                                      description: The table size for Maglev hashing.
                                      minimum: 0
                                      type: integer
                                  type: object
                                minimumRingSize:
                                  description: Deprecated.
                                  minimum: 0
                                  type: integer
                                ringHash:
                                  description: The ring/modulo hash load balancer
                                    implements consistent hashing to backend hosts.
                                  properties:
                                    minimumRingSize:
                                      description: The minimum number of virtual nodes
                                        to use for the hash ring.
                                      minimum: 0
                                      type: integer
                                  type: object
                                useSourceIp:
                                  description: Hash based on the source IP address.
                                  type: boolean
                              type: object
                            localityLbSetting:
                              properties:
                                distribute:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating locality, '/' separated,
                                          e.g.
                                        type: string
                                      to:
                                        additionalProperties:
                                          maximum: **********
                                          minimum: 0
                                          type: integer
                                        description: Map of upstream localities to
                                          traffic distribution weights.
                                        type: object
                                    type: object
                                  type: array
                                enabled:
                                  description: Enable locality load balancing.
                                  nullable: true
                                  type: boolean
                                failover:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating region.
                                        type: string
                                      to:
                                        description: Destination region the traffic
                                          will fail over to when endpoints in the
                                          'from' region becomes unhealthy.
                                        type: string
                                    type: object
                                  type: array
                                failoverPriority:
                                  description: failoverPriority is an ordered list
                                    of labels used to sort endpoints to do priority
                                    based load balancing.
                                  items:
                                    type: string
                                  type: array
                              type: object
                            simple:
                              description: |2-


                                Valid Options: LEAST_CONN, RANDOM, PASSTHROUGH, ROUND_ROBIN, LEAST_REQUEST
                              enum:
                              - UNSPECIFIED
                              - LEAST_CONN
                              - RANDOM
                              - PASSTHROUGH
                              - ROUND_ROBIN
                              - LEAST_REQUEST
                              type: string
                            warmup:
                              description: Represents the warmup configuration of
                                Service.
                              properties:
                                aggression:
                                  description: This parameter controls the speed of
                                    traffic increase over the warmup duration.
                                  format: double
                                  minimum: 1
                                  nullable: true
                                  type: number
                                duration:
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                minimumPercent:
                                  format: double
                                  maximum: 100
                                  minimum: 0
                                  nullable: true
                                  type: number
                              required:
                              - duration
                              type: object
                            warmupDurationSecs:
                              description: 'Deprecated: use `warmup` instead.'
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                          type: object
                        outlierDetection:
                          properties:
                            baseEjectionTime:
                              description: Minimum ejection duration.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            consecutive5xxErrors:
                              description: Number of 5xx errors before a host is ejected
                                from the connection pool.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            consecutiveErrors:
                              format: int32
                              type: integer
                            consecutiveGatewayErrors:
                              description: Number of gateway errors before a host
                                is ejected from the connection pool.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            consecutiveLocalOriginFailures:
                              description: The number of consecutive locally originated
                                failures before ejection occurs.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            interval:
                              description: Time interval between ejection sweep analysis.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            maxEjectionPercent:
                              description: Maximum % of hosts in the load balancing
                                pool for the upstream service that can be ejected.
                              format: int32
                              type: integer
                            minHealthPercent:
                              description: Outlier detection will be enabled as long
                                as the associated load balancing pool has at least
                                `minHealthPercent` hosts in healthy mode.
                              format: int32
                              type: integer
                            splitExternalLocalOriginErrors:
                              description: Determines whether to distinguish local
                                origin failures from external errors.
                              type: boolean
                          type: object
                        port:
                          description: Specifies the number of a port on the destination
                            service on which this policy is being applied.
                          properties:
                            number:
                              maximum: **********
                              minimum: 0
                              type: integer
                          type: object
                        tls:
                          description: TLS related settings for connections to the
                            upstream service.
                          properties:
                            caCertificates:
                              description: 'OPTIONAL: The path to the file containing
                                certificate authority certificates to use in verifying
                                a presented server certificate.'
                              type: string
                            caCrl:
                              description: 'OPTIONAL: The path to the file containing
                                the certificate revocation list (CRL) to use in verifying
                                a presented server certificate.'
                              type: string
                            clientCertificate:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            credentialName:
                              description: The name of the secret that holds the TLS
                                certs for the client including the CA certificates.
                              type: string
                            insecureSkipVerify:
                              description: '`insecureSkipVerify` specifies whether
                                the proxy should skip verifying the CA signature and
                                SAN for the server certificate corresponding to the
                                host.'
                              nullable: true
                              type: boolean
                            mode:
                              description: |-
                                Indicates whether connections to this port should be secured using TLS.

                                Valid Options: DISABLE, SIMPLE, MUTUAL, ISTIO_MUTUAL
                              enum:
                              - DISABLE
                              - SIMPLE
                              - MUTUAL
                              - ISTIO_MUTUAL
                              type: string
                            privateKey:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            sni:
                              description: SNI string to present to the server during
                                TLS handshake.
                              type: string
                            subjectAltNames:
                              description: A list of alternate names to verify the
                                subject identity in the certificate.
                              items:
                                type: string
                              type: array
                          type: object
                      type: object
                    maxItems: 4096
                    type: array
                  proxyProtocol:
                    description: The upstream PROXY protocol settings.
                    properties:
                      version:
                        description: |-
                          The PROXY protocol version to use.

                          Valid Options: V1, V2
                        enum:
                        - V1
                        - V2
                        type: string
                    type: object
                  tls:
                    description: TLS related settings for connections to the upstream
                      service.
                    properties:
                      caCertificates:
                        description: 'OPTIONAL: The path to the file containing certificate
                          authority certificates to use in verifying a presented server
                          certificate.'
                        type: string
                      caCrl:
                        description: 'OPTIONAL: The path to the file containing the
                          certificate revocation list (CRL) to use in verifying a
                          presented server certificate.'
                        type: string
                      clientCertificate:
                        description: REQUIRED if mode is `MUTUAL`.
                        type: string
                      credentialName:
                        description: The name of the secret that holds the TLS certs
                          for the client including the CA certificates.
                        type: string
                      insecureSkipVerify:
                        description: '`insecureSkipVerify` specifies whether the proxy
                          should skip verifying the CA signature and SAN for the server
                          certificate corresponding to the host.'
                        nullable: true
                        type: boolean
                      mode:
                        description: |-
                          Indicates whether connections to this port should be secured using TLS.

                          Valid Options: DISABLE, SIMPLE, MUTUAL, ISTIO_MUTUAL
                        enum:
                        - DISABLE
                        - SIMPLE
                        - MUTUAL
                        - ISTIO_MUTUAL
                        type: string
                      privateKey:
                        description: REQUIRED if mode is `MUTUAL`.
                        type: string
                      sni:
                        description: SNI string to present to the server during TLS
                          handshake.
                        type: string
                      subjectAltNames:
                        description: A list of alternate names to verify the subject
                          identity in the certificate.
                        items:
                          type: string
                        type: array
                    type: object
                  tunnel:
                    description: Configuration of tunneling TCP over other transport
                      or application layers for the host configured in the DestinationRule.
                    properties:
                      protocol:
                        description: Specifies which protocol to use for tunneling
                          the downstream connection.
                        type: string
                      targetHost:
                        description: Specifies a host to which the downstream connection
                          is tunneled.
                        type: string
                      targetPort:
                        description: Specifies a port to which the downstream connection
                          is tunneled.
                        maximum: **********
                        minimum: 0
                        type: integer
                    required:
                    - targetHost
                    - targetPort
                    type: object
                type: object
              workloadSelector:
                description: Criteria used to select the specific set of pods/VMs
                  on which this `DestinationRule` configuration should be applied.
                properties:
                  matchLabels:
                    additionalProperties:
                      maxLength: 63
                      type: string
                      x-kubernetes-validations:
                      - message: wildcard not allowed in label value match
                        rule: '!self.contains(''*'')'
                    description: One or more labels that indicate a specific set of
                      pods/VMs on which a policy should be applied.
                    maxProperties: 4096
                    type: object
                    x-kubernetes-validations:
                    - message: wildcard not allowed in label key match
                      rule: self.all(key, !key.contains('*'))
                    - message: key must not be empty
                      rule: self.all(key, key.size() != 0)
                type: object
            required:
            - host
            type: object
          status:
            properties:
              conditions:
                description: Current service state of the resource.
                items:
                  properties:
                    lastProbeTime:
                      description: Last time we probed the condition.
                      format: date-time
                      type: string
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      format: date-time
                      type: string
                    message:
                      description: Human-readable message indicating details about
                        last transition.
                      type: string
                    reason:
                      description: Unique, one-word, CamelCase reason for the condition's
                        last transition.
                      type: string
                    status:
                      description: Status is the status of the condition.
                      type: string
                    type:
                      description: Type is the type of the condition.
                      type: string
                  type: object
                type: array
              observedGeneration:
                anyOf:
                - type: integer
                - type: string
                description: Resource Generation to which the Reconciled Condition
                  refers.
                x-kubernetes-int-or-string: true
              validationMessages:
                description: Includes any errors or warnings detected by Istio's analyzers.
                items:
                  properties:
                    documentationUrl:
                      description: A url pointing to the Istio documentation for this
                        specific error type.
                      type: string
                    level:
                      description: |-
                        Represents how severe a message is.

                        Valid Options: UNKNOWN, ERROR, WARNING, INFO
                      enum:
                      - UNKNOWN
                      - ERROR
                      - WARNING
                      - INFO
                      type: string
                    type:
                      properties:
                        code:
                          description: A 7 character code matching `^IST[0-9]{4}$`
                            intended to uniquely identify the message type.
                          type: string
                        name:
                          description: A human-readable name for the message type.
                          type: string
                      type: object
                  type: object
                type: array
            type: object
            x-kubernetes-preserve-unknown-fields: true
        type: object
    served: true
    storage: false
    subresources:
      status: {}
  - additionalPrinterColumns:
    - description: The name of a service from the service registry
      jsonPath: .spec.host
      name: Host
      type: string
    - description: 'CreationTimestamp is a timestamp representing the server time
        when this object was created. It is not guaranteed to be set in happens-before
        order across separate operations. Clients may not set this value. It is represented
        in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for
        lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata'
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1beta1
    schema:
      openAPIV3Schema:
        properties:
          spec:
            description: 'Configuration affecting load balancing, outlier detection,
              etc. See more details at: https://istio.io/docs/reference/config/networking/destination-rule.html'
            properties:
              exportTo:
                description: A list of namespaces to which this destination rule is
                  exported.
                items:
                  type: string
                type: array
              host:
                description: The name of a service from the service registry.
                type: string
              subsets:
                description: One or more named sets that represent individual versions
                  of a service.
                items:
                  properties:
                    labels:
                      additionalProperties:
                        type: string
                      description: Labels apply a filter over the endpoints of a service
                        in the service registry.
                      type: object
                    name:
                      description: Name of the subset.
                      type: string
                    trafficPolicy:
                      description: Traffic policies that apply to this subset.
                      properties:
                        connectionPool:
                          properties:
                            http:
                              description: HTTP connection pool settings.
                              properties:
                                h2UpgradePolicy:
                                  description: |-
                                    Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                                    Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                                  enum:
                                  - DEFAULT
                                  - DO_NOT_UPGRADE
                                  - UPGRADE
                                  type: string
                                http1MaxPendingRequests:
                                  description: Maximum number of requests that will
                                    be queued while waiting for a ready connection
                                    pool connection.
                                  format: int32
                                  type: integer
                                http2MaxRequests:
                                  description: Maximum number of active requests to
                                    a destination.
                                  format: int32
                                  type: integer
                                idleTimeout:
                                  description: The idle timeout for upstream connection
                                    pool connections.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConcurrentStreams:
                                  description: The maximum number of concurrent streams
                                    allowed for a peer on one HTTP/2 connection.
                                  format: int32
                                  type: integer
                                maxRequestsPerConnection:
                                  description: Maximum number of requests per connection
                                    to a backend.
                                  format: int32
                                  type: integer
                                maxRetries:
                                  description: Maximum number of retries that can
                                    be outstanding to all hosts in a cluster at a
                                    given time.
                                  format: int32
                                  type: integer
                                useClientProtocol:
                                  description: If set to true, client protocol will
                                    be preserved while initiating connection to backend.
                                  type: boolean
                              type: object
                            tcp:
                              description: Settings common to both HTTP and TCP upstream
                                connections.
                              properties:
                                connectTimeout:
                                  description: TCP connection timeout.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                idleTimeout:
                                  description: The idle timeout for TCP connections.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConnectionDuration:
                                  description: The maximum duration of a connection.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConnections:
                                  description: Maximum number of HTTP1 /TCP connections
                                    to a destination host.
                                  format: int32
                                  type: integer
                                tcpKeepalive:
                                  description: If set then set SO_KEEPALIVE on the
                                    socket to enable TCP Keepalives.
                                  properties:
                                    interval:
                                      description: The time duration between keep-alive
                                        probes.
                                      type: string
                                      x-kubernetes-validations:
                                      - message: must be a valid duration greater
                                          than 1ms
                                        rule: duration(self) >= duration('1ms')
                                    probes:
                                      description: Maximum number of keepalive probes
                                        to send without response before deciding the
                                        connection is dead.
                                      maximum: **********
                                      minimum: 0
                                      type: integer
                                    time:
                                      description: The time duration a connection
                                        needs to be idle before keep-alive probes
                                        start being sent.
                                      type: string
                                      x-kubernetes-validations:
                                      - message: must be a valid duration greater
                                          than 1ms
                                        rule: duration(self) >= duration('1ms')
                                  type: object
                              type: object
                          type: object
                        loadBalancer:
                          description: Settings controlling the load balancer algorithms.
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - simple
                              - required:
                                - consistentHash
                          - required:
                            - simple
                          - required:
                            - consistentHash
                          properties:
                            consistentHash:
                              allOf:
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - httpHeaderName
                                    - required:
                                      - httpCookie
                                    - required:
                                      - useSourceIp
                                    - required:
                                      - httpQueryParameterName
                                - required:
                                  - httpHeaderName
                                - required:
                                  - httpCookie
                                - required:
                                  - useSourceIp
                                - required:
                                  - httpQueryParameterName
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - ringHash
                                    - required:
                                      - maglev
                                - required:
                                  - ringHash
                                - required:
                                  - maglev
                              properties:
                                httpCookie:
                                  description: Hash based on HTTP cookie.
                                  properties:
                                    name:
                                      description: Name of the cookie.
                                      type: string
                                    path:
                                      description: Path to set for the cookie.
                                      type: string
                                    ttl:
                                      description: Lifetime of the cookie.
                                      type: string
                                  required:
                                  - name
                                  type: object
                                httpHeaderName:
                                  description: Hash based on a specific HTTP header.
                                  type: string
                                httpQueryParameterName:
                                  description: Hash based on a specific HTTP query
                                    parameter.
                                  type: string
                                maglev:
                                  description: The Maglev load balancer implements
                                    consistent hashing to backend hosts.
                                  properties:
                                    tableSize:
                                      description: The table size for Maglev hashing.
                                      minimum: 0
                                      type: integer
                                  type: object
                                minimumRingSize:
                                  description: Deprecated.
                                  minimum: 0
                                  type: integer
                                ringHash:
                                  description: The ring/modulo hash load balancer
                                    implements consistent hashing to backend hosts.
                                  properties:
                                    minimumRingSize:
                                      description: The minimum number of virtual nodes
                                        to use for the hash ring.
                                      minimum: 0
                                      type: integer
                                  type: object
                                useSourceIp:
                                  description: Hash based on the source IP address.
                                  type: boolean
                              type: object
                            localityLbSetting:
                              properties:
                                distribute:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating locality, '/' separated,
                                          e.g.
                                        type: string
                                      to:
                                        additionalProperties:
                                          maximum: **********
                                          minimum: 0
                                          type: integer
                                        description: Map of upstream localities to
                                          traffic distribution weights.
                                        type: object
                                    type: object
                                  type: array
                                enabled:
                                  description: Enable locality load balancing.
                                  nullable: true
                                  type: boolean
                                failover:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating region.
                                        type: string
                                      to:
                                        description: Destination region the traffic
                                          will fail over to when endpoints in the
                                          'from' region becomes unhealthy.
                                        type: string
                                    type: object
                                  type: array
                                failoverPriority:
                                  description: failoverPriority is an ordered list
                                    of labels used to sort endpoints to do priority
                                    based load balancing.
                                  items:
                                    type: string
                                  type: array
                              type: object
                            simple:
                              description: |2-


                                Valid Options: LEAST_CONN, RANDOM, PASSTHROUGH, ROUND_ROBIN, LEAST_REQUEST
                              enum:
                              - UNSPECIFIED
                              - LEAST_CONN
                              - RANDOM
                              - PASSTHROUGH
                              - ROUND_ROBIN
                              - LEAST_REQUEST
                              type: string
                            warmup:
                              description: Represents the warmup configuration of
                                Service.
                              properties:
                                aggression:
                                  description: This parameter controls the speed of
                                    traffic increase over the warmup duration.
                                  format: double
                                  minimum: 1
                                  nullable: true
                                  type: number
                                duration:
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                minimumPercent:
                                  format: double
                                  maximum: 100
                                  minimum: 0
                                  nullable: true
                                  type: number
                              required:
                              - duration
                              type: object
                            warmupDurationSecs:
                              description: 'Deprecated: use `warmup` instead.'
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                          type: object
                        outlierDetection:
                          properties:
                            baseEjectionTime:
                              description: Minimum ejection duration.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            consecutive5xxErrors:
                              description: Number of 5xx errors before a host is ejected
                                from the connection pool.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            consecutiveErrors:
                              format: int32
                              type: integer
                            consecutiveGatewayErrors:
                              description: Number of gateway errors before a host
                                is ejected from the connection pool.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            consecutiveLocalOriginFailures:
                              description: The number of consecutive locally originated
                                failures before ejection occurs.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            interval:
                              description: Time interval between ejection sweep analysis.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            maxEjectionPercent:
                              description: Maximum % of hosts in the load balancing
                                pool for the upstream service that can be ejected.
                              format: int32
                              type: integer
                            minHealthPercent:
                              description: Outlier detection will be enabled as long
                                as the associated load balancing pool has at least
                                `minHealthPercent` hosts in healthy mode.
                              format: int32
                              type: integer
                            splitExternalLocalOriginErrors:
                              description: Determines whether to distinguish local
                                origin failures from external errors.
                              type: boolean
                          type: object
                        portLevelSettings:
                          description: Traffic policies specific to individual ports.
                          items:
                            properties:
                              connectionPool:
                                properties:
                                  http:
                                    description: HTTP connection pool settings.
                                    properties:
                                      h2UpgradePolicy:
                                        description: |-
                                          Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                                          Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                                        enum:
                                        - DEFAULT
                                        - DO_NOT_UPGRADE
                                        - UPGRADE
                                        type: string
                                      http1MaxPendingRequests:
                                        description: Maximum number of requests that
                                          will be queued while waiting for a ready
                                          connection pool connection.
                                        format: int32
                                        type: integer
                                      http2MaxRequests:
                                        description: Maximum number of active requests
                                          to a destination.
                                        format: int32
                                        type: integer
                                      idleTimeout:
                                        description: The idle timeout for upstream
                                          connection pool connections.
                                        type: string
                                        x-kubernetes-validations:
                                        - message: must be a valid duration greater
                                            than 1ms
                                          rule: duration(self) >= duration('1ms')
                                      maxConcurrentStreams:
                                        description: The maximum number of concurrent
                                          streams allowed for a peer on one HTTP/2
                                          connection.
                                        format: int32
                                        type: integer
                                      maxRequestsPerConnection:
                                        description: Maximum number of requests per
                                          connection to a backend.
                                        format: int32
                                        type: integer
                                      maxRetries:
                                        description: Maximum number of retries that
                                          can be outstanding to all hosts in a cluster
                                          at a given time.
                                        format: int32
                                        type: integer
                                      useClientProtocol:
                                        description: If set to true, client protocol
                                          will be preserved while initiating connection
                                          to backend.
                                        type: boolean
                                    type: object
                                  tcp:
                                    description: Settings common to both HTTP and
                                      TCP upstream connections.
                                    properties:
                                      connectTimeout:
                                        description: TCP connection timeout.
                                        type: string
                                        x-kubernetes-validations:
                                        - message: must be a valid duration greater
                                            than 1ms
                                          rule: duration(self) >= duration('1ms')
                                      idleTimeout:
                                        description: The idle timeout for TCP connections.
                                        type: string
                                        x-kubernetes-validations:
                                        - message: must be a valid duration greater
                                            than 1ms
                                          rule: duration(self) >= duration('1ms')
                                      maxConnectionDuration:
                                        description: The maximum duration of a connection.
                                        type: string
                                        x-kubernetes-validations:
                                        - message: must be a valid duration greater
                                            than 1ms
                                          rule: duration(self) >= duration('1ms')
                                      maxConnections:
                                        description: Maximum number of HTTP1 /TCP
                                          connections to a destination host.
                                        format: int32
                                        type: integer
                                      tcpKeepalive:
                                        description: If set then set SO_KEEPALIVE
                                          on the socket to enable TCP Keepalives.
                                        properties:
                                          interval:
                                            description: The time duration between
                                              keep-alive probes.
                                            type: string
                                            x-kubernetes-validations:
                                            - message: must be a valid duration greater
                                                than 1ms
                                              rule: duration(self) >= duration('1ms')
                                          probes:
                                            description: Maximum number of keepalive
                                              probes to send without response before
                                              deciding the connection is dead.
                                            maximum: **********
                                            minimum: 0
                                            type: integer
                                          time:
                                            description: The time duration a connection
                                              needs to be idle before keep-alive probes
                                              start being sent.
                                            type: string
                                            x-kubernetes-validations:
                                            - message: must be a valid duration greater
                                                than 1ms
                                              rule: duration(self) >= duration('1ms')
                                        type: object
                                    type: object
                                type: object
                              loadBalancer:
                                description: Settings controlling the load balancer
                                  algorithms.
                                oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - simple
                                    - required:
                                      - consistentHash
                                - required:
                                  - simple
                                - required:
                                  - consistentHash
                                properties:
                                  consistentHash:
                                    allOf:
                                    - oneOf:
                                      - not:
                                          anyOf:
                                          - required:
                                            - httpHeaderName
                                          - required:
                                            - httpCookie
                                          - required:
                                            - useSourceIp
                                          - required:
                                            - httpQueryParameterName
                                      - required:
                                        - httpHeaderName
                                      - required:
                                        - httpCookie
                                      - required:
                                        - useSourceIp
                                      - required:
                                        - httpQueryParameterName
                                    - oneOf:
                                      - not:
                                          anyOf:
                                          - required:
                                            - ringHash
                                          - required:
                                            - maglev
                                      - required:
                                        - ringHash
                                      - required:
                                        - maglev
                                    properties:
                                      httpCookie:
                                        description: Hash based on HTTP cookie.
                                        properties:
                                          name:
                                            description: Name of the cookie.
                                            type: string
                                          path:
                                            description: Path to set for the cookie.
                                            type: string
                                          ttl:
                                            description: Lifetime of the cookie.
                                            type: string
                                        required:
                                        - name
                                        type: object
                                      httpHeaderName:
                                        description: Hash based on a specific HTTP
                                          header.
                                        type: string
                                      httpQueryParameterName:
                                        description: Hash based on a specific HTTP
                                          query parameter.
                                        type: string
                                      maglev:
                                        description: The Maglev load balancer implements
                                          consistent hashing to backend hosts.
                                        properties:
                                          tableSize:
                                            description: The table size for Maglev
                                              hashing.
                                            minimum: 0
                                            type: integer
                                        type: object
                                      minimumRingSize:
                                        description: Deprecated.
                                        minimum: 0
                                        type: integer
                                      ringHash:
                                        description: The ring/modulo hash load balancer
                                          implements consistent hashing to backend
                                          hosts.
                                        properties:
                                          minimumRingSize:
                                            description: The minimum number of virtual
                                              nodes to use for the hash ring.
                                            minimum: 0
                                            type: integer
                                        type: object
                                      useSourceIp:
                                        description: Hash based on the source IP address.
                                        type: boolean
                                    type: object
                                  localityLbSetting:
                                    properties:
                                      distribute:
                                        description: 'Optional: only one of distribute,
                                          failover or failoverPriority can be set.'
                                        items:
                                          properties:
                                            from:
                                              description: Originating locality, '/'
                                                separated, e.g.
                                              type: string
                                            to:
                                              additionalProperties:
                                                maximum: **********
                                                minimum: 0
                                                type: integer
                                              description: Map of upstream localities
                                                to traffic distribution weights.
                                              type: object
                                          type: object
                                        type: array
                                      enabled:
                                        description: Enable locality load balancing.
                                        nullable: true
                                        type: boolean
                                      failover:
                                        description: 'Optional: only one of distribute,
                                          failover or failoverPriority can be set.'
                                        items:
                                          properties:
                                            from:
                                              description: Originating region.
                                              type: string
                                            to:
                                              description: Destination region the
                                                traffic will fail over to when endpoints
                                                in the 'from' region becomes unhealthy.
                                              type: string
                                          type: object
                                        type: array
                                      failoverPriority:
                                        description: failoverPriority is an ordered
                                          list of labels used to sort endpoints to
                                          do priority based load balancing.
                                        items:
                                          type: string
                                        type: array
                                    type: object
                                  simple:
                                    description: |2-


                                      Valid Options: LEAST_CONN, RANDOM, PASSTHROUGH, ROUND_ROBIN, LEAST_REQUEST
                                    enum:
                                    - UNSPECIFIED
                                    - LEAST_CONN
                                    - RANDOM
                                    - PASSTHROUGH
                                    - ROUND_ROBIN
                                    - LEAST_REQUEST
                                    type: string
                                  warmup:
                                    description: Represents the warmup configuration
                                      of Service.
                                    properties:
                                      aggression:
                                        description: This parameter controls the speed
                                          of traffic increase over the warmup duration.
                                        format: double
                                        minimum: 1
                                        nullable: true
                                        type: number
                                      duration:
                                        type: string
                                        x-kubernetes-validations:
                                        - message: must be a valid duration greater
                                            than 1ms
                                          rule: duration(self) >= duration('1ms')
                                      minimumPercent:
                                        format: double
                                        maximum: 100
                                        minimum: 0
                                        nullable: true
                                        type: number
                                    required:
                                    - duration
                                    type: object
                                  warmupDurationSecs:
                                    description: 'Deprecated: use `warmup` instead.'
                                    type: string
                                    x-kubernetes-validations:
                                    - message: must be a valid duration greater than
                                        1ms
                                      rule: duration(self) >= duration('1ms')
                                type: object
                              outlierDetection:
                                properties:
                                  baseEjectionTime:
                                    description: Minimum ejection duration.
                                    type: string
                                    x-kubernetes-validations:
                                    - message: must be a valid duration greater than
                                        1ms
                                      rule: duration(self) >= duration('1ms')
                                  consecutive5xxErrors:
                                    description: Number of 5xx errors before a host
                                      is ejected from the connection pool.
                                    maximum: **********
                                    minimum: 0
                                    nullable: true
                                    type: integer
                                  consecutiveErrors:
                                    format: int32
                                    type: integer
                                  consecutiveGatewayErrors:
                                    description: Number of gateway errors before a
                                      host is ejected from the connection pool.
                                    maximum: **********
                                    minimum: 0
                                    nullable: true
                                    type: integer
                                  consecutiveLocalOriginFailures:
                                    description: The number of consecutive locally
                                      originated failures before ejection occurs.
                                    maximum: **********
                                    minimum: 0
                                    nullable: true
                                    type: integer
                                  interval:
                                    description: Time interval between ejection sweep
                                      analysis.
                                    type: string
                                    x-kubernetes-validations:
                                    - message: must be a valid duration greater than
                                        1ms
                                      rule: duration(self) >= duration('1ms')
                                  maxEjectionPercent:
                                    description: Maximum % of hosts in the load balancing
                                      pool for the upstream service that can be ejected.
                                    format: int32
                                    type: integer
                                  minHealthPercent:
                                    description: Outlier detection will be enabled
                                      as long as the associated load balancing pool
                                      has at least `minHealthPercent` hosts in healthy
                                      mode.
                                    format: int32
                                    type: integer
                                  splitExternalLocalOriginErrors:
                                    description: Determines whether to distinguish
                                      local origin failures from external errors.
                                    type: boolean
                                type: object
                              port:
                                description: Specifies the number of a port on the
                                  destination service on which this policy is being
                                  applied.
                                properties:
                                  number:
                                    maximum: **********
                                    minimum: 0
                                    type: integer
                                type: object
                              tls:
                                description: TLS related settings for connections
                                  to the upstream service.
                                properties:
                                  caCertificates:
                                    description: 'OPTIONAL: The path to the file containing
                                      certificate authority certificates to use in
                                      verifying a presented server certificate.'
                                    type: string
                                  caCrl:
                                    description: 'OPTIONAL: The path to the file containing
                                      the certificate revocation list (CRL) to use
                                      in verifying a presented server certificate.'
                                    type: string
                                  clientCertificate:
                                    description: REQUIRED if mode is `MUTUAL`.
                                    type: string
                                  credentialName:
                                    description: The name of the secret that holds
                                      the TLS certs for the client including the CA
                                      certificates.
                                    type: string
                                  insecureSkipVerify:
                                    description: '`insecureSkipVerify` specifies whether
                                      the proxy should skip verifying the CA signature
                                      and SAN for the server certificate corresponding
                                      to the host.'
                                    nullable: true
                                    type: boolean
                                  mode:
                                    description: |-
                                      Indicates whether connections to this port should be secured using TLS.

                                      Valid Options: DISABLE, SIMPLE, MUTUAL, ISTIO_MUTUAL
                                    enum:
                                    - DISABLE
                                    - SIMPLE
                                    - MUTUAL
                                    - ISTIO_MUTUAL
                                    type: string
                                  privateKey:
                                    description: REQUIRED if mode is `MUTUAL`.
                                    type: string
                                  sni:
                                    description: SNI string to present to the server
                                      during TLS handshake.
                                    type: string
                                  subjectAltNames:
                                    description: A list of alternate names to verify
                                      the subject identity in the certificate.
                                    items:
                                      type: string
                                    type: array
                                type: object
                            type: object
                          maxItems: 4096
                          type: array
                        proxyProtocol:
                          description: The upstream PROXY protocol settings.
                          properties:
                            version:
                              description: |-
                                The PROXY protocol version to use.

                                Valid Options: V1, V2
                              enum:
                              - V1
                              - V2
                              type: string
                          type: object
                        tls:
                          description: TLS related settings for connections to the
                            upstream service.
                          properties:
                            caCertificates:
                              description: 'OPTIONAL: The path to the file containing
                                certificate authority certificates to use in verifying
                                a presented server certificate.'
                              type: string
                            caCrl:
                              description: 'OPTIONAL: The path to the file containing
                                the certificate revocation list (CRL) to use in verifying
                                a presented server certificate.'
                              type: string
                            clientCertificate:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            credentialName:
                              description: The name of the secret that holds the TLS
                                certs for the client including the CA certificates.
                              type: string
                            insecureSkipVerify:
                              description: '`insecureSkipVerify` specifies whether
                                the proxy should skip verifying the CA signature and
                                SAN for the server certificate corresponding to the
                                host.'
                              nullable: true
                              type: boolean
                            mode:
                              description: |-
                                Indicates whether connections to this port should be secured using TLS.

                                Valid Options: DISABLE, SIMPLE, MUTUAL, ISTIO_MUTUAL
                              enum:
                              - DISABLE
                              - SIMPLE
                              - MUTUAL
                              - ISTIO_MUTUAL
                              type: string
                            privateKey:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            sni:
                              description: SNI string to present to the server during
                                TLS handshake.
                              type: string
                            subjectAltNames:
                              description: A list of alternate names to verify the
                                subject identity in the certificate.
                              items:
                                type: string
                              type: array
                          type: object
                        tunnel:
                          description: Configuration of tunneling TCP over other transport
                            or application layers for the host configured in the DestinationRule.
                          properties:
                            protocol:
                              description: Specifies which protocol to use for tunneling
                                the downstream connection.
                              type: string
                            targetHost:
                              description: Specifies a host to which the downstream
                                connection is tunneled.
                              type: string
                            targetPort:
                              description: Specifies a port to which the downstream
                                connection is tunneled.
                              maximum: **********
                              minimum: 0
                              type: integer
                          required:
                          - targetHost
                          - targetPort
                          type: object
                      type: object
                  required:
                  - name
                  type: object
                type: array
              trafficPolicy:
                description: Traffic policies to apply (load balancing policy, connection
                  pool sizes, outlier detection).
                properties:
                  connectionPool:
                    properties:
                      http:
                        description: HTTP connection pool settings.
                        properties:
                          h2UpgradePolicy:
                            description: |-
                              Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                              Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                            enum:
                            - DEFAULT
                            - DO_NOT_UPGRADE
                            - UPGRADE
                            type: string
                          http1MaxPendingRequests:
                            description: Maximum number of requests that will be queued
                              while waiting for a ready connection pool connection.
                            format: int32
                            type: integer
                          http2MaxRequests:
                            description: Maximum number of active requests to a destination.
                            format: int32
                            type: integer
                          idleTimeout:
                            description: The idle timeout for upstream connection
                              pool connections.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          maxConcurrentStreams:
                            description: The maximum number of concurrent streams
                              allowed for a peer on one HTTP/2 connection.
                            format: int32
                            type: integer
                          maxRequestsPerConnection:
                            description: Maximum number of requests per connection
                              to a backend.
                            format: int32
                            type: integer
                          maxRetries:
                            description: Maximum number of retries that can be outstanding
                              to all hosts in a cluster at a given time.
                            format: int32
                            type: integer
                          useClientProtocol:
                            description: If set to true, client protocol will be preserved
                              while initiating connection to backend.
                            type: boolean
                        type: object
                      tcp:
                        description: Settings common to both HTTP and TCP upstream
                          connections.
                        properties:
                          connectTimeout:
                            description: TCP connection timeout.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          idleTimeout:
                            description: The idle timeout for TCP connections.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          maxConnectionDuration:
                            description: The maximum duration of a connection.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          maxConnections:
                            description: Maximum number of HTTP1 /TCP connections
                              to a destination host.
                            format: int32
                            type: integer
                          tcpKeepalive:
                            description: If set then set SO_KEEPALIVE on the socket
                              to enable TCP Keepalives.
                            properties:
                              interval:
                                description: The time duration between keep-alive
                                  probes.
                                type: string
                                x-kubernetes-validations:
                                - message: must be a valid duration greater than 1ms
                                  rule: duration(self) >= duration('1ms')
                              probes:
                                description: Maximum number of keepalive probes to
                                  send without response before deciding the connection
                                  is dead.
                                maximum: **********
                                minimum: 0
                                type: integer
                              time:
                                description: The time duration a connection needs
                                  to be idle before keep-alive probes start being
                                  sent.
                                type: string
                                x-kubernetes-validations:
                                - message: must be a valid duration greater than 1ms
                                  rule: duration(self) >= duration('1ms')
                            type: object
                        type: object
                    type: object
                  loadBalancer:
                    description: Settings controlling the load balancer algorithms.
                    oneOf:
                    - not:
                        anyOf:
                        - required:
                          - simple
                        - required:
                          - consistentHash
                    - required:
                      - simple
                    - required:
                      - consistentHash
                    properties:
                      consistentHash:
                        allOf:
                        - oneOf:
                          - not:
                              anyOf:
                              - required:
                                - httpHeaderName
                              - required:
                                - httpCookie
                              - required:
                                - useSourceIp
                              - required:
                                - httpQueryParameterName
                          - required:
                            - httpHeaderName
                          - required:
                            - httpCookie
                          - required:
                            - useSourceIp
                          - required:
                            - httpQueryParameterName
                        - oneOf:
                          - not:
                              anyOf:
                              - required:
                                - ringHash
                              - required:
                                - maglev
                          - required:
                            - ringHash
                          - required:
                            - maglev
                        properties:
                          httpCookie:
                            description: Hash based on HTTP cookie.
                            properties:
                              name:
                                description: Name of the cookie.
                                type: string
                              path:
                                description: Path to set for the cookie.
                                type: string
                              ttl:
                                description: Lifetime of the cookie.
                                type: string
                            required:
                            - name
                            type: object
                          httpHeaderName:
                            description: Hash based on a specific HTTP header.
                            type: string
                          httpQueryParameterName:
                            description: Hash based on a specific HTTP query parameter.
                            type: string
                          maglev:
                            description: The Maglev load balancer implements consistent
                              hashing to backend hosts.
                            properties:
                              tableSize:
                                description: The table size for Maglev hashing.
                                minimum: 0
                                type: integer
                            type: object
                          minimumRingSize:
                            description: Deprecated.
                            minimum: 0
                            type: integer
                          ringHash:
                            description: The ring/modulo hash load balancer implements
                              consistent hashing to backend hosts.
                            properties:
                              minimumRingSize:
                                description: The minimum number of virtual nodes to
                                  use for the hash ring.
                                minimum: 0
                                type: integer
                            type: object
                          useSourceIp:
                            description: Hash based on the source IP address.
                            type: boolean
                        type: object
                      localityLbSetting:
                        properties:
                          distribute:
                            description: 'Optional: only one of distribute, failover
                              or failoverPriority can be set.'
                            items:
                              properties:
                                from:
                                  description: Originating locality, '/' separated,
                                    e.g.
                                  type: string
                                to:
                                  additionalProperties:
                                    maximum: **********
                                    minimum: 0
                                    type: integer
                                  description: Map of upstream localities to traffic
                                    distribution weights.
                                  type: object
                              type: object
                            type: array
                          enabled:
                            description: Enable locality load balancing.
                            nullable: true
                            type: boolean
                          failover:
                            description: 'Optional: only one of distribute, failover
                              or failoverPriority can be set.'
                            items:
                              properties:
                                from:
                                  description: Originating region.
                                  type: string
                                to:
                                  description: Destination region the traffic will
                                    fail over to when endpoints in the 'from' region
                                    becomes unhealthy.
                                  type: string
                              type: object
                            type: array
                          failoverPriority:
                            description: failoverPriority is an ordered list of labels
                              used to sort endpoints to do priority based load balancing.
                            items:
                              type: string
                            type: array
                        type: object
                      simple:
                        description: |2-


                          Valid Options: LEAST_CONN, RANDOM, PASSTHROUGH, ROUND_ROBIN, LEAST_REQUEST
                        enum:
                        - UNSPECIFIED
                        - LEAST_CONN
                        - RANDOM
                        - PASSTHROUGH
                        - ROUND_ROBIN
                        - LEAST_REQUEST
                        type: string
                      warmup:
                        description: Represents the warmup configuration of Service.
                        properties:
                          aggression:
                            description: This parameter controls the speed of traffic
                              increase over the warmup duration.
                            format: double
                            minimum: 1
                            nullable: true
                            type: number
                          duration:
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          minimumPercent:
                            format: double
                            maximum: 100
                            minimum: 0
                            nullable: true
                            type: number
                        required:
                        - duration
                        type: object
                      warmupDurationSecs:
                        description: 'Deprecated: use `warmup` instead.'
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                    type: object
                  outlierDetection:
                    properties:
                      baseEjectionTime:
                        description: Minimum ejection duration.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      consecutive5xxErrors:
                        description: Number of 5xx errors before a host is ejected
                          from the connection pool.
                        maximum: **********
                        minimum: 0
                        nullable: true
                        type: integer
                      consecutiveErrors:
                        format: int32
                        type: integer
                      consecutiveGatewayErrors:
                        description: Number of gateway errors before a host is ejected
                          from the connection pool.
                        maximum: **********
                        minimum: 0
                        nullable: true
                        type: integer
                      consecutiveLocalOriginFailures:
                        description: The number of consecutive locally originated
                          failures before ejection occurs.
                        maximum: **********
                        minimum: 0
                        nullable: true
                        type: integer
                      interval:
                        description: Time interval between ejection sweep analysis.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      maxEjectionPercent:
                        description: Maximum % of hosts in the load balancing pool
                          for the upstream service that can be ejected.
                        format: int32
                        type: integer
                      minHealthPercent:
                        description: Outlier detection will be enabled as long as
                          the associated load balancing pool has at least `minHealthPercent`
                          hosts in healthy mode.
                        format: int32
                        type: integer
                      splitExternalLocalOriginErrors:
                        description: Determines whether to distinguish local origin
                          failures from external errors.
                        type: boolean
                    type: object
                  portLevelSettings:
                    description: Traffic policies specific to individual ports.
                    items:
                      properties:
                        connectionPool:
                          properties:
                            http:
                              description: HTTP connection pool settings.
                              properties:
                                h2UpgradePolicy:
                                  description: |-
                                    Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                                    Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                                  enum:
                                  - DEFAULT
                                  - DO_NOT_UPGRADE
                                  - UPGRADE
                                  type: string
                                http1MaxPendingRequests:
                                  description: Maximum number of requests that will
                                    be queued while waiting for a ready connection
                                    pool connection.
                                  format: int32
                                  type: integer
                                http2MaxRequests:
                                  description: Maximum number of active requests to
                                    a destination.
                                  format: int32
                                  type: integer
                                idleTimeout:
                                  description: The idle timeout for upstream connection
                                    pool connections.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConcurrentStreams:
                                  description: The maximum number of concurrent streams
                                    allowed for a peer on one HTTP/2 connection.
                                  format: int32
                                  type: integer
                                maxRequestsPerConnection:
                                  description: Maximum number of requests per connection
                                    to a backend.
                                  format: int32
                                  type: integer
                                maxRetries:
                                  description: Maximum number of retries that can
                                    be outstanding to all hosts in a cluster at a
                                    given time.
                                  format: int32
                                  type: integer
                                useClientProtocol:
                                  description: If set to true, client protocol will
                                    be preserved while initiating connection to backend.
                                  type: boolean
                              type: object
                            tcp:
                              description: Settings common to both HTTP and TCP upstream
                                connections.
                              properties:
                                connectTimeout:
                                  description: TCP connection timeout.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                idleTimeout:
                                  description: The idle timeout for TCP connections.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConnectionDuration:
                                  description: The maximum duration of a connection.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                maxConnections:
                                  description: Maximum number of HTTP1 /TCP connections
                                    to a destination host.
                                  format: int32
                                  type: integer
                                tcpKeepalive:
                                  description: If set then set SO_KEEPALIVE on the
                                    socket to enable TCP Keepalives.
                                  properties:
                                    interval:
                                      description: The time duration between keep-alive
                                        probes.
                                      type: string
                                      x-kubernetes-validations:
                                      - message: must be a valid duration greater
                                          than 1ms
                                        rule: duration(self) >= duration('1ms')
                                    probes:
                                      description: Maximum number of keepalive probes
                                        to send without response before deciding the
                                        connection is dead.
                                      maximum: **********
                                      minimum: 0
                                      type: integer
                                    time:
                                      description: The time duration a connection
                                        needs to be idle before keep-alive probes
                                        start being sent.
                                      type: string
                                      x-kubernetes-validations:
                                      - message: must be a valid duration greater
                                          than 1ms
                                        rule: duration(self) >= duration('1ms')
                                  type: object
                              type: object
                          type: object
                        loadBalancer:
                          description: Settings controlling the load balancer algorithms.
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - simple
                              - required:
                                - consistentHash
                          - required:
                            - simple
                          - required:
                            - consistentHash
                          properties:
                            consistentHash:
                              allOf:
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - httpHeaderName
                                    - required:
                                      - httpCookie
                                    - required:
                                      - useSourceIp
                                    - required:
                                      - httpQueryParameterName
                                - required:
                                  - httpHeaderName
                                - required:
                                  - httpCookie
                                - required:
                                  - useSourceIp
                                - required:
                                  - httpQueryParameterName
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - ringHash
                                    - required:
                                      - maglev
                                - required:
                                  - ringHash
                                - required:
                                  - maglev
                              properties:
                                httpCookie:
                                  description: Hash based on HTTP cookie.
                                  properties:
                                    name:
                                      description: Name of the cookie.
                                      type: string
                                    path:
                                      description: Path to set for the cookie.
                                      type: string
                                    ttl:
                                      description: Lifetime of the cookie.
                                      type: string
                                  required:
                                  - name
                                  type: object
                                httpHeaderName:
                                  description: Hash based on a specific HTTP header.
                                  type: string
                                httpQueryParameterName:
                                  description: Hash based on a specific HTTP query
                                    parameter.
                                  type: string
                                maglev:
                                  description: The Maglev load balancer implements
                                    consistent hashing to backend hosts.
                                  properties:
                                    tableSize:
                                      description: The table size for Maglev hashing.
                                      minimum: 0
                                      type: integer
                                  type: object
                                minimumRingSize:
                                  description: Deprecated.
                                  minimum: 0
                                  type: integer
                                ringHash:
                                  description: The ring/modulo hash load balancer
                                    implements consistent hashing to backend hosts.
                                  properties:
                                    minimumRingSize:
                                      description: The minimum number of virtual nodes
                                        to use for the hash ring.
                                      minimum: 0
                                      type: integer
                                  type: object
                                useSourceIp:
                                  description: Hash based on the source IP address.
                                  type: boolean
                              type: object
                            localityLbSetting:
                              properties:
                                distribute:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating locality, '/' separated,
                                          e.g.
                                        type: string
                                      to:
                                        additionalProperties:
                                          maximum: **********
                                          minimum: 0
                                          type: integer
                                        description: Map of upstream localities to
                                          traffic distribution weights.
                                        type: object
                                    type: object
                                  type: array
                                enabled:
                                  description: Enable locality load balancing.
                                  nullable: true
                                  type: boolean
                                failover:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating region.
                                        type: string
                                      to:
                                        description: Destination region the traffic
                                          will fail over to when endpoints in the
                                          'from' region becomes unhealthy.
                                        type: string
                                    type: object
                                  type: array
                                failoverPriority:
                                  description: failoverPriority is an ordered list
                                    of labels used to sort endpoints to do priority
                                    based load balancing.
                                  items:
                                    type: string
                                  type: array
                              type: object
                            simple:
                              description: |2-


                                Valid Options: LEAST_CONN, RANDOM, PASSTHROUGH, ROUND_ROBIN, LEAST_REQUEST
                              enum:
                              - UNSPECIFIED
                              - LEAST_CONN
                              - RANDOM
                              - PASSTHROUGH
                              - ROUND_ROBIN
                              - LEAST_REQUEST
                              type: string
                            warmup:
                              description: Represents the warmup configuration of
                                Service.
                              properties:
                                aggression:
                                  description: This parameter controls the speed of
                                    traffic increase over the warmup duration.
                                  format: double
                                  minimum: 1
                                  nullable: true
                                  type: number
                                duration:
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                minimumPercent:
                                  format: double
                                  maximum: 100
                                  minimum: 0
                                  nullable: true
                                  type: number
                              required:
                              - duration
                              type: object
                            warmupDurationSecs:
                              description: 'Deprecated: use `warmup` instead.'
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                          type: object
                        outlierDetection:
                          properties:
                            baseEjectionTime:
                              description: Minimum ejection duration.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            consecutive5xxErrors:
                              description: Number of 5xx errors before a host is ejected
                                from the connection pool.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            consecutiveErrors:
                              format: int32
                              type: integer
                            consecutiveGatewayErrors:
                              description: Number of gateway errors before a host
                                is ejected from the connection pool.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            consecutiveLocalOriginFailures:
                              description: The number of consecutive locally originated
                                failures before ejection occurs.
                              maximum: **********
                              minimum: 0
                              nullable: true
                              type: integer
                            interval:
                              description: Time interval between ejection sweep analysis.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            maxEjectionPercent:
                              description: Maximum % of hosts in the load balancing
                                pool for the upstream service that can be ejected.
                              format: int32
                              type: integer
                            minHealthPercent:
                              description: Outlier detection will be enabled as long
                                as the associated load balancing pool has at least
                                `minHealthPercent` hosts in healthy mode.
                              format: int32
                              type: integer
                            splitExternalLocalOriginErrors:
                              description: Determines whether to distinguish local
                                origin failures from external errors.
                              type: boolean
                          type: object
                        port:
                          description: Specifies the number of a port on the destination
                            service on which this policy is being applied.
                          properties:
                            number:
                              maximum: **********
                              minimum: 0
                              type: integer
                          type: object
                        tls:
                          description: TLS related settings for connections to the
                            upstream service.
                          properties:
                            caCertificates:
                              description: 'OPTIONAL: The path to the file containing
                                certificate authority certificates to use in verifying
                                a presented server certificate.'
                              type: string
                            caCrl:
                              description: 'OPTIONAL: The path to the file containing
                                the certificate revocation list (CRL) to use in verifying
                                a presented server certificate.'
                              type: string
                            clientCertificate:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            credentialName:
                              description: The name of the secret that holds the TLS
                                certs for the client including the CA certificates.
                              type: string
                            insecureSkipVerify:
                              description: '`insecureSkipVerify` specifies whether
                                the proxy should skip verifying the CA signature and
                                SAN for the server certificate corresponding to the
                                host.'
                              nullable: true
                              type: boolean
                            mode:
                              description: |-
                                Indicates whether connections to this port should be secured using TLS.

                                Valid Options: DISABLE, SIMPLE, MUTUAL, ISTIO_MUTUAL
                              enum:
                              - DISABLE
                              - SIMPLE
                              - MUTUAL
                              - ISTIO_MUTUAL
                              type: string
                            privateKey:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            sni:
                              description: SNI string to present to the server during
                                TLS handshake.
                              type: string
                            subjectAltNames:
                              description: A list of alternate names to verify the
                                subject identity in the certificate.
                              items:
                                type: string
                              type: array
                          type: object
                      type: object
                    maxItems: 4096
                    type: array
                  proxyProtocol:
                    description: The upstream PROXY protocol settings.
                    properties:
                      version:
                        description: |-
                          The PROXY protocol version to use.

                          Valid Options: V1, V2
                        enum:
                        - V1
                        - V2
                        type: string
                    type: object
                  tls:
                    description: TLS related settings for connections to the upstream
                      service.
                    properties:
                      caCertificates:
                        description: 'OPTIONAL: The path to the file containing certificate
                          authority certificates to use in verifying a presented server
                          certificate.'
                        type: string
                      caCrl:
                        description: 'OPTIONAL: The path to the file containing the
                          certificate revocation list (CRL) to use in verifying a
                          presented server certificate.'
                        type: string
                      clientCertificate:
                        description: REQUIRED if mode is `MUTUAL`.
                        type: string
                      credentialName:
                        description: The name of the secret that holds the TLS certs
                          for the client including the CA certificates.
                        type: string
                      insecureSkipVerify:
                        description: '`insecureSkipVerify` specifies whether the proxy
                          should skip verifying the CA signature and SAN for the server
                          certificate corresponding to the host.'
                        nullable: true
                        type: boolean
                      mode:
                        description: |-
                          Indicates whether connections to this port should be secured using TLS.

                          Valid Options: DISABLE, SIMPLE, MUTUAL, ISTIO_MUTUAL
                        enum:
                        - DISABLE
                        - SIMPLE
                        - MUTUAL
                        - ISTIO_MUTUAL
                        type: string
                      privateKey:
                        description: REQUIRED if mode is `MUTUAL`.
                        type: string
                      sni:
                        description: SNI string to present to the server during TLS
                          handshake.
                        type: string
                      subjectAltNames:
                        description: A list of alternate names to verify the subject
                          identity in the certificate.
                        items:
                          type: string
                        type: array
                    type: object
                  tunnel:
                    description: Configuration of tunneling TCP over other transport
                      or application layers for the host configured in the DestinationRule.
                    properties:
                      protocol:
                        description: Specifies which protocol to use for tunneling
                          the downstream connection.
                        type: string
                      targetHost:
                        description: Specifies a host to which the downstream connection
                          is tunneled.
                        type: string
                      targetPort:
                        description: Specifies a port to which the downstream connection
                          is tunneled.
                        maximum: **********
                        minimum: 0
                        type: integer
                    required:
                    - targetHost
                    - targetPort
                    type: object
                type: object
              workloadSelector:
                description: Criteria used to select the specific set of pods/VMs
                  on which this `DestinationRule` configuration should be applied.
                properties:
                  matchLabels:
                    additionalProperties:
                      maxLength: 63
                      type: string
                      x-kubernetes-validations:
                      - message: wildcard not allowed in label value match
                        rule: '!self.contains(''*'')'
                    description: One or more labels that indicate a specific set of
                      pods/VMs on which a policy should be applied.
                    maxProperties: 4096
                    type: object
                    x-kubernetes-validations:
                    - message: wildcard not allowed in label key match
                      rule: self.all(key, !key.contains('*'))
                    - message: key must not be empty
                      rule: self.all(key, key.size() != 0)
                type: object
            required:
            - host
            type: object
          status:
            properties:
              conditions:
                description: Current service state of the resource.
                items:
                  properties:
                    lastProbeTime:
                      description: Last time we probed the condition.
                      format: date-time
                      type: string
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      format: date-time
                      type: string
                    message:
                      description: Human-readable message indicating details about
                        last transition.
                      type: string
                    reason:
                      description: Unique, one-word, CamelCase reason for the condition's
                        last transition.
                      type: string
                    status:
                      description: Status is the status of the condition.
                      type: string
                    type:
                      description: Type is the type of the condition.
                      type: string
                  type: object
                type: array
              observedGeneration:
                anyOf:
                - type: integer
                - type: string
                description: Resource Generation to which the Reconciled Condition
                  refers.
                x-kubernetes-int-or-string: true
              validationMessages:
                description: Includes any errors or warnings detected by Istio's analyzers.
                items:
                  properties:
                    documentationUrl:
                      description: A url pointing to the Istio documentation for this
                        specific error type.
                      type: string
                    level:
                      description: |-
                        Represents how severe a message is.

                        Valid Options: UNKNOWN, ERROR, WARNING, INFO
                      enum:
                      - UNKNOWN
                      - ERROR
                      - WARNING
                      - INFO
                      type: string
                    type:
                      properties:
                        code:
                          description: A 7 character code matching `^IST[0-9]{4}$`
                            intended to uniquely identify the message type.
                          type: string
                        name:
                          description: A human-readable name for the message type.
                          type: string
                      type: object
                  type: object
                type: array
            type: object
            x-kubernetes-preserve-unknown-fields: true
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    categories:
    - istio-io
    - networking-istio-io
    kind: DestinationRule
    listKind: DestinationRuleList
    plural: destinationrules
    shortNames:
    - dr
    singular: destinationrule
  conditions:
  - lastTransitionTime: "2025-03-10T19:25:40Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-03-10T19:25:40Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1beta1
