# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: v1
kind: Service
metadata:
  name: test-tenant-app
  namespace: test-tenant-app
  labels:
    app: test-tenant-app
    service: test-tenant-app
spec:
  ports:
    - name: port8080
      port: 8080
      targetPort: 8080
    - name: port8081
      port: 8081
      targetPort: 8081
  selector:
    app: test-tenant-app
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: http-echo-multi-port
  namespace: test-tenant-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: test-tenant-app
  template:
    metadata:
      labels:
        app: test-tenant-app
    spec:
      containers:
        - name: http-echo-port-8080
          image: hashicorp/http-echo
          imagePullPolicy: IfNotPresent
          args:
            - "-text=Hello from port 8080"
            - "-status-code=200"
            - "-listen=:8080"
          resources:
            limits:
              cpu: 50m
              memory: 64Mi
            requests:
              cpu: 50m
              memory: 64Mi
          ports:
            - containerPort: 8080
        - name: http-echo-port-8081
          image: hashicorp/http-echo
          imagePullPolicy: IfNotPresent
          args:
            - "-text=Hello from port 8081"
            - "-status-code=200"
            - "-listen=:8081"
          resources:
            limits:
              cpu: 50m
              memory: 64Mi
            requests:
              cpu: 50m
              memory: 64Mi
          ports:
            - containerPort: 8081
