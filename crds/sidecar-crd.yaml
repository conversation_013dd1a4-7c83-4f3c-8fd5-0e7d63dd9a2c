apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    helm.sh/resource-policy: keep
    meta.helm.sh/release-name: base
    meta.helm.sh/release-namespace: istio-system
  creationTimestamp: "2025-03-10T19:25:39Z"
  generation: 1
  labels:
    app.kubernetes.io/instance: base
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: istio
    app.kubernetes.io/version: 1.24.3
    helm.sh/chart: base-1.24.3
  name: sidecars.networking.istio.io
  resourceVersion: "1204"
  uid: 0042d840-4171-4f4c-b5bd-a371faab2899
spec:
  conversion:
    strategy: None
  group: networking.istio.io
  names:
    categories:
    - istio-io
    - networking-istio-io
    kind: Sidecar
    listKind: SidecarList
    plural: sidecars
    singular: sidecar
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        properties:
          spec:
            description: 'Configuration affecting network reachability of a sidecar.
              See more details at: https://istio.io/docs/reference/config/networking/sidecar.html'
            properties:
              egress:
                description: Egress specifies the configuration of the sidecar for
                  processing outbound traffic from the attached workload instance
                  to other services in the mesh.
                items:
                  properties:
                    bind:
                      description: The IP(IPv4 or IPv6) or the Unix domain socket
                        to which the listener should be bound to.
                      type: string
                    captureMode:
                      description: |-
                        When the bind address is an IP, the captureMode option dictates how traffic to the listener is expected to be captured (or not).

                        Valid Options: DEFAULT, IPTABLES, NONE
                      enum:
                      - DEFAULT
                      - IPTABLES
                      - NONE
                      type: string
                    hosts:
                      description: One or more service hosts exposed by the listener
                        in `namespace/dnsName` format.
                      items:
                        type: string
                      type: array
                    port:
                      description: The port associated with the listener.
                      properties:
                        name:
                          description: Label assigned to the port.
                          type: string
                        number:
                          description: A valid non-negative integer port number.
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                        protocol:
                          description: The protocol exposed on the port.
                          type: string
                        targetPort:
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                      type: object
                  required:
                  - hosts
                  type: object
                type: array
              inboundConnectionPool:
                description: Settings controlling the volume of connections Envoy
                  will accept from the network.
                properties:
                  http:
                    description: HTTP connection pool settings.
                    properties:
                      h2UpgradePolicy:
                        description: |-
                          Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                          Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                        enum:
                        - DEFAULT
                        - DO_NOT_UPGRADE
                        - UPGRADE
                        type: string
                      http1MaxPendingRequests:
                        description: Maximum number of requests that will be queued
                          while waiting for a ready connection pool connection.
                        format: int32
                        type: integer
                      http2MaxRequests:
                        description: Maximum number of active requests to a destination.
                        format: int32
                        type: integer
                      idleTimeout:
                        description: The idle timeout for upstream connection pool
                          connections.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      maxConcurrentStreams:
                        description: The maximum number of concurrent streams allowed
                          for a peer on one HTTP/2 connection.
                        format: int32
                        type: integer
                      maxRequestsPerConnection:
                        description: Maximum number of requests per connection to
                          a backend.
                        format: int32
                        type: integer
                      maxRetries:
                        description: Maximum number of retries that can be outstanding
                          to all hosts in a cluster at a given time.
                        format: int32
                        type: integer
                      useClientProtocol:
                        description: If set to true, client protocol will be preserved
                          while initiating connection to backend.
                        type: boolean
                    type: object
                  tcp:
                    description: Settings common to both HTTP and TCP upstream connections.
                    properties:
                      connectTimeout:
                        description: TCP connection timeout.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      idleTimeout:
                        description: The idle timeout for TCP connections.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      maxConnectionDuration:
                        description: The maximum duration of a connection.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      maxConnections:
                        description: Maximum number of HTTP1 /TCP connections to a
                          destination host.
                        format: int32
                        type: integer
                      tcpKeepalive:
                        description: If set then set SO_KEEPALIVE on the socket to
                          enable TCP Keepalives.
                        properties:
                          interval:
                            description: The time duration between keep-alive probes.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          probes:
                            description: Maximum number of keepalive probes to send
                              without response before deciding the connection is dead.
                            maximum: 4294967295
                            minimum: 0
                            type: integer
                          time:
                            description: The time duration a connection needs to be
                              idle before keep-alive probes start being sent.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                        type: object
                    type: object
                type: object
              ingress:
                description: Ingress specifies the configuration of the sidecar for
                  processing inbound traffic to the attached workload instance.
                items:
                  properties:
                    bind:
                      description: The IP(IPv4 or IPv6) to which the listener should
                        be bound.
                      type: string
                    captureMode:
                      description: |-
                        The captureMode option dictates how traffic to the listener is expected to be captured (or not).

                        Valid Options: DEFAULT, IPTABLES, NONE
                      enum:
                      - DEFAULT
                      - IPTABLES
                      - NONE
                      type: string
                    connectionPool:
                      description: Settings controlling the volume of connections
                        Envoy will accept from the network.
                      properties:
                        http:
                          description: HTTP connection pool settings.
                          properties:
                            h2UpgradePolicy:
                              description: |-
                                Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                                Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                              enum:
                              - DEFAULT
                              - DO_NOT_UPGRADE
                              - UPGRADE
                              type: string
                            http1MaxPendingRequests:
                              description: Maximum number of requests that will be
                                queued while waiting for a ready connection pool connection.
                              format: int32
                              type: integer
                            http2MaxRequests:
                              description: Maximum number of active requests to a
                                destination.
                              format: int32
                              type: integer
                            idleTimeout:
                              description: The idle timeout for upstream connection
                                pool connections.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            maxConcurrentStreams:
                              description: The maximum number of concurrent streams
                                allowed for a peer on one HTTP/2 connection.
                              format: int32
                              type: integer
                            maxRequestsPerConnection:
                              description: Maximum number of requests per connection
                                to a backend.
                              format: int32
                              type: integer
                            maxRetries:
                              description: Maximum number of retries that can be outstanding
                                to all hosts in a cluster at a given time.
                              format: int32
                              type: integer
                            useClientProtocol:
                              description: If set to true, client protocol will be
                                preserved while initiating connection to backend.
                              type: boolean
                          type: object
                        tcp:
                          description: Settings common to both HTTP and TCP upstream
                            connections.
                          properties:
                            connectTimeout:
                              description: TCP connection timeout.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            idleTimeout:
                              description: The idle timeout for TCP connections.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            maxConnectionDuration:
                              description: The maximum duration of a connection.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            maxConnections:
                              description: Maximum number of HTTP1 /TCP connections
                                to a destination host.
                              format: int32
                              type: integer
                            tcpKeepalive:
                              description: If set then set SO_KEEPALIVE on the socket
                                to enable TCP Keepalives.
                              properties:
                                interval:
                                  description: The time duration between keep-alive
                                    probes.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                probes:
                                  description: Maximum number of keepalive probes
                                    to send without response before deciding the connection
                                    is dead.
                                  maximum: 4294967295
                                  minimum: 0
                                  type: integer
                                time:
                                  description: The time duration a connection needs
                                    to be idle before keep-alive probes start being
                                    sent.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                              type: object
                          type: object
                      type: object
                    defaultEndpoint:
                      description: The IP endpoint or Unix domain socket to which
                        traffic should be forwarded to.
                      type: string
                    port:
                      description: The port associated with the listener.
                      properties:
                        name:
                          description: Label assigned to the port.
                          type: string
                        number:
                          description: A valid non-negative integer port number.
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                        protocol:
                          description: The protocol exposed on the port.
                          type: string
                        targetPort:
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                      type: object
                    tls:
                      description: Set of TLS related options that will enable TLS
                        termination on the sidecar for requests originating from outside
                        the mesh.
                      properties:
                        caCertificates:
                          description: REQUIRED if mode is `MUTUAL` or `OPTIONAL_MUTUAL`.
                          type: string
                        caCrl:
                          description: 'OPTIONAL: The path to the file containing
                            the certificate revocation list (CRL) to use in verifying
                            a presented client side certificate.'
                          type: string
                        cipherSuites:
                          description: 'Optional: If specified, only support the specified
                            cipher list.'
                          items:
                            type: string
                          type: array
                        credentialName:
                          description: For gateways running on Kubernetes, the name
                            of the secret that holds the TLS certs including the CA
                            certificates.
                          type: string
                        httpsRedirect:
                          description: If set to true, the load balancer will send
                            a 301 redirect for all http connections, asking the clients
                            to use HTTPS.
                          type: boolean
                        maxProtocolVersion:
                          description: |-
                            Optional: Maximum TLS protocol version.

                            Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
                          enum:
                          - TLS_AUTO
                          - TLSV1_0
                          - TLSV1_1
                          - TLSV1_2
                          - TLSV1_3
                          type: string
                        minProtocolVersion:
                          description: |-
                            Optional: Minimum TLS protocol version.

                            Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
                          enum:
                          - TLS_AUTO
                          - TLSV1_0
                          - TLSV1_1
                          - TLSV1_2
                          - TLSV1_3
                          type: string
                        mode:
                          description: |-
                            Optional: Indicates whether connections to this port should be secured using TLS.

                            Valid Options: PASSTHROUGH, SIMPLE, MUTUAL, AUTO_PASSTHROUGH, ISTIO_MUTUAL, OPTIONAL_MUTUAL
                          enum:
                          - PASSTHROUGH
                          - SIMPLE
                          - MUTUAL
                          - AUTO_PASSTHROUGH
                          - ISTIO_MUTUAL
                          - OPTIONAL_MUTUAL
                          type: string
                        privateKey:
                          description: REQUIRED if mode is `SIMPLE` or `MUTUAL`.
                          type: string
                        serverCertificate:
                          description: REQUIRED if mode is `SIMPLE` or `MUTUAL`.
                          type: string
                        subjectAltNames:
                          description: A list of alternate names to verify the subject
                            identity in the certificate presented by the client.
                          items:
                            type: string
                          type: array
                        verifyCertificateHash:
                          description: An optional list of hex-encoded SHA-256 hashes
                            of the authorized client certificates.
                          items:
                            type: string
                          type: array
                        verifyCertificateSpki:
                          description: An optional list of base64-encoded SHA-256
                            hashes of the SPKIs of authorized client certificates.
                          items:
                            type: string
                          type: array
                      type: object
                  required:
                  - port
                  type: object
                type: array
              outboundTrafficPolicy:
                description: Set the default behavior of the sidecar for handling
                  outbound traffic from the application.
                properties:
                  egressProxy:
                    properties:
                      host:
                        description: The name of a service from the service registry.
                        type: string
                      port:
                        description: Specifies the port on the host that is being
                          addressed.
                        properties:
                          number:
                            maximum: 4294967295
                            minimum: 0
                            type: integer
                        type: object
                      subset:
                        description: The name of a subset within the service.
                        type: string
                    required:
                    - host
                    type: object
                  mode:
                    description: |2-


                      Valid Options: REGISTRY_ONLY, ALLOW_ANY
                    enum:
                    - REGISTRY_ONLY
                    - ALLOW_ANY
                    type: string
                type: object
              workloadSelector:
                description: Criteria used to select the specific set of pods/VMs
                  on which this `Sidecar` configuration should be applied.
                properties:
                  labels:
                    additionalProperties:
                      maxLength: 63
                      type: string
                      x-kubernetes-validations:
                      - message: wildcard is not supported in selector
                        rule: '!self.contains(''*'')'
                    description: One or more labels that indicate a specific set of
                      pods/VMs on which the configuration should be applied.
                    maxProperties: 256
                    type: object
                type: object
            type: object
          status:
            properties:
              conditions:
                description: Current service state of the resource.
                items:
                  properties:
                    lastProbeTime:
                      description: Last time we probed the condition.
                      format: date-time
                      type: string
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      format: date-time
                      type: string
                    message:
                      description: Human-readable message indicating details about
                        last transition.
                      type: string
                    reason:
                      description: Unique, one-word, CamelCase reason for the condition's
                        last transition.
                      type: string
                    status:
                      description: Status is the status of the condition.
                      type: string
                    type:
                      description: Type is the type of the condition.
                      type: string
                  type: object
                type: array
              observedGeneration:
                anyOf:
                - type: integer
                - type: string
                description: Resource Generation to which the Reconciled Condition
                  refers.
                x-kubernetes-int-or-string: true
              validationMessages:
                description: Includes any errors or warnings detected by Istio's analyzers.
                items:
                  properties:
                    documentationUrl:
                      description: A url pointing to the Istio documentation for this
                        specific error type.
                      type: string
                    level:
                      description: |-
                        Represents how severe a message is.

                        Valid Options: UNKNOWN, ERROR, WARNING, INFO
                      enum:
                      - UNKNOWN
                      - ERROR
                      - WARNING
                      - INFO
                      type: string
                    type:
                      properties:
                        code:
                          description: A 7 character code matching `^IST[0-9]{4}$`
                            intended to uniquely identify the message type.
                          type: string
                        name:
                          description: A human-readable name for the message type.
                          type: string
                      type: object
                  type: object
                type: array
            type: object
            x-kubernetes-preserve-unknown-fields: true
        type: object
    served: true
    storage: false
    subresources:
      status: {}
  - name: v1alpha3
    schema:
      openAPIV3Schema:
        properties:
          spec:
            description: 'Configuration affecting network reachability of a sidecar.
              See more details at: https://istio.io/docs/reference/config/networking/sidecar.html'
            properties:
              egress:
                description: Egress specifies the configuration of the sidecar for
                  processing outbound traffic from the attached workload instance
                  to other services in the mesh.
                items:
                  properties:
                    bind:
                      description: The IP(IPv4 or IPv6) or the Unix domain socket
                        to which the listener should be bound to.
                      type: string
                    captureMode:
                      description: |-
                        When the bind address is an IP, the captureMode option dictates how traffic to the listener is expected to be captured (or not).

                        Valid Options: DEFAULT, IPTABLES, NONE
                      enum:
                      - DEFAULT
                      - IPTABLES
                      - NONE
                      type: string
                    hosts:
                      description: One or more service hosts exposed by the listener
                        in `namespace/dnsName` format.
                      items:
                        type: string
                      type: array
                    port:
                      description: The port associated with the listener.
                      properties:
                        name:
                          description: Label assigned to the port.
                          type: string
                        number:
                          description: A valid non-negative integer port number.
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                        protocol:
                          description: The protocol exposed on the port.
                          type: string
                        targetPort:
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                      type: object
                  required:
                  - hosts
                  type: object
                type: array
              inboundConnectionPool:
                description: Settings controlling the volume of connections Envoy
                  will accept from the network.
                properties:
                  http:
                    description: HTTP connection pool settings.
                    properties:
                      h2UpgradePolicy:
                        description: |-
                          Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                          Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                        enum:
                        - DEFAULT
                        - DO_NOT_UPGRADE
                        - UPGRADE
                        type: string
                      http1MaxPendingRequests:
                        description: Maximum number of requests that will be queued
                          while waiting for a ready connection pool connection.
                        format: int32
                        type: integer
                      http2MaxRequests:
                        description: Maximum number of active requests to a destination.
                        format: int32
                        type: integer
                      idleTimeout:
                        description: The idle timeout for upstream connection pool
                          connections.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      maxConcurrentStreams:
                        description: The maximum number of concurrent streams allowed
                          for a peer on one HTTP/2 connection.
                        format: int32
                        type: integer
                      maxRequestsPerConnection:
                        description: Maximum number of requests per connection to
                          a backend.
                        format: int32
                        type: integer
                      maxRetries:
                        description: Maximum number of retries that can be outstanding
                          to all hosts in a cluster at a given time.
                        format: int32
                        type: integer
                      useClientProtocol:
                        description: If set to true, client protocol will be preserved
                          while initiating connection to backend.
                        type: boolean
                    type: object
                  tcp:
                    description: Settings common to both HTTP and TCP upstream connections.
                    properties:
                      connectTimeout:
                        description: TCP connection timeout.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      idleTimeout:
                        description: The idle timeout for TCP connections.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      maxConnectionDuration:
                        description: The maximum duration of a connection.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      maxConnections:
                        description: Maximum number of HTTP1 /TCP connections to a
                          destination host.
                        format: int32
                        type: integer
                      tcpKeepalive:
                        description: If set then set SO_KEEPALIVE on the socket to
                          enable TCP Keepalives.
                        properties:
                          interval:
                            description: The time duration between keep-alive probes.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          probes:
                            description: Maximum number of keepalive probes to send
                              without response before deciding the connection is dead.
                            maximum: 4294967295
                            minimum: 0
                            type: integer
                          time:
                            description: The time duration a connection needs to be
                              idle before keep-alive probes start being sent.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                        type: object
                    type: object
                type: object
              ingress:
                description: Ingress specifies the configuration of the sidecar for
                  processing inbound traffic to the attached workload instance.
                items:
                  properties:
                    bind:
                      description: The IP(IPv4 or IPv6) to which the listener should
                        be bound.
                      type: string
                    captureMode:
                      description: |-
                        The captureMode option dictates how traffic to the listener is expected to be captured (or not).

                        Valid Options: DEFAULT, IPTABLES, NONE
                      enum:
                      - DEFAULT
                      - IPTABLES
                      - NONE
                      type: string
                    connectionPool:
                      description: Settings controlling the volume of connections
                        Envoy will accept from the network.
                      properties:
                        http:
                          description: HTTP connection pool settings.
                          properties:
                            h2UpgradePolicy:
                              description: |-
                                Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                                Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                              enum:
                              - DEFAULT
                              - DO_NOT_UPGRADE
                              - UPGRADE
                              type: string
                            http1MaxPendingRequests:
                              description: Maximum number of requests that will be
                                queued while waiting for a ready connection pool connection.
                              format: int32
                              type: integer
                            http2MaxRequests:
                              description: Maximum number of active requests to a
                                destination.
                              format: int32
                              type: integer
                            idleTimeout:
                              description: The idle timeout for upstream connection
                                pool connections.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            maxConcurrentStreams:
                              description: The maximum number of concurrent streams
                                allowed for a peer on one HTTP/2 connection.
                              format: int32
                              type: integer
                            maxRequestsPerConnection:
                              description: Maximum number of requests per connection
                                to a backend.
                              format: int32
                              type: integer
                            maxRetries:
                              description: Maximum number of retries that can be outstanding
                                to all hosts in a cluster at a given time.
                              format: int32
                              type: integer
                            useClientProtocol:
                              description: If set to true, client protocol will be
                                preserved while initiating connection to backend.
                              type: boolean
                          type: object
                        tcp:
                          description: Settings common to both HTTP and TCP upstream
                            connections.
                          properties:
                            connectTimeout:
                              description: TCP connection timeout.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            idleTimeout:
                              description: The idle timeout for TCP connections.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            maxConnectionDuration:
                              description: The maximum duration of a connection.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            maxConnections:
                              description: Maximum number of HTTP1 /TCP connections
                                to a destination host.
                              format: int32
                              type: integer
                            tcpKeepalive:
                              description: If set then set SO_KEEPALIVE on the socket
                                to enable TCP Keepalives.
                              properties:
                                interval:
                                  description: The time duration between keep-alive
                                    probes.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                probes:
                                  description: Maximum number of keepalive probes
                                    to send without response before deciding the connection
                                    is dead.
                                  maximum: 4294967295
                                  minimum: 0
                                  type: integer
                                time:
                                  description: The time duration a connection needs
                                    to be idle before keep-alive probes start being
                                    sent.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                              type: object
                          type: object
                      type: object
                    defaultEndpoint:
                      description: The IP endpoint or Unix domain socket to which
                        traffic should be forwarded to.
                      type: string
                    port:
                      description: The port associated with the listener.
                      properties:
                        name:
                          description: Label assigned to the port.
                          type: string
                        number:
                          description: A valid non-negative integer port number.
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                        protocol:
                          description: The protocol exposed on the port.
                          type: string
                        targetPort:
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                      type: object
                    tls:
                      description: Set of TLS related options that will enable TLS
                        termination on the sidecar for requests originating from outside
                        the mesh.
                      properties:
                        caCertificates:
                          description: REQUIRED if mode is `MUTUAL` or `OPTIONAL_MUTUAL`.
                          type: string
                        caCrl:
                          description: 'OPTIONAL: The path to the file containing
                            the certificate revocation list (CRL) to use in verifying
                            a presented client side certificate.'
                          type: string
                        cipherSuites:
                          description: 'Optional: If specified, only support the specified
                            cipher list.'
                          items:
                            type: string
                          type: array
                        credentialName:
                          description: For gateways running on Kubernetes, the name
                            of the secret that holds the TLS certs including the CA
                            certificates.
                          type: string
                        httpsRedirect:
                          description: If set to true, the load balancer will send
                            a 301 redirect for all http connections, asking the clients
                            to use HTTPS.
                          type: boolean
                        maxProtocolVersion:
                          description: |-
                            Optional: Maximum TLS protocol version.

                            Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
                          enum:
                          - TLS_AUTO
                          - TLSV1_0
                          - TLSV1_1
                          - TLSV1_2
                          - TLSV1_3
                          type: string
                        minProtocolVersion:
                          description: |-
                            Optional: Minimum TLS protocol version.

                            Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
                          enum:
                          - TLS_AUTO
                          - TLSV1_0
                          - TLSV1_1
                          - TLSV1_2
                          - TLSV1_3
                          type: string
                        mode:
                          description: |-
                            Optional: Indicates whether connections to this port should be secured using TLS.

                            Valid Options: PASSTHROUGH, SIMPLE, MUTUAL, AUTO_PASSTHROUGH, ISTIO_MUTUAL, OPTIONAL_MUTUAL
                          enum:
                          - PASSTHROUGH
                          - SIMPLE
                          - MUTUAL
                          - AUTO_PASSTHROUGH
                          - ISTIO_MUTUAL
                          - OPTIONAL_MUTUAL
                          type: string
                        privateKey:
                          description: REQUIRED if mode is `SIMPLE` or `MUTUAL`.
                          type: string
                        serverCertificate:
                          description: REQUIRED if mode is `SIMPLE` or `MUTUAL`.
                          type: string
                        subjectAltNames:
                          description: A list of alternate names to verify the subject
                            identity in the certificate presented by the client.
                          items:
                            type: string
                          type: array
                        verifyCertificateHash:
                          description: An optional list of hex-encoded SHA-256 hashes
                            of the authorized client certificates.
                          items:
                            type: string
                          type: array
                        verifyCertificateSpki:
                          description: An optional list of base64-encoded SHA-256
                            hashes of the SPKIs of authorized client certificates.
                          items:
                            type: string
                          type: array
                      type: object
                  required:
                  - port
                  type: object
                type: array
              outboundTrafficPolicy:
                description: Set the default behavior of the sidecar for handling
                  outbound traffic from the application.
                properties:
                  egressProxy:
                    properties:
                      host:
                        description: The name of a service from the service registry.
                        type: string
                      port:
                        description: Specifies the port on the host that is being
                          addressed.
                        properties:
                          number:
                            maximum: 4294967295
                            minimum: 0
                            type: integer
                        type: object
                      subset:
                        description: The name of a subset within the service.
                        type: string
                    required:
                    - host
                    type: object
                  mode:
                    description: |2-


                      Valid Options: REGISTRY_ONLY, ALLOW_ANY
                    enum:
                    - REGISTRY_ONLY
                    - ALLOW_ANY
                    type: string
                type: object
              workloadSelector:
                description: Criteria used to select the specific set of pods/VMs
                  on which this `Sidecar` configuration should be applied.
                properties:
                  labels:
                    additionalProperties:
                      maxLength: 63
                      type: string
                      x-kubernetes-validations:
                      - message: wildcard is not supported in selector
                        rule: '!self.contains(''*'')'
                    description: One or more labels that indicate a specific set of
                      pods/VMs on which the configuration should be applied.
                    maxProperties: 256
                    type: object
                type: object
            type: object
          status:
            properties:
              conditions:
                description: Current service state of the resource.
                items:
                  properties:
                    lastProbeTime:
                      description: Last time we probed the condition.
                      format: date-time
                      type: string
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      format: date-time
                      type: string
                    message:
                      description: Human-readable message indicating details about
                        last transition.
                      type: string
                    reason:
                      description: Unique, one-word, CamelCase reason for the condition's
                        last transition.
                      type: string
                    status:
                      description: Status is the status of the condition.
                      type: string
                    type:
                      description: Type is the type of the condition.
                      type: string
                  type: object
                type: array
              observedGeneration:
                anyOf:
                - type: integer
                - type: string
                description: Resource Generation to which the Reconciled Condition
                  refers.
                x-kubernetes-int-or-string: true
              validationMessages:
                description: Includes any errors or warnings detected by Istio's analyzers.
                items:
                  properties:
                    documentationUrl:
                      description: A url pointing to the Istio documentation for this
                        specific error type.
                      type: string
                    level:
                      description: |-
                        Represents how severe a message is.

                        Valid Options: UNKNOWN, ERROR, WARNING, INFO
                      enum:
                      - UNKNOWN
                      - ERROR
                      - WARNING
                      - INFO
                      type: string
                    type:
                      properties:
                        code:
                          description: A 7 character code matching `^IST[0-9]{4}$`
                            intended to uniquely identify the message type.
                          type: string
                        name:
                          description: A human-readable name for the message type.
                          type: string
                      type: object
                  type: object
                type: array
            type: object
            x-kubernetes-preserve-unknown-fields: true
        type: object
    served: true
    storage: false
    subresources:
      status: {}
  - name: v1beta1
    schema:
      openAPIV3Schema:
        properties:
          spec:
            description: 'Configuration affecting network reachability of a sidecar.
              See more details at: https://istio.io/docs/reference/config/networking/sidecar.html'
            properties:
              egress:
                description: Egress specifies the configuration of the sidecar for
                  processing outbound traffic from the attached workload instance
                  to other services in the mesh.
                items:
                  properties:
                    bind:
                      description: The IP(IPv4 or IPv6) or the Unix domain socket
                        to which the listener should be bound to.
                      type: string
                    captureMode:
                      description: |-
                        When the bind address is an IP, the captureMode option dictates how traffic to the listener is expected to be captured (or not).

                        Valid Options: DEFAULT, IPTABLES, NONE
                      enum:
                      - DEFAULT
                      - IPTABLES
                      - NONE
                      type: string
                    hosts:
                      description: One or more service hosts exposed by the listener
                        in `namespace/dnsName` format.
                      items:
                        type: string
                      type: array
                    port:
                      description: The port associated with the listener.
                      properties:
                        name:
                          description: Label assigned to the port.
                          type: string
                        number:
                          description: A valid non-negative integer port number.
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                        protocol:
                          description: The protocol exposed on the port.
                          type: string
                        targetPort:
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                      type: object
                  required:
                  - hosts
                  type: object
                type: array
              inboundConnectionPool:
                description: Settings controlling the volume of connections Envoy
                  will accept from the network.
                properties:
                  http:
                    description: HTTP connection pool settings.
                    properties:
                      h2UpgradePolicy:
                        description: |-
                          Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                          Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                        enum:
                        - DEFAULT
                        - DO_NOT_UPGRADE
                        - UPGRADE
                        type: string
                      http1MaxPendingRequests:
                        description: Maximum number of requests that will be queued
                          while waiting for a ready connection pool connection.
                        format: int32
                        type: integer
                      http2MaxRequests:
                        description: Maximum number of active requests to a destination.
                        format: int32
                        type: integer
                      idleTimeout:
                        description: The idle timeout for upstream connection pool
                          connections.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      maxConcurrentStreams:
                        description: The maximum number of concurrent streams allowed
                          for a peer on one HTTP/2 connection.
                        format: int32
                        type: integer
                      maxRequestsPerConnection:
                        description: Maximum number of requests per connection to
                          a backend.
                        format: int32
                        type: integer
                      maxRetries:
                        description: Maximum number of retries that can be outstanding
                          to all hosts in a cluster at a given time.
                        format: int32
                        type: integer
                      useClientProtocol:
                        description: If set to true, client protocol will be preserved
                          while initiating connection to backend.
                        type: boolean
                    type: object
                  tcp:
                    description: Settings common to both HTTP and TCP upstream connections.
                    properties:
                      connectTimeout:
                        description: TCP connection timeout.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      idleTimeout:
                        description: The idle timeout for TCP connections.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      maxConnectionDuration:
                        description: The maximum duration of a connection.
                        type: string
                        x-kubernetes-validations:
                        - message: must be a valid duration greater than 1ms
                          rule: duration(self) >= duration('1ms')
                      maxConnections:
                        description: Maximum number of HTTP1 /TCP connections to a
                          destination host.
                        format: int32
                        type: integer
                      tcpKeepalive:
                        description: If set then set SO_KEEPALIVE on the socket to
                          enable TCP Keepalives.
                        properties:
                          interval:
                            description: The time duration between keep-alive probes.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                          probes:
                            description: Maximum number of keepalive probes to send
                              without response before deciding the connection is dead.
                            maximum: 4294967295
                            minimum: 0
                            type: integer
                          time:
                            description: The time duration a connection needs to be
                              idle before keep-alive probes start being sent.
                            type: string
                            x-kubernetes-validations:
                            - message: must be a valid duration greater than 1ms
                              rule: duration(self) >= duration('1ms')
                        type: object
                    type: object
                type: object
              ingress:
                description: Ingress specifies the configuration of the sidecar for
                  processing inbound traffic to the attached workload instance.
                items:
                  properties:
                    bind:
                      description: The IP(IPv4 or IPv6) to which the listener should
                        be bound.
                      type: string
                    captureMode:
                      description: |-
                        The captureMode option dictates how traffic to the listener is expected to be captured (or not).

                        Valid Options: DEFAULT, IPTABLES, NONE
                      enum:
                      - DEFAULT
                      - IPTABLES
                      - NONE
                      type: string
                    connectionPool:
                      description: Settings controlling the volume of connections
                        Envoy will accept from the network.
                      properties:
                        http:
                          description: HTTP connection pool settings.
                          properties:
                            h2UpgradePolicy:
                              description: |-
                                Specify if http1.1 connection should be upgraded to http2 for the associated destination.

                                Valid Options: DEFAULT, DO_NOT_UPGRADE, UPGRADE
                              enum:
                              - DEFAULT
                              - DO_NOT_UPGRADE
                              - UPGRADE
                              type: string
                            http1MaxPendingRequests:
                              description: Maximum number of requests that will be
                                queued while waiting for a ready connection pool connection.
                              format: int32
                              type: integer
                            http2MaxRequests:
                              description: Maximum number of active requests to a
                                destination.
                              format: int32
                              type: integer
                            idleTimeout:
                              description: The idle timeout for upstream connection
                                pool connections.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            maxConcurrentStreams:
                              description: The maximum number of concurrent streams
                                allowed for a peer on one HTTP/2 connection.
                              format: int32
                              type: integer
                            maxRequestsPerConnection:
                              description: Maximum number of requests per connection
                                to a backend.
                              format: int32
                              type: integer
                            maxRetries:
                              description: Maximum number of retries that can be outstanding
                                to all hosts in a cluster at a given time.
                              format: int32
                              type: integer
                            useClientProtocol:
                              description: If set to true, client protocol will be
                                preserved while initiating connection to backend.
                              type: boolean
                          type: object
                        tcp:
                          description: Settings common to both HTTP and TCP upstream
                            connections.
                          properties:
                            connectTimeout:
                              description: TCP connection timeout.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            idleTimeout:
                              description: The idle timeout for TCP connections.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            maxConnectionDuration:
                              description: The maximum duration of a connection.
                              type: string
                              x-kubernetes-validations:
                              - message: must be a valid duration greater than 1ms
                                rule: duration(self) >= duration('1ms')
                            maxConnections:
                              description: Maximum number of HTTP1 /TCP connections
                                to a destination host.
                              format: int32
                              type: integer
                            tcpKeepalive:
                              description: If set then set SO_KEEPALIVE on the socket
                                to enable TCP Keepalives.
                              properties:
                                interval:
                                  description: The time duration between keep-alive
                                    probes.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                                probes:
                                  description: Maximum number of keepalive probes
                                    to send without response before deciding the connection
                                    is dead.
                                  maximum: 4294967295
                                  minimum: 0
                                  type: integer
                                time:
                                  description: The time duration a connection needs
                                    to be idle before keep-alive probes start being
                                    sent.
                                  type: string
                                  x-kubernetes-validations:
                                  - message: must be a valid duration greater than
                                      1ms
                                    rule: duration(self) >= duration('1ms')
                              type: object
                          type: object
                      type: object
                    defaultEndpoint:
                      description: The IP endpoint or Unix domain socket to which
                        traffic should be forwarded to.
                      type: string
                    port:
                      description: The port associated with the listener.
                      properties:
                        name:
                          description: Label assigned to the port.
                          type: string
                        number:
                          description: A valid non-negative integer port number.
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                        protocol:
                          description: The protocol exposed on the port.
                          type: string
                        targetPort:
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                      type: object
                    tls:
                      description: Set of TLS related options that will enable TLS
                        termination on the sidecar for requests originating from outside
                        the mesh.
                      properties:
                        caCertificates:
                          description: REQUIRED if mode is `MUTUAL` or `OPTIONAL_MUTUAL`.
                          type: string
                        caCrl:
                          description: 'OPTIONAL: The path to the file containing
                            the certificate revocation list (CRL) to use in verifying
                            a presented client side certificate.'
                          type: string
                        cipherSuites:
                          description: 'Optional: If specified, only support the specified
                            cipher list.'
                          items:
                            type: string
                          type: array
                        credentialName:
                          description: For gateways running on Kubernetes, the name
                            of the secret that holds the TLS certs including the CA
                            certificates.
                          type: string
                        httpsRedirect:
                          description: If set to true, the load balancer will send
                            a 301 redirect for all http connections, asking the clients
                            to use HTTPS.
                          type: boolean
                        maxProtocolVersion:
                          description: |-
                            Optional: Maximum TLS protocol version.

                            Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
                          enum:
                          - TLS_AUTO
                          - TLSV1_0
                          - TLSV1_1
                          - TLSV1_2
                          - TLSV1_3
                          type: string
                        minProtocolVersion:
                          description: |-
                            Optional: Minimum TLS protocol version.

                            Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
                          enum:
                          - TLS_AUTO
                          - TLSV1_0
                          - TLSV1_1
                          - TLSV1_2
                          - TLSV1_3
                          type: string
                        mode:
                          description: |-
                            Optional: Indicates whether connections to this port should be secured using TLS.

                            Valid Options: PASSTHROUGH, SIMPLE, MUTUAL, AUTO_PASSTHROUGH, ISTIO_MUTUAL, OPTIONAL_MUTUAL
                          enum:
                          - PASSTHROUGH
                          - SIMPLE
                          - MUTUAL
                          - AUTO_PASSTHROUGH
                          - ISTIO_MUTUAL
                          - OPTIONAL_MUTUAL
                          type: string
                        privateKey:
                          description: REQUIRED if mode is `SIMPLE` or `MUTUAL`.
                          type: string
                        serverCertificate:
                          description: REQUIRED if mode is `SIMPLE` or `MUTUAL`.
                          type: string
                        subjectAltNames:
                          description: A list of alternate names to verify the subject
                            identity in the certificate presented by the client.
                          items:
                            type: string
                          type: array
                        verifyCertificateHash:
                          description: An optional list of hex-encoded SHA-256 hashes
                            of the authorized client certificates.
                          items:
                            type: string
                          type: array
                        verifyCertificateSpki:
                          description: An optional list of base64-encoded SHA-256
                            hashes of the SPKIs of authorized client certificates.
                          items:
                            type: string
                          type: array
                      type: object
                  required:
                  - port
                  type: object
                type: array
              outboundTrafficPolicy:
                description: Set the default behavior of the sidecar for handling
                  outbound traffic from the application.
                properties:
                  egressProxy:
                    properties:
                      host:
                        description: The name of a service from the service registry.
                        type: string
                      port:
                        description: Specifies the port on the host that is being
                          addressed.
                        properties:
                          number:
                            maximum: 4294967295
                            minimum: 0
                            type: integer
                        type: object
                      subset:
                        description: The name of a subset within the service.
                        type: string
                    required:
                    - host
                    type: object
                  mode:
                    description: |2-


                      Valid Options: REGISTRY_ONLY, ALLOW_ANY
                    enum:
                    - REGISTRY_ONLY
                    - ALLOW_ANY
                    type: string
                type: object
              workloadSelector:
                description: Criteria used to select the specific set of pods/VMs
                  on which this `Sidecar` configuration should be applied.
                properties:
                  labels:
                    additionalProperties:
                      maxLength: 63
                      type: string
                      x-kubernetes-validations:
                      - message: wildcard is not supported in selector
                        rule: '!self.contains(''*'')'
                    description: One or more labels that indicate a specific set of
                      pods/VMs on which the configuration should be applied.
                    maxProperties: 256
                    type: object
                type: object
            type: object
          status:
            properties:
              conditions:
                description: Current service state of the resource.
                items:
                  properties:
                    lastProbeTime:
                      description: Last time we probed the condition.
                      format: date-time
                      type: string
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      format: date-time
                      type: string
                    message:
                      description: Human-readable message indicating details about
                        last transition.
                      type: string
                    reason:
                      description: Unique, one-word, CamelCase reason for the condition's
                        last transition.
                      type: string
                    status:
                      description: Status is the status of the condition.
                      type: string
                    type:
                      description: Type is the type of the condition.
                      type: string
                  type: object
                type: array
              observedGeneration:
                anyOf:
                - type: integer
                - type: string
                description: Resource Generation to which the Reconciled Condition
                  refers.
                x-kubernetes-int-or-string: true
              validationMessages:
                description: Includes any errors or warnings detected by Istio's analyzers.
                items:
                  properties:
                    documentationUrl:
                      description: A url pointing to the Istio documentation for this
                        specific error type.
                      type: string
                    level:
                      description: |-
                        Represents how severe a message is.

                        Valid Options: UNKNOWN, ERROR, WARNING, INFO
                      enum:
                      - UNKNOWN
                      - ERROR
                      - WARNING
                      - INFO
                      type: string
                    type:
                      properties:
                        code:
                          description: A 7 character code matching `^IST[0-9]{4}$`
                            intended to uniquely identify the message type.
                          type: string
                        name:
                          description: A human-readable name for the message type.
                          type: string
                      type: object
                  type: object
                type: array
            type: object
            x-kubernetes-preserve-unknown-fields: true
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    categories:
    - istio-io
    - networking-istio-io
    kind: Sidecar
    listKind: SidecarList
    plural: sidecars
    singular: sidecar
  conditions:
  - lastTransitionTime: "2025-03-10T19:25:39Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-03-10T19:25:40Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1beta1
