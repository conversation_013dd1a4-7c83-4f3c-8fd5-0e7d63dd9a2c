# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

pass:
  - test: default
    validation: validation.yaml
    resources: resources.json
    expected-validation: true
  - test: change-accessLogFile-to-different-dir
    validation: validation.yaml
    resources: resources.json
    permutation: '.istioMeshConfig.accessLogFile = "/log/test"'
    expected-validation: false
  - test: remove-accessLogFile
    validation: validation.yaml
    resources: resources.json
    permutation: "del(.istioMeshConfig.accessLogFile)"
    expected-validation: false
