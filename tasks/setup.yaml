# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

tasks:
  - name: create-k3d-cluster
    inputs:
      K3D_EXTRA_ARGS:
        default: ""
    actions:
      - description: "Create the K3d cluster"
        # renovate: datasource=github-tags depName=defenseunicorns/uds-k3d versioning=semver
        cmd: "uds zarf package deploy oci://defenseunicorns/uds-k3d:0.14.3 --confirm --set K3D_EXTRA_ARGS='${{ .inputs.K3D_EXTRA_ARGS }}' --no-progress"

  - name: k3d-test-cluster
    actions:
      - task: create-k3d-cluster

      - description: "Initialize the cluster with Zarf"
        # renovate: datasource=github-tags depName=zarf-dev/zarf versioning=semver
        cmd: "uds zarf package deploy oci://ghcr.io/zarf-dev/packages/init:v0.57.0 --confirm --no-progress"

  - name: ha-postgres
    actions:
      - description: "Cleanup previous PostgreSQL runs"
        cmd: |
          docker kill postgres || true
          docker rm postgres || true
      - description: "Create a network for the PostgreSQL container"
        cmd: docker network create k3d-uds || true
      - description: "Generate PostgreSQL certs"
        cmd: |
          mkdir -p build/certs
          openssl req -x509 -newkey rsa:4096 -sha256 -nodes \
            -keyout build/certs/server.key \
            -out build/certs/server.crt \
            -days 365 -subj "/CN=postgres" > /dev/null 2>&1 || {
              echo "Error: Failed to generate PostgreSQL certs"
              exit 1
            }
          chmod 600 build/certs/server.key
          chmod 644 build/certs/server.crt
      - description: "Prepare PostgreSQL certs inside a temp container"
        cmd: |
          docker run --rm \
            -v $(pwd)/build/certs:/certs \
            --entrypoint bash postgres:16 \
            -c "cp /certs/server.key /certs/fixed.key && \
                cp /certs/server.crt /certs/fixed.crt && \
                chown 999:999 /certs/fixed.key /certs/fixed.crt && \
                chmod 600 /certs/fixed.key && chmod 644 /certs/fixed.crt"
      - description: "Start PostgreSQL Docker container"
        cmd: |
          CONTAINER_NAME=postgres
          # We're using Postgres 16 as this is the compatibility version for RDS
          POSTGRES_VERSION=16
          # RDS (Postgres 16) supports only TLS 1.2 only
          TLS_VERSION=TLSv1.2
          docker run -p 5432:5432 --network=k3d-uds --rm --name $CONTAINER_NAME \
            -e POSTGRES_DB=keycloak \
            -e POSTGRES_USER=postgres \
            -e POSTGRES_PASSWORD='unicorn123!@#UN' \
            -v $(pwd)/build/certs/fixed.crt:/var/lib/postgresql/server.crt:ro \
            -v $(pwd)/build/certs/fixed.key:/var/lib/postgresql/server.key:ro \
            -d postgres:$POSTGRES_VERSION \
            -c ssl=on \
            -c ssl_cert_file=/var/lib/postgresql/server.crt \
            -c ssl_key_file=/var/lib/postgresql/server.key \
            -c ssl_min_protocol_version=$TLS_VERSION \
            -c ssl_max_protocol_version=$TLS_VERSION \
            -c log_connections=on \
            -c log_disconnections=on \
            -c log_min_messages=debug1 \
            -c log_line_prefix='%m [%p] %q%u@%d ' \
            -c log_statement=all
      - description: "Wait for PostgreSQL to be ready"
        cmd: |
          for i in {1..10}; do
            docker exec postgres pg_isready -U postgres && break
            sleep 1
          done
      - description: "Add Grafana database to PostgreSQL"
        cmd: docker exec postgres psql -U postgres -c "CREATE DATABASE grafana;"
