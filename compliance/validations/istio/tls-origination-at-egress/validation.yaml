# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

metadata:
  name: tls-origination-at-egress-PLACEHOLDER
  uuid: 8be1601e-5870-4573-ab4f-c1c199944815
domain:
  type: kubernetes
  kubernetes-spec:
    resources: []
provider:
  type: opa
  opa-spec:
    rego: |
      package validate

      default validate := false

      # How to prove TLS origination is configured at egress
      # DestinationRule?
