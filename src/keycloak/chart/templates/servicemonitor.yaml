# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

{{- range $key, $serviceMonitor := dict "metrics" .Values.serviceMonitor "extra" .Values.extraServiceMonitor }}
{{- with $serviceMonitor }}
{{- if .enabled }}
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "keycloak.fullname" $ }}-{{ $key }}
  {{- with .namespace }}
  namespace: {{ . }}
  {{- end }}
  {{- with .annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
  {{- end }}
  labels:
  {{- include "keycloak.labels" $ | nindent 4 }}
  {{- range $key, $value := .labels }}
  {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
  {{- end }}
spec:
  {{- with .namespaceSelector }}
  namespaceSelector:
  {{- toYaml . | nindent 4 }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "keycloak.selectorLabels" $ | nindent 6 }}
      app.kubernetes.io/component: http
  endpoints:
    - port: {{ .port }}
      path: {{ .path }}
      interval: {{ .interval }}
      scrapeTimeout: {{ .scrapeTimeout }}
      {{- if .scheme }}
      scheme: {{ .scheme }}
      {{- end }}
      {{- if .tlsConfig }}
      tlsConfig:
        {{- toYaml .tlsConfig | nindent 8 }}
      {{- end }}
{{- end }}
{{- end }}
{{- end }}
