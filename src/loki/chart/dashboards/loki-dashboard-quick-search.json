{"annotations": {"list": [{"$$hashKey": "object:75", "builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": 12019, "graphTooltip": 0, "id": null, "iteration": 1586288900167, "links": [], "panels": [{"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "<PERSON>", "fill": 1, "fillGradient": 0, "gridPos": {"h": 3, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(count_over_time({namespace=\"$namespace\", pod=~\"$pod\"} |~ \"$search\"[$__interval]))", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:168", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}, {"$$hashKey": "object:169", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "<PERSON>", "gridPos": {"h": 25, "w": 24, "x": 0, "y": 3}, "id": 2, "maxDataPoints": "", "options": {"showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": true}, "targets": [{"expr": "{namespace=\"$namespace\", pod=~\"$pod\"} |~ \"$search\"", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Logs Panel", "type": "logs"}, {"content": "<div style=\"text-align:center\"> For Grafana Loki blog example </div>\n\n\n", "datasource": null, "gridPos": {"h": 3, "w": 24, "x": 0, "y": 28}, "id": 4, "mode": "html", "timeFrom": null, "timeShift": null, "title": "", "transparent": true, "type": "text"}], "schemaVersion": 22, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {}, "datasource": "Prometheus", "definition": "label_values(kube_pod_info, namespace)", "hide": 0, "includeAll": false, "index": -1, "label": null, "multi": false, "name": "namespace", "options": [], "query": "label_values(kube_pod_info, namespace)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {}, "datasource": "Prometheus", "definition": "label_values(container_network_receive_bytes_total{namespace=~\"$namespace\"},pod)", "hide": 0, "includeAll": true, "index": -1, "label": null, "multi": true, "name": "pod", "options": [], "query": "label_values(container_network_receive_bytes_total{namespace=~\"$namespace\"},pod)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "", "value": ""}, "hide": 0, "label": null, "name": "search", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Loki Dashboard quick search", "uid": "liz0yRCZz", "variables": {"list": []}, "version": 1, "description": "Loki logs panel with prometheus variables "}