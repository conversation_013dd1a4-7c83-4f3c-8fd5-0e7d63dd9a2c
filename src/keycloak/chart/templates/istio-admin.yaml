# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

{{- if .Capabilities.APIVersions.Has "security.istio.io/v1beta1" }}
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: keycloak-block-admin-access-from-public-gateway
  namespace: {{ .Release.Namespace }}
spec:
  targetRefs:
    - kind: Gateway
      group: gateway.networking.k8s.io
      name: {{ include "keycloak.fullname" . }}-waypoint
  action: DENY
  rules:
    - to:
        - operation:
            ports:
              - "8080"
            paths:
              - "/admin*"
              - "/realms/master*"
      from:
        - source:
            notNamespaces:
              - istio-admin-gateway
              - "pepr-system"
    - to:
        - operation:
            ports:
              - "8080"
            paths:
            - /metrics*
      from:
        - source:
            notNamespaces:
            - istio-admin-gateway
            - monitoring
    - to:
        - operation:
            ports:
              - "8080"
            paths:
              - "/admin/realms/{{ .Values.realm }}/clients"
      from:
        - source:
            notNamespaces:
              - pepr-system
              - istio-admin-gateway
    - when:
        - key: request.headers[istio-mtls-client-certificate]
          values: ["*"]
      to:
        - operation:
            ports:
              - "8080"
      from:
        - source:
            notNamespaces:
            - istio-tenant-gateway
            - istio-admin-gateway
            {{- range .Values.additionalGatewayNamespaces }}
            {{- if not (hasPrefix "istio-" .) }}
              {{- fail (printf "Allowed gateway namespace '%s' must start with 'istio-'" .) }}
            {{- end }}
            - {{ . }}
            {{- end }}
{{- end }}
