## Or, do all this in the helm chart... because if you're adding gateways and global services across packages they could clobber each other? I don't totally understand how this is different from what they're doing with ingress gateways right now...
# Could you create a gateway in the helm chart then mutate it every time a new package wants to expose it?

## Exists, per pkg and operator

# Gateway

# Egress Gateway workload

# Sample network pkg
# Create the UDS Package CR
# Also sample deployment...?

allowRemoteAnywhere: true  # defaults to false if egress gateways; true if not?

network:
  allow:
    - direction: Egress
      selector:
        app.kubernetes.io/name: vector
      description: "Elastic Storage"
      # If remoteHost is defined, this supercedes remoteNamespace, remotePodLabels, and remoteSelector
      remoteHost: "svc.elastic.com"
      # if remoteHost then add auth policy...
      remoteProtocol: "https" | "tls" | "http" # "https" error in short term, because tls origination
      port: 9200
      # if https -> 
      # affects service entry - means the sidecar of the workload will attempt to originate the connection (i.e., provide the client certificate)

	    # remoteGenerated must be Anywhere? 
	    # what if you did this with like something in the cluster? then I guess you could still add like a service entry or auth policy or something??
	    # remoteGenerated: 
	    # remoteSelector I guess could be used to ID the gateway....
	    # or egressSelector?

# Question - what, if anything, do I need to do with the port?

# Pepr TODO:
# Create gateway
apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: {{ allow.cleanDescription }}-gateway
  namespace: {{ pkg.namespace }} # In the namespace of the gateway
spec:
  selector:
    app: {{}}-egressgateway
  servers:
  # if tls -> 
  - port:
      number: 9200
      name: tls
      protocol: TLS
    hosts:
      - "svc.elastic.com"
    tls:
      mode: PASSTHROUGH
  # if http -> 
  - port:
      number: 9200
      name: http
      protocol: HTTP
    hosts:
      - "*"
    tls:
      mode: PASSTHROUGH
  # if https ->
  - port:
      number: 9200
      name: https
      protocol: HTTPS
    hosts:
      - "*"
    tls:
      mode: PASSTHROUGH

# Add ServiceEntry for each `network.allow[].remoteHosts` is non-empty
# Enables adding additional entries into Istio's service registry
# Q: Do I need to specify the ports or do these point to the GW ports?
# How does this know which gateway to use?
# Is the problem that if multiple packages define the same hosts differently?
# How do I check the istio service registry?
apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: {{ pkg.namespace }}-{{ allow.cleanDescription }}-service-entry
  namespace: {{ pkg.namespace }}
spec:
  hosts: # Add from `remoteHost` in network config
  - svc.elastic.com
  ports: # Do these ports need to be the source service or simply the same as the GW?
  # if tls -> 
  - number: 9200
    name: tls
    protocol: TLS
  # if https -> 
  - number: 9200
    name: https
    protocol: HTTPS
  resolution: DNS
  exportTo: "." # keeps it in this ns - I think don't want this?

# Add Destination Rule?
# Do I need this or is it more like "best practice" to add?
# Probably would change for tls passthrough
apiVersion: networking.istio.io/v1
kind: DestinationRule
metadata:
  name: egressgateway-for-{{ allow.cleanDescription }}
  namespace: {{ pkg.namespace }}
spec:
  host: egressgateway.istio-egress-gateway.svc.cluster.local
  subsets:
    - name: {{ allow.cleanDescription }}

# Add Virtual Service
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: {{ allow.cleanDescription }}-through-egress-gateway
  namespace: {{ pkg.namespace }}
spec:
  hosts: # Add from `remoteHost` in network config
  - svc.elastic.com
  gateways:
  - egressgateway
  - mesh
# if "tls" ->
  tls:
  - match:
    - gateways:
      - mesh
      port: 9200
      sniHosts:
      - svc.elastic.com
    route:
    - destination:
        host: egressgateway.istio-system.svc.cluster.local
        subset: {{ allow.cleanDescription }}
        port:
          number: 9200
  - match:
    - gateways:
      - egressgateway
      port: 9200
      sniHosts:
      - svc.elastic.com
    route:
    - destination:
        host: foo.host.com
        port:
          number: 9200
# if "http" | "https" ->
  http:
  - match:
    - gateways:
      - mesh
      port: 9200
    route:
    - destination:
        host: egressgateway.istio-system.svc.cluster.local
        subset: {{ allow.cleanDescription }}
        port:
          number: 9200
  - match:
    - gateways:
      - egressgateway
      port: 9200
    route:
    - destination:
        host: foo.host.com
        port:
          number: 9200