{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 10, "w": 3, "x": 0, "y": 0}, "id": 38, "options": {"content": "<div style=\"text-align:center\">\n  \n  ![NeuVector Logo](https://avatars.githubusercontent.com/u/19367275?s=200&v=4)<br>\n  <br>\n  [Documentation](https://open-docs.neuvector.com)<br>\n  </br>\n  [Users Slack Channel](https://rancher-users.slack.com/archives/C036F6JDZ8C)<br>\n  </br>\n  [GitHub](https://github.com/neuvector)\n\n</div>", "mode": "markdown"}, "pluginVersion": "9.1.5", "title": "NeuVector Product Links", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 0}, "id": 25, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "nv_summary_enforcers", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{target}}", "refId": "A"}], "title": "Enforcer Replica Count", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 3, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 0}, "id": 8, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "nv_summary_cvedbVersion", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{target}}", "refId": "A"}], "title": "CVE Database Version", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 9, "y": 0}, "id": 20, "links": [], "maxDataPoints": 1000, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "nv_summary_pods", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{target}}", "refId": "A"}], "title": "Discovered Pod Count", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 0}, "id": 34, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "max(nv_controller_cpu) by (display)\n", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{display}}", "range": true, "refId": "A"}], "title": "Controller CPU Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 3}, "id": 32, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "nv_admission_denied", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Denied Admissions", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-RdYlGr"}, "mappings": [{"options": {"1": {"color": "light-orange", "index": 1}, "2": {"color": "yellow", "index": 2}, "3": {"color": "green", "index": 3}}, "type": "value"}, {"options": {"match": "null", "result": {"index": 0, "text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 3}, "id": 2, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "nv_summary_controllers", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{target}}", "refId": "A"}], "title": "Controller Replicas", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 9, "y": 3}, "id": 19, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "value"}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "nv_summary_disconnectedEnforcers", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{target}}", "refId": "A"}], "title": "Disconnected Enforcers", "type": "stat"}, {"columns": [{"text": "Current", "value": "current"}], "datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "auto", "filterable": false, "inspect": false, "width": 300}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "string"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "log"}, "properties": [{"id": "custom.width", "value": 101}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "color", "value": {"fixedColor": "light-orange", "mode": "fixed"}}, {"id": "displayName", "value": "Event Type"}, {"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "custom.filterable", "value": true}, {"id": "displayName", "value": "Violation Type"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Last seen"}, "properties": [{"id": "unit", "value": "dateTimeAsIso"}, {"id": "custom.width", "value": 200}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "fromname"}, "properties": [{"id": "displayName", "value": "Source Pod"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "toname"}, "properties": [{"id": "displayName", "value": "Destination Pod"}]}]}, "fontSize": "90%", "gridPos": {"h": 8, "w": 9, "x": 3, "y": 6}, "id": 29, "links": [], "options": {"footer": {"enablePagination": true, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Last seen"}]}, "pluginVersion": "9.1.5", "scroll": true, "showHeader": true, "sort": {"col": 1, "desc": true}, "styles": [{"alias": "Event", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm", "decimals": 2, "link": false, "mappingType": 1, "pattern": "Metric", "preserveFormat": false, "sanitize": true, "thresholds": [], "type": "string", "unit": "short"}, {"alias": "Time", "colorMode": "value", "colors": ["#E0B400", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 0, "pattern": "Current", "thresholds": [], "type": "number", "unit": "dateTimeAsIso"}], "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "nv_log_events", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "range": false, "refId": "A"}], "title": "Security Event Log", "transform": "timeseries_aggregations", "transformations": [{"id": "labelsToFields", "options": {}}, {"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "endpoint": true, "fromns": true, "id": true, "instance": true, "job": true, "namespace": true, "pod": true, "service": true, "target": true, "tons": true}, "indexByName": {"Time": 0, "Value": 14, "endpoint": 1, "fromname": 7, "fromns": 15, "id": 2, "instance": 3, "job": 4, "log": 5, "name": 6, "namespace": 8, "pod": 9, "service": 10, "target": 11, "toname": 12, "tons": 13}, "renameByName": {}}}, {"id": "groupBy", "options": {"fields": {"Value": {"aggregations": ["max"], "operation": "aggregate"}, "fromname": {"aggregations": [], "operation": "groupby"}, "log": {"aggregations": [], "operation": "groupby"}, "name": {"aggregations": [], "operation": "groupby"}, "toname": {"aggregations": [], "operation": "groupby"}}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"Value (lastNotNull)": "Last seen", "Value (max)": "Last seen"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "left", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 6}, "id": 12, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "max(nv_controller_memory) by (display)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{display}}", "range": true, "refId": "A"}], "title": "Controller Memory Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": [], "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "High"}, {"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Medium"}, {"id": "color", "value": {"fixedColor": "light-orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 14, "w": 3, "x": 0, "y": 10}, "id": 24, "links": [], "options": {"displayLabels": ["value"], "legend": {"displayMode": "list", "placement": "bottom", "showLegend": true, "values": []}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "none", "sort": "none"}}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(nv_container_vulnerabilityHigh) by (service)", "format": "table", "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(nv_container_vulnerabilityMedium) by (service)", "format": "table", "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "B"}], "title": "Cluster CVE Count", "transformations": [{"id": "merge", "options": {"reducers": []}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {}}}], "type": "piechart"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 12}, "hiddenSeries": false, "id": 10, "legend": {"avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "max(nv_enforcer_cpu) by (display)\n", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{display}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Enforcer CPU Usage", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:865", "format": "percentunit", "logBase": 1, "show": true}, {"$$hashKey": "object:866", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "auto", "inspect": false, "width": 101}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "exported_service"}, "properties": [{"id": "custom.filterable", "value": true}, {"id": "displayName", "value": "Cluster Service Name"}, {"id": "custom.inspect", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "High"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, {"id": "custom.displayMode", "value": "color-text"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "custom.displayMode", "value": "color-text"}, {"id": "displayName", "value": "Medium"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "light-orange", "value": 1}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "exported_service"}, "properties": [{"id": "custom.width", "value": 300}, {"id": "custom.align", "value": "right"}, {"id": "displayName", "value": "Cluster Service Name"}]}]}, "gridPos": {"h": 10, "w": 4, "x": 3, "y": 14}, "id": 36, "links": [], "options": {"footer": {"enablePagination": true, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(nv_container_vulnerabilityHigh) by (exported_service)", "format": "table", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(nv_container_vulnerabilityMedium) by (exported_service)", "format": "table", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "B"}], "title": "Vulnerabilities by Service", "transformations": [{"id": "merge", "options": {"reducers": []}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "auto", "filterable": false, "inspect": false, "minWidth": 50}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "unit", "value": "string"}, {"id": "custom.align", "value": "right"}, {"id": "custom.inspect", "value": true}, {"id": "custom.filterable", "value": true}, {"id": "displayName", "value": "Repository/Image: Tag"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "High"}, {"id": "unit", "value": "none"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "color"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Medium"}, {"id": "unit", "value": "none"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "light-orange", "value": 1}]}}, {"id": "color"}]}]}, "gridPos": {"h": 10, "w": 5, "x": 7, "y": 14}, "id": 33, "links": [], "options": {"footer": {"enablePagination": true, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(nv_image_vulnerabilityHigh) by (name)", "format": "table", "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(nv_image_vulnerabilityMedium) by (name)", "format": "table", "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "B"}], "title": "Registry Images Vulnerabilities", "transformations": [{"id": "merge", "options": {"reducers": []}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {}}}], "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 18}, "hiddenSeries": false, "id": 35, "legend": {"avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "max(nv_enforcer_memory) by (display)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{display}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Enforcer Memory Usage", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:940", "format": "bytes", "logBase": 1, "show": true}, {"$$hashKey": "object:941", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": "15s", "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"hidden": false, "refresh_intervals": ["5s", "10s", "15s", "30s", "1m", "5m", "15m", "30m", "1h"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "UTC", "title": "NeuVector", "uid": "nv_dashboard0001", "version": 2, "weekStart": ""}