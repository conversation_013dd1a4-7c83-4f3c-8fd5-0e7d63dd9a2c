# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

tasks:
  - name: validate
    actions:
      - description: Wait for metrics-server pod(s) to be ready
        wait:
          cluster:
            kind: Deployment
            name: metrics-server
            namespace: metrics-server
      - description: Wait for metrics-server API to be ready
        wait:
          cluster:
            kind: APIService
            name: v1beta1.metrics.k8s.io
            condition: available

  - name: e2e-test
    actions:
      - description: "Run Metrics Server E2E tests"
        cmd: |
          npm ci && npx vitest run "metrics-server"
        dir: test/vitest
