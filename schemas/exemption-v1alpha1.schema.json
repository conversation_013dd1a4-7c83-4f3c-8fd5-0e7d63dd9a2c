{"$schema": "http://json-schema.org/draft-06/schema#", "$ref": "#/definitions/Exemption", "definitions": {"Exemption": {"type": "object", "additionalProperties": {}, "properties": {"spec": {"$ref": "#/definitions/Spec"}}, "required": [], "title": "Exemption"}, "Spec": {"type": "object", "additionalProperties": false, "properties": {"exemptions": {"type": "array", "items": {"$ref": "#/definitions/ExemptionElement"}, "description": "Policy exemptions"}}, "required": ["exemptions"], "title": "Spec"}, "ExemptionElement": {"type": "object", "additionalProperties": false, "properties": {"description": {"type": "string", "description": "Reasons as to why this exemption is needed"}, "matcher": {"$ref": "#/definitions/Matcher", "description": "Resource to exempt (Regex allowed for name)"}, "policies": {"type": "array", "items": {"$ref": "#/definitions/Policy"}, "description": "A list of policies to override"}, "title": {"type": "string", "description": "title to give the exemption for reporting purposes"}}, "required": ["matcher", "policies"], "title": "ExemptionElement"}, "Matcher": {"type": "object", "additionalProperties": false, "properties": {"kind": {"$ref": "#/definitions/Kind"}, "name": {"type": "string"}, "namespace": {"type": "string"}}, "required": ["name", "namespace"], "title": "Matcher", "description": "Resource to exempt (Regex allowed for name)"}, "Kind": {"type": "string", "enum": ["pod", "service"], "title": "Kind"}, "Policy": {"type": "string", "enum": ["DisallowHostNamespaces", "DisallowNodePortServices", "DisallowPrivileged", "DisallowSELinuxOptions", "DropAllCapabilities", "RequireNonRootUser", "RestrictCapabilities", "RestrictExternalNames", "RestrictHostPathWrite", "RestrictHostPorts", "RestrictProcMount", "RestrictSeccomp", "RestrictSELinuxType", "RestrictVolumeTypes"], "title": "Policy"}}}