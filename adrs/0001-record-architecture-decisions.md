# 1. Record architecture decisions

Date: 2024-07-17

## Status

Accepted

## Context

> NOTE:
>
> This file was automatically created when we used [adr-tools](https://github.com/npryce/adr-tools) to initialize the document log in the repo. ADRs on ADRs are a little silly, but it does give a lightweight way to direct the reader over to our contributor guide that has a lot more information.

We need to record the architectural decisions made on this project.

## Decision

We will use Architecture Decision Records, as [described by <PERSON>](http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions), with a couple of small tweaks. See the [Contributor guide](https://github.com/defenseunicorns/uds-core/blob/main/CONTRIBUTING.md) for full details.

## Consequences

See <PERSON>'s article, linked above. For a lightweight ADR toolset, see <PERSON>'s [adr-tools](https://github.com/npryce/adr-tools).
