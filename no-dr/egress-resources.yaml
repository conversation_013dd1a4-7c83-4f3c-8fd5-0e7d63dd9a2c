apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: service-entry-1
  namespace: ns-1
spec:
  location: MESH_EXTERNAL
  hosts:
  - example.com
  ports: 
  - number: 443
    name: tls
    protocol: TLS
  resolution: DNS
  exportTo:
    - "."
    - "istio-egress-gateway"
---
# Global gateway, destination rule, virtual service
apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: example-com-443-tls-gateway
  namespace: istio-egress-gateway
spec:
  selector:
    app: egressgateway
  servers:
  - port:
      number: 443
      name: tls
      protocol: TLS
    hosts:
      - example.com
    tls:
      mode: PASSTHROUGH
---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: through-egress-gateway-1
  namespace: istio-egress-gateway
  annotations:
    uds-core.pepr.dev/egress: '{"pkgs":["pkg-1", "pkg-2"], "route": {"host": "example.com", "port": 443, "protcol": "tls"}}' 
spec:
  hosts:
  - example.com
  gateways:
  - example-com-443-tls-gateway
  - mesh
  tls:
  - match:
    - gateways:
      - mesh
      port: 443
      sniHosts:
      - example.com
    route:
    - destination:
        host: egressgateway.istio-egress-gateway.svc.cluster.local
        port:
          number: 443
  - match:
    - gateways:
      - example-com-443-tls-gateway
      port: 443
      sniHosts:
      - example.com
    route:
    - destination:
        host: example.com
        port:
          number: 443
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-egress-gateway-access-1
  namespace: ns-1
spec:
  podSelector: 
    matchLabels:
      app: curl1
  policyTypes:
  - Egress
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: istio-egress-gateway
      podSelector:
        matchLabels:
          app: egressgateway
    ports:
    - protocol: TCP
      port: 443