{"authorizationPolicies": [{"apiVersion": "security.istio.io/v1beta1", "kind": "AuthorizationPolicy", "metadata": {"annotations": {"meta.helm.sh/release-name": "keycloak", "meta.helm.sh/release-namespace": "keycloak"}, "creationTimestamp": "2024-06-07T14:37:02Z", "generation": 1, "labels": {"app.kubernetes.io/managed-by": "<PERSON><PERSON>"}, "name": "keycloak-block-admin-access-from-public-gateway", "namespace": "keycloak", "resourceVersion": "1656", "uid": "cda7ec8f-f7b7-4873-a1e2-5c6ec722f7e4"}, "spec": {"action": "DENY", "rules": [{"from": [{"source": {"notNamespaces": ["istio-admin-gateway"]}}], "to": [{"operation": {"paths": ["/admin*", "/realms/master*"]}}]}, {"from": [{"source": {"notNamespaces": ["istio-admin-gateway", "monitoring"]}}], "to": [{"operation": {"paths": ["/metrics*"]}}]}, {"from": [{"source": {"notNamespaces": ["pepr-system"]}}], "to": [{"operation": {"paths": ["/realms/uds/clients-registrations/*"]}}]}], "selector": {"matchLabels": {"app.kubernetes.io/instance": "keycloak", "app.kubernetes.io/name": "keycloak"}}}}, {"apiVersion": "security.istio.io/v1beta1", "kind": "AuthorizationPolicy", "metadata": {"annotations": {"meta.helm.sh/release-name": "authservice", "meta.helm.sh/release-namespace": "authservice"}, "creationTimestamp": "2024-06-07T14:42:13Z", "generation": 1, "labels": {"app.kubernetes.io/managed-by": "<PERSON><PERSON>"}, "name": "jwt-authz", "namespace": "istio-system", "resourceVersion": "3764", "uid": "de7038cb-62fc-449e-b846-abab3432e39e"}, "spec": {"rules": [{"from": [{"source": {"requestPrincipals": ["https://login.uds.dev/auth/realms/doug/*"]}}]}], "selector": {"matchLabels": {"protect": "keycloak"}}}}, {"apiVersion": "security.istio.io/v1beta1", "kind": "AuthorizationPolicy", "metadata": {"annotations": {"meta.helm.sh/release-name": "authservice", "meta.helm.sh/release-namespace": "authservice"}, "creationTimestamp": "2024-06-07T14:42:13Z", "generation": 1, "labels": {"app.kubernetes.io/managed-by": "<PERSON><PERSON>"}, "name": "authservice", "namespace": "istio-system", "resourceVersion": "3767", "uid": "5ca626af-4cd3-4652-90d5-3ab8b0d5d72c"}, "spec": {"action": "CUSTOM", "provider": {"name": "authservice"}, "rules": [{"when": [{"key": "request.headers[authorization]", "notValues": ["*"]}]}], "selector": {"matchLabels": {"protect": "keycloak"}}}}]}