# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: uds-core-prometheus-stack
  description: "UDS Core Prometheus-Stack"

components:
  - name: prometheus-operator-crds
    required: true
    description: "Install kube-prometheus-stack operator crds using the helm chart https://github.com/prometheus-community/helm-charts/tree/main/charts/prometheus-operator-crds"
    charts:
      - name: prometheus-operator-crds
        url: https://prometheus-community.github.io/helm-charts
        version: 21.0.0
        namespace: uds-crds
        valuesFiles:
          - "values/crd-values.yaml"

  - name: kube-prometheus-stack
    required: true
    description: "Install kube-prometheus-stack using the helm chart https://github.com/prometheus-community/helm-charts/tree/main/charts/kube-prometheus-stack"
    only:
      flavor: upstream
    import:
      path: common
    charts:
      - name: kube-prometheus-stack
        valuesFiles:
          - "values/upstream-values.yaml"
    images:
      - "quay.io/prometheus/node-exporter:v1.9.1"
      - "quay.io/prometheus-operator/prometheus-operator:v0.83.0"
      - "registry.k8s.io/kube-state-metrics/kube-state-metrics:v2.15.0"
      - "quay.io/prometheus/alertmanager:v0.28.1"
      - "quay.io/prometheus-operator/prometheus-config-reloader:v0.83.0"
      - "quay.io/prometheus/prometheus:v3.4.1"
      - "registry.k8s.io/ingress-nginx/kube-webhook-certgen:v1.5.4"

  - name: kube-prometheus-stack
    required: true
    description: "Install kube-prometheus-stack using the helm chart https://github.com/prometheus-community/helm-charts/tree/main/charts/kube-prometheus-stack"
    only:
      flavor: registry1
    import:
      path: common
    charts:
      - name: kube-prometheus-stack
        valuesFiles:
          - "values/registry1-values.yaml"
    images:
      - "registry1.dso.mil/ironbank/opensource/prometheus/node-exporter:v1.9.1"
      - "registry1.dso.mil/ironbank/opensource/prometheus-operator/prometheus-operator:v0.83.0"
      - "registry1.dso.mil/ironbank/opensource/kubernetes/kube-state-metrics:v2.15.0"
      - "registry1.dso.mil/ironbank/opensource/prometheus/alertmanager:v0.28.1"
      - "registry1.dso.mil/ironbank/opensource/prometheus-operator/prometheus-config-reloader:v0.83.0"
      - "registry1.dso.mil/ironbank/opensource/prometheus/prometheus:v3.4.1"
      - "registry1.dso.mil/ironbank/opensource/ingress-nginx/kube-webhook-certgen:v1.5.4"

  - name: kube-prometheus-stack
    required: true
    description: "Install kube-prometheus-stack using the helm chart https://github.com/prometheus-community/helm-charts/tree/main/charts/kube-prometheus-stack"
    only:
      flavor: unicorn
    import:
      path: common
    charts:
      - name: kube-prometheus-stack
        valuesFiles:
          - "values/unicorn-values.yaml"
    images:
      - "quay.io/rfcurated/prometheus/node-exporter:1.9.1-jammy-scratch-fips-rfcurated"
      - "quay.io/rfcurated/prometheus-operator:0.83.0-jammy-scratch-fips-rfcurated"
      - "quay.io/rfcurated/kube-state-metrics:2.15.0-jammy-scratch-fips-rfcurated-rfhardened"
      - "quay.io/rfcurated/prometheus/alertmanager:0.28.1-jammy-fips-rfcurated-rfhardened"
      - "quay.io/rfcurated/prometheus-operator/prometheus-config-reloader:0.83.0-jammy-scratch-fips-rfcurated"
      - "quay.io/rfcurated/prometheus:3.4.1-jammy-fips-rfcurated-rfhardened"
      - "quay.io/rfcurated/ingress-nginx/kube-webhook-certgen:1.5.4-jammy-fips-rfcurated-rfhardened"
