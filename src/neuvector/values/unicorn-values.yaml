# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

# Generate certs missing from unicorn images
autoGenerateCert: true

registry: quay.io
# renovate: datasource=docker depName=quay.io/rfcurated/neuvector/controller versioning=docker
tag: 5.4.4-jammy-scratch-fips-rfcurated

manager:
  image:
    repository: rfcurated/neuvector/manager

enforcer:
  image:
    repository: rfcurated/neuvector/enforcer
  containerSecurityContext:
    privileged: true

controller:
  image:
    repository: rfcurated/neuvector/controller

cve:
  scanner:
    image:
      repository: rfcurated/neuvector/scanner
      tag: latest-jammy-scratch-fips-rfcurated
    volumes:
      - name: internal-cert-dir
        emptyDir:
          sizeLimit: 50Mi
    volumeMounts:
      - mountPath: /etc/neuvector/certs/internal/
        name: internal-cert-dir
  updater:
    enabled: true
    image:
      repository: rfcurated/curl
      # renovate: datasource=docker depName=quay.io/rfcurated/curl versioning=docker
      tag: 8.14.1-jammy-scratch-fips-rfcurated
