# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

pass:
  - test: default
    validation: validation.yaml
    resources: resources.json
    expected-validation: true
  - test: remove-auth-rules
    validation: validation.yaml
    resources: resources.json
    permutation: "del(.authorizationPolicy.spec.rules)"
    expected-validation: false
  - test: add-not-namespaces
    validation: validation.yaml
    resources: resources.json
    permutation: '(.authorizationPolicy.spec.rules[] | .from[] | .source.notNamespaces) |= . + ["test-ns"]'
    expected-validation: false
