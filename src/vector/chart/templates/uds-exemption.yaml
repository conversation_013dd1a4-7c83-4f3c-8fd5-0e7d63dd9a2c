# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: uds.dev/v1alpha1
kind: Exemption
metadata:
  name: vector
  namespace: uds-policy-exemptions
spec:
  exemptions:
    - policies:
        - RequireNonRootUser
        - RestrictSELinuxType
        - RestrictHostPathWrite
        - RestrictVolumeTypes
      matcher:
        namespace: vector
        name: "^vector-.*"
      title: "vector exemptions"
      description: "Vector mounts the following hostPaths:
        - `/var/log`: to tail logs
        - `/var/lib/vector`: for Vector's buffering and persistent state
        Since logs can have sensitive information, it is better to exclude
        <PERSON><PERSON> from the policy than add the paths as allowable mounts"
