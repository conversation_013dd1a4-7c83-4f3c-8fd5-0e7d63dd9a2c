# Probably set up by the chart/bundle?
apiVersion: v1
kind: Namespace
metadata:
  name: istio-egress
  labels:
    istio.io/dataplane-mode: ambient
    istio.io/use-waypoint: waypoint
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: egress-waypoint
  namespace: istio-egress
  labels:
    istio.io/waypoint-for: all
spec:
  gatewayClassName: istio-waypoint
  listeners:
    - name: mesh
      port: 15008
      protocol: HBONE
      allowedRoutes:
        namespaces:
          from: All
        kinds:
          - group: "networking.istio.io"
            kind: ServiceEntry
  # infrastructure:
  #   parametersRef:
  #     group: ""
  #     kind: ConfigMap
  #     name: gw-options
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: gw-options
  namespace: istio-egress
data:
  deployment: |
    spec:
      replicas: 4
      template:
        spec:
          containers:
          - name: istio-proxy
            resources:
              requests:
                cpu: 1234m
