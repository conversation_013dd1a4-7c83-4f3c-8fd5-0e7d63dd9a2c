# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

pass:
  - test: default
    validation: validation.yaml
    resources: resources.json
    expected-validation: true
  - test: grafana-pods-missing-annotations
    validation: validation.yaml
    resources: resources.json
    permutation: '.pods |= map(if .metadata.namespace == "grafana" then .metadata.annotations["prometheus.io/scrape"] = false else . end)'
    expected-validation: false
