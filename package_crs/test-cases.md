# Test Cases

1) Add Single Package / Remove Single Package

Apply `single-pkg.yaml`

-> Check: istioctl analyze (pkg ns and istio-egress-gateway) reports no issues
```sh
% istioctl analyze -n ns-1

✔ No validation issues found when analyzing namespace: ns-1.

% istioctl analyze -n istio-egress-gateway
Info [IST0102] (Namespace istio-egress-gateway) The namespace is not enabled for Istio injection...
```

-> Check: all resources are created
```sh
% k get gateway,destinationrule,vs -n istio-egress-gateway
NAME                                              AGE
gateway.networking.istio.io/gateway-example-com   12s

NAME                                                                 HOST                                                   AGE
destinationrule.networking.istio.io/egressgateway-destination-rule   egressgateway.istio-egress-gateway.svc.cluster.local   12s

NAME                                                       GATEWAYS                         HOSTS             AGE
virtualservice.networking.istio.io/egress-vs-example-com   ["mesh","gateway-example-com"]   ["example.com"]   12s

% k get serviceentry,sidecar -n ns-1
NAME                                                                HOSTS             LOCATION        RESOLUTION   AGE
serviceentry.networking.istio.io/pkg-1-egress-tls-443-example-com   ["example.com"]   MESH_EXTERNAL   DNS          16s

NAME                                                           AGE
sidecar.networking.istio.io/pkg-1-egress-tls-443-example-com   16s
```

-> Check: curl pod can access the host, and not another
```sh
/home/<USER>//example.com
<!doctype html>
...

/home/<USER>//httpbin.org/headers
curl: (35) Recv failure: Connection reset by peer
```

* Delete the package
-> Check: all resources are removed

```sh
% k get gateway,destinationrule,vs -n istio-egress-gateway
No resources found in istio-egress-gateway namespace.

% k get serviceentry,sidecar -n ns-1
No resources found in ns-1 namespace.
```

1a) Try changing the package spec, e.g., the allow ports, and reapply

Apply `single-pkg.yaml`


1b) Try changing the package spec, e.g., the allow host, and reapply


1c) Try changing the package spec, e.g., the selector, and reapply

2) Add Two Packages with the same Host / Remove Single Package
-> Check: istioctl analyze reports no issues
-> Check: Shared resources are created with the right annotations, only the annotations removed for removed package

3) Categorize errors when port is desired but not exposed via the gateway
* Apply `single-pkg-no-port.yaml`
-> Check: istioctl analyze reports some issues <insert here>