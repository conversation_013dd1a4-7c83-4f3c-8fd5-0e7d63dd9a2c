# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

metadata:
  name: istio-authorization-policies-require-authentication
  uuid: e38c0695-10f6-40b6-b246-fa58b26ccd25
domain:
  type: kubernetes
  kubernetes-spec:
    resources:
      - name: authorizationPolicy
        resource-rule:
          name: jwt-authz
          group: security.istio.io
          namespaces: ["istio-system"]
          resource: authorizationpolicies
          version: v1beta1
provider:
  type: opa
  opa-spec:
    output:
      observations:
        - validate.msg
      validation: validate.validate
    rego: |
      package validate

      # Default policy result
      default validate = false
      default msg = "Authorization Policies do not require authentication"

      # Evaluation for Istio Authorization Policies
      validate {
        result_auth_policy.result
      }

      msg = result_auth_policy.msg

      result_auth_policy = {"result": true, "msg": msg} {
        # Check that authorization policy exists and require authentication
        input.authorizationPolicy.kind == "AuthorizationPolicy"

        # "require authentication" is defined as having requestPrincipals defined
        #   and the selector.protect label is set to "keycloak"
        input.authorizationPolicy.spec.rules[_].from[_].source.requestPrincipals != null
        input.authorizationPolicy.spec.selector.matchLabels.protect == "keycloak"
        msg := "Authorization Policy requires authentication for keycloak"
      } else = {"result": false, "msg": msg} {
        msg := "Authorization Policy does not require authentication"
      }
