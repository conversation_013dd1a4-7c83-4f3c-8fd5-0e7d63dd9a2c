# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: uds-core-test-apps
  description: "UDS Core Common test apps"
  url: https://github.com/defenseunicorns/uds-core

components:
  # Deploy package CRs
  - name: test-app-packages
    required: true
    manifests:
      - name: test-app-packages
        files:
          - "app-admin-package.yaml"
          - "app-authservice-tenant-package.yaml"
          - "app-tenant-package.yaml"
          - "app-curl-packages.yaml"

  # This is excluded from slim-dev to save some resources in package CI
  - name: egress-app
    required: false
    manifests:
      - name: egress-pkg
        files:
          - "app-egress-package.yaml"
      - name: egress-app
        files:
          - "app-egress.yaml"
    images:
      - curlimages/curl:latest

  # Deploy test workloads
  - name: test-apps
    required: true
    manifests:
      - name: app-admin
        files:
          - "app-admin.yaml"
      - name: app-tenant
        files:
          - "app-tenant.yaml"
      - name: app-authservice-tenant
        files:
          - "app-authservice-tenant.yaml"
      - name: curl-test
        files:
          - "app-curl.yaml"
    images:
      - docker.io/kong/httpbin:0.2.3
      - hashicorp/http-echo:latest
      - curlimages/curl:latest

  - name: podinfo
    required: true
    charts:
      - name: uds-podinfo-config
        namespace: podinfo
        localPath: ./chart
        version: 0.1.0
      - name: podinfo
        version: 6.9.0
        namespace: podinfo
        url: https://github.com/stefanprodan/podinfo.git
        gitPath: charts/podinfo
        valuesFiles:
          - ./podinfo-values.yaml
    images:
      - ghcr.io/stefanprodan/podinfo:6.9.0
