# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

{{- $tls := .Values.tls }}
{{ if and $tls.cert (not $tls.credentialName) }}
apiVersion: v1
kind: Secret
metadata:
  name: gateway-tls
  namespace: {{ .Release.Namespace }}
data:
  tls.crt: {{ $tls.cert }}
  tls.key: {{ $tls.key }}
  cacert: {{ $tls.cacert }}
type: kubernetes.io/tls
---
{{ end }}


{{ range $name,$server := .Values.tls.servers }}
{{ if and ($server.tls).cert (not ($server.tls).credentialName) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{$name}}-tls
  namespace: {{ $.Release.Namespace }}
data:
  tls.crt: {{ $server.tls.cert }}
  tls.key: {{ $server.tls.key }}
  cacert: {{ $server.tls.cacert }}
type: kubernetes.io/tls
---
{{ end }}
{{ end }}
