# Copyright 2025 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: RKE2 Test

on:
  schedule:
    - cron: "0 0 * * 0" # Every Sunday Midnight (UTC) / Saturday 5pm MT
  workflow_call: {}

permissions:
  id-token: write
  contents: read
  packages: read

jobs:
  test-rke2-install:
    strategy:
      fail-fast: false
      matrix:
        flavor: [upstream, registry1, unicorn]
    runs-on: ubuntu-latest
    name: test-rke2
    permissions:
      id-token: write
      contents: read
      packages: read
      pull-requests: write # Allows writing to pull requests (needed for renovate-readiness)
    env:
      SHA: ${{ github.sha }}
      UDS_REGION: us-gov-west-1
      UDS_PERMISSIONS_BOUNDARY_ARN: ${{ secrets.GOV_PERMISSIONS_BOUNDARY_ARN }}
      UDS_PERMISSIONS_BOUNDARY_NAME: ${{ secrets.PERMISSIONS_BOUNDARY_NAME }}
      # UDS_STATE_BUCKET_NAME: uds-tf-state-20250305185900362500000001 # commercial
      UDS_STATE_BUCKET_NAME: uds-tf-state-20250305185903808500000001 # govcloud
      UDS_STATE_DYNAMODB_TABLE_NAME: uds-tf-state-lock
      UDS_IMAGES_AWS_ACCOUNT_ID: ${{ secrets.GOV_UDS_IMAGES_AWS_ACCOUNT_ID }}
    steps:
      - name: Set ENV
        run: |
          echo "UDS_CLUSTER_NAME=uds-ci-${{ matrix.flavor }}-${SHA:0:7}" >> $GITHUB_ENV
          echo "UDS_STATE_KEY="tfstate/ci/install/${SHA:0:7}-rke2-core-${{ matrix.flavor }}-aws.tfstate >> $GITHUB_ENV
          echo "TF_VAR_region=${UDS_REGION}" >> $GITHUB_ENV
          echo "TF_VAR_name=uds-ci-${{ matrix.flavor }}-${SHA:0:7}" >> $GITHUB_ENV
          echo "TF_VAR_run_id=$GITHUB_RUN_ID" >> $GITHUB_ENV
          echo "TF_VAR_use_permissions_boundary=true" >> $GITHUB_ENV
          echo "TF_VAR_permissions_boundary_name=${UDS_PERMISSIONS_BOUNDARY_NAME}" >> $GITHUB_ENV
          echo "TF_VAR_uds_images_aws_account_id=${UDS_IMAGES_AWS_ACCOUNT_ID}" >> $GITHUB_ENV

      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4

      - name: Check renovate readiness
        if: startsWith(github.event.pull_request.head.ref, 'renovate/') # Only call for Renovate PRs
        uses: ./.github/actions/renovate-readiness
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@e3dd6a429d7300a6a4c196c26e071d42e0343502 # v4
        with:
          role-to-assume: ${{ secrets.AWS_GOVCLOUD_ROLE_TO_ASSUME }}
          role-session-name: ${{ github.job || github.event.client_payload.pull_request.head.sha || github.sha }}
          aws-region: ${{ env.UDS_REGION }}
          role-duration-seconds: 7200

      - name: Environment setup
        uses: ./.github/actions/setup
        with:
          registry1Username: ${{ secrets.IRON_BANK_ROBOT_USERNAME }}
          registry1Password: ${{ secrets.IRON_BANK_ROBOT_PASSWORD }}
          ghToken: ${{ secrets.GITHUB_TOKEN }}
          rapidfortUsername: ${{ secrets.RAPIDFORT_USERNAME }}
          rapidfortPassword: ${{ secrets.RAPIDFORT_PASSWORD }}

      - name: Setup Tofu
        uses: opentofu/setup-opentofu@592200bd4b9bbf4772ace78f887668b1aee8f716 # v1.0.5
        with:
          tofu_version: 1.8.2
          tofu_wrapper: false

      - name: Create UDS Core Package
        run: ZARF_ARCHITECTURE=amd64 uds run -f tasks/create.yaml standard-package --no-progress --with create_options="--skip-sbom" --set FLAVOR=${{ matrix.flavor }}

      - name: Create Core Bundle
        run: uds create .github/bundles/rke2 --confirm

      - name: Create IAC
        run: uds run -f tasks/iac.yaml create-iac --no-progress --set K8S_DISTRO=rke2 --set CLOUD=aws
        timeout-minutes: 20

      - name: Get Kubeconfig
        run: uds run -f tasks/iac.yaml rke2-get-kubeconfig --no-progress
        timeout-minutes: 20

      - name: Wait for RKE2 Cluster
        run: uds run -f tasks/iac.yaml rke2-cluster-ready --no-progress
        timeout-minutes: 20

      - name: Deploy Core Bundle
        env:
          UDS_CONFIG: .github/bundles/rke2/uds-config.yaml
        run: uds deploy .github/bundles/rke2/uds-bundle-uds-core-rke2-nightly-*.tar.zst --confirm
        timeout-minutes: 40

      - name: Test UDS Core
        run: uds run -f tasks/test.yaml uds-core-non-k3d

      - name: Debug Output
        if: ${{ always() }}
        uses: ./.github/actions/debug-output

      - name: Save logs
        if: always()
        uses: ./.github/actions/save-logs
        with:
          suffix: -rke2-${{ matrix.flavor }}
          distro: "rke2"

      - name: Remove UDS Core
        if: always()
        run: uds remove .github/bundles/rke2/uds-bundle-uds-core-rke2-*.tar.zst --confirm
        timeout-minutes: 20
        continue-on-error: true

      - name: Remove IAC
        if: always()
        run: uds run -f tasks/iac.yaml destroy-iac --no-progress --set K8S_DISTRO=rke2 --set CLOUD=aws
        timeout-minutes: 10
        continue-on-error: true
