# Add ambient and use-waypoint labels to namespace
# k label ns istio-egress-gateway istio.io/dataplane-mode=ambient istio.io/use-waypoint=egress-waypoint
#
# Apply the waypoint to istio-egress-gateway
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: egress-waypoint
  namespace: istio-egress-gateway
  labels:
    istio.io/waypoint-for: all
spec:
  gatewayClassName: istio-waypoint
  listeners:
    - name: mesh
      port: 15008
      protocol: HBONE
      allowedRoutes:
        namespaces:
          from: All
        kinds:
          - group: "networking.istio.io"
            kind: ServiceEntry

# Test 1 - without ns labels
# Test 2 - with ns labels