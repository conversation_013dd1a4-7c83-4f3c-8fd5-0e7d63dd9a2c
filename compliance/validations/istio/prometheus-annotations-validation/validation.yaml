# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

metadata:
  name: istio-prometheus-annotations-validation
  uuid: f345c359-3208-46fb-9348-959bd628301e
domain:
  type: kubernetes
  kubernetes-spec:
    resources:
      - name: pods
        resource-rule:
          resource: pods
          version: v1
          namespaces: []
provider:
  type: opa
  opa-spec:
    rego: |
      package validate
      import rego.v1

      # Default values
      default validate := false
      default msg := "Not evaluated"

      # Check for required Istio and Prometheus annotations
      validate if {
        has_prometheus_annotation.result
      }

      msg = has_prometheus_annotation.msg
      msg_exempted_namespaces = concat(", ", exempt_namespaces)

      # Check for prometheus annotations in pod spec
      no_annotation = [sprintf("%s/%s", [pod.metadata.namespace, pod.metadata.name]) | pod := input.pods[_]; not contains_annotation(pod); not is_exempt(pod)]

      has_prometheus_annotation = {"result": true, "msg": msg} if {
        count(no_annotation) == 0
          msg := "All pods have correct prometheus annotations."
      } else = {"result": false, "msg": msg} if {
        msg := sprintf("Prometheus annotations not found in pods: %s.", [concat(", ", no_annotation)])
      }

      contains_annotation(pod) if {
        annotations := pod.metadata.annotations
        annotations["prometheus.io/scrape"] == "true"
        annotations["prometheus.io/path"] != ""
        annotations["prometheus.io/port"] == "15020"
      }

      # Exemptions
      exempt_namespaces = {"kube-system", "istio-system", "uds-dev-stack", "zarf"}

      is_exempt(pod) if {
          pod.metadata.namespace in exempt_namespaces
      }

    output:
      validation: validate.validate
      observations:
        - validate.msg
        - validate.msg_exempted_namespaces
