# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: pepr-uds-core
  description: "Pepr Module: A collection of capabilities for UDS Core"
  url: https://github.com/defenseunicorns/pepr

variables:
  - name: DOMAIN
    description: "Cluster domain"
    default: "uds.dev"

  - name: ADMIN_DOMAIN
    description: "Domain for admin services, defaults to `admin.DOMAIN`"

  - name: CA_CERT
    description: "Base64 encoded CA cert that signed the domain wildcard certs used for Istio ingress"
    default: ""

  - name: UDS_LOG_LEVEL
    description: "UDS Operator log level"
    default: "debug"

  - name: AUTHSERVICE_REDIS_URI
    description: "UDS Authservice Redis URI"
    default: ""

  - name: PEPR_SERVICE_MONITORS
    description: "Enables Service Monitors for Pepr services (watcher, admission)"
    default: "true"

components:
  - name: uds-operator-config
    required: true
    charts:
      - name: uds-operator-config
        namespace: pepr-system
        version: 0.1.0
        localPath: uds-operator-config
        valuesFiles:
          - uds-operator-config/values.yaml

  - name: pepr-uds-core
    required: true
    import:
      name: module
      path: ../../dist
    charts:
      - name: module
        valuesFiles:
          - values.yaml
