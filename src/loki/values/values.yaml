# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

# Sets the global DNS service to the service created in this chart
global:
  dnsService: "uds-loki-dns"

# -- Overrides the chart's name
nameOverride: loki
# -- Overrides the chart's computed fullname
fullnameOverride: loki
# -- Overrides the chart's cluster label
clusterLabelOverride: null

chunksCache:
  enabled: false

resultsCache:
  enabled: false

loki:
  configStorageType: Secret
  # Disable telemetry that doesn't function in the airgap
  analytics:
    reporting_enabled: false
  storage:
    bucketNames:
      chunks: uds
      ruler: uds
      admin: uds
    type: s3
    s3:
      endpoint: http://minio.uds-dev-stack.svc.cluster.local:9000
      secretAccessKey: uds-secret
      accessKeyId: uds
      s3ForcePathStyle: true
      insecure: false
  commonConfig:
    replication_factor: 1
  schemaConfig:
    configs:
      - from: 2022-01-11
        store: boltdb-shipper
        object_store: "{{ .Values.loki.storage.type }}"
        schema: v12
        index:
          prefix: loki_index_
          period: 24h
      - from: "{{- $secret := lookup \"v1\" \"Secret\" \"loki\" \"loki\" -}}
          {{- $pastDate := now | dateModify \"-48h\" | date \"2006-01-02\" -}}
          {{- $futureDate := now | dateModify \"+48h\" | date \"2006-01-02\" -}}
          {{- $result := $pastDate -}}
          {{- if $secret -}}
            {{- $result = $futureDate -}}
            {{- if (index $secret.data \"config.yaml\") -}}
              {{- $configYAML := (index $secret.data \"config.yaml\" | b64dec | fromYaml) -}}
              {{- range $configYAML.schema_config.configs -}}
                {{- if and (eq .store \"tsdb\") (eq .schema \"v13\") -}}
                  {{- $result = .from -}}
                  {{- break -}}
                {{- end -}}
              {{- end -}}
            {{- end -}}
          {{- end -}}
          {{- $result -}}"
        store: tsdb
        object_store: "{{ .Values.loki.storage.type }}"
        schema: v13
        index:
          prefix: loki_index_
          period: 24h
  limits_config:
    split_queries_by_interval: "30m"
    allow_structured_metadata: false
  query_scheduler:
    max_outstanding_requests_per_tenant: 32000 # This is the default in Loki 3.0
  extraMemberlistConfig:
    rejoin_interval: 120s
  auth_enabled: false
  # -- Additional storage config
  storage_config:
    boltdb_shipper:
      active_index_directory: /var/loki/boltdb-shipper-active
      cache_location: /var/loki/boltdb-shipper-cache
      cache_ttl: 24h
    tsdb_shipper:
      active_index_directory: /var/loki/tsdb-index
      cache_location: /var/loki/tsdb-cache
      cache_ttl: 24h
    hedging:
      at: "250ms"
      max_per_second: 20
      up_to: 3
  # -- The SecurityContext for Loki containers
  containerSecurityContext:
    readOnlyRootFilesystem: true
    capabilities:
      drop:
        - ALL
    allowPrivilegeEscalation: false
enterprise:
  # Enable enterprise features, license must be provided
  enabled: false
# RBAC configuration
rbac:
  # -- If pspEnabled true, a PodSecurityPolicy is created for K8s that use psp.
  pspEnabled: false
# -- Section for configuring optional Helm test
test:
  enabled: false

deploymentMode: SimpleScalable

# Configuration for the single binary node(s)
singleBinary:
  # -- Number of replicas for the single binary
  replicas: 0
  # -- Resource requests and limits for the single binary
  resources:
    limits:
      cpu: 100m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 256Mi
  persistence:
    # -- Enable StatefulSetAutoDeletePVC feature
    enableStatefulSetAutoDeletePVC: false
    # -- Enable persistent disk
    enabled: true
    # -- Size of persistent disk
    size: 12Gi
minio:
  # -- Enable minio instance support, must have minio-operator installed
  enabled: false
sidecar:
  resources:
    limits:
      cpu: 100m
      memory: 100Mi
    requests:
      cpu: 100m
      memory: 100Mi
  securityContext:
    allowPrivilegeEscalation: false
    capabilities:
      drop:
        - ALL
    seccompProfile:
      type: RuntimeDefault
  rules:
    # -- Whether or not to create a sidecar to ingest rule from specific ConfigMaps and/or Secrets.
    enabled: false

memcachedExporter:
  # -- Whether memcached metrics should be exported
  enabled: false

monitoring:
  serviceMonitor:
    enabled: true

lokiCanary:
  enabled: false

gateway:
  enabled: true
  # Remove default anti-affinity to support single node
  affinity: null

  # Gateway has no metrics https://github.com/grafana/loki/issues/13201
  service:
    labels:
      prometheus.io/service-monitor: "false"

read:
  # Remove default anti-affinity to support single node
  affinity: null

write:
  # Remove default anti-affinity to support single node
  affinity: null

backend:
  # Remove default anti-affinity to support single node
  affinity: null
  # Temporary label to trigger mutation for istio service patch
  service:
    labels:
      uds/istio-patch: "true"
