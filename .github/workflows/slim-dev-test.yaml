# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: Slim Dev

# This workflow is triggered on pull requests
on:
  pull_request:
    # milestoned is added here as a workaround for release-please not triggering PR workflows (PRs should be added to a milestone to trigger the workflow).
    # labeled is added to support renovate-ready labelling on PRs
    types: [milestoned, labeled, opened, reopened, synchronize]
    paths:
      - src/pepr/**
      - src/keycloak/**
      - src/authservice/**
      - src/istio/**
      - src/prometheus-stack/**
      - packages/base/**
      - packages/identity-authorization/**
      - bundles/k3d-slim-dev/**
      - .github/workflows/slim-dev**
      - tasks/test.yaml
      - "!**/*.md"
      - "!**.jpg"
      - "!**.png"
      - "!**.gif"
      - "!**.svg"

# Permissions for the GITHUB_TOKEN used by the workflow.
permissions:
  id-token: write # Needed for OIDC-related operations.
  contents: read # Allows reading the content of the repository.

# Default settings for all run commands in the workflow jobs.
defaults:
  run:
    shell: bash -e -o pipefail {0} # Ensures that scripts fail on error and pipefail is set.

# Abort prior jobs in the same workflow / PR
concurrency:
  group: test-slim-dev-${{ github.ref }}
  cancel-in-progress: true

jobs:
  lint-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: lint-check
        uses: ./.github/actions/lint-check

  autogenerated-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: autogenerated-check
        uses: ./.github/actions/autogenerated-check

  # This job runs the slim-dev bundle create/deploy process.
  test:
    name: Test
    runs-on: ubuntu-latest
    needs: [lint-check, autogenerated-check]
    permissions:
      id-token: write # Needed for OIDC-related operations.
      contents: read # Allows reading the content of the repository.
      pull-requests: write # Allows writing to pull requests (needed for renovate-readiness)
    steps:
      - name: Checkout the code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Check renovate readiness
        if: startsWith(github.event.pull_request.head.ref, 'renovate/') # Only call for Renovate PRs
        uses: ./.github/actions/renovate-readiness
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
      - name: Environment setup
        uses: ./.github/actions/setup
        with:
          registry1Username: ${{ secrets.IRON_BANK_ROBOT_USERNAME }}
          registry1Password: ${{ secrets.IRON_BANK_ROBOT_PASSWORD }}
          ghToken: ${{ secrets.GITHUB_TOKEN }}
          rapidfortUsername: ${{ secrets.RAPIDFORT_USERNAME }}
          rapidfortPassword: ${{ secrets.RAPIDFORT_PASSWORD }}
      - name: Deploy Slim Dev Bundle
        run: uds run slim-dev --no-progress
      - name: Test Slim Dev Bundle
        run: uds run test:slim-dev --no-progress
      - name: Debug Output
        if: ${{ always() }}
        uses: ./.github/actions/debug-output
      - name: Save logs
        if: always()
        uses: ./.github/actions/save-logs
        with:
          suffix: -slim-dev
