# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: Test Shim

# Permissions for the GITHUB_TOKEN used by the workflow.
permissions:
  contents: read # Allows reading the content of the repository.

on:
  # Manual trigger
  workflow_dispatch:
    inputs:
      package:
        type: string
        description: "The name of the source package to test"
        required: true
      flavor:
        type: string
        description: "Flavor of the source package to test"
        required: true
      test_type:
        type: string
        description: "The type of test to perform"
        required: true
  workflow_call:
    inputs:
      package:
        type: string
        description: "The name of the source package to test"
        required: true
      flavor:
        type: string
        description: "Flavor of the source package to test"
        required: true
      test_type:
        type: string
        description: "The type of test to perform"
        required: true

jobs:
  test:
    runs-on: 'ubuntu-latest'
    name: Test
    steps:
      - name: Skipped
        run: |
          echo skipped
