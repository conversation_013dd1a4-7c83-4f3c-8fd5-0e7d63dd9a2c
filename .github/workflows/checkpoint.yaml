# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: Checkpoint UDS Core

# Permissions for the GITHUB_TOKEN used by the workflow.
permissions:
  contents: read # Allows reading the content of the repository.
  packages: read # Allows reading the content of the repository's packages.
  id-token: write

on:
  pull_request:
    # milestoned is added here as a workaround for release-please not triggering PR workflows (PRs should be added to a milestone to trigger the workflow).
    # labeled is added to support renovate-ready labelling on PRs
    types: [milestoned, labeled, opened, reopened, synchronize]
    paths:
      - packages/checkpoint-dev/**
      - .github/workflows/checkpoint**
      - tasks/test.yaml
      - "!**/*.md"
      - "!**.jpg"
      - "!**.png"
      - "!**.gif"
      - "!**.svg"
  # triggered by tag-and-release.yaml
  workflow_call:

jobs:
  checkpoint:
    strategy:
      matrix:
        architecture: [amd64, arm64]
    runs-on: ${{ matrix.architecture == 'arm64' && 'uds-ubuntu-24-arm64-4-core' || 'uds-ubuntu-big-boy-4-core' }}
    name: UDS Core Checkpoint

    permissions:
      contents: read
      packages: write
      id-token: write # This is needed for OIDC federation.
      pull-requests: write # Allows writing to pull requests (needed for renovate-readiness)

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Check renovate readiness
        if: startsWith(github.event.pull_request.head.ref, 'renovate/') # Only call for Renovate PRs
        uses: ./.github/actions/renovate-readiness
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Environment setup
        uses: ./.github/actions/setup
        with:
          registry1Username: ${{ secrets.IRON_BANK_ROBOT_USERNAME }}
          registry1Password: ${{ secrets.IRON_BANK_ROBOT_PASSWORD }}
          ghToken: ${{ secrets.GITHUB_TOKEN }}
          rapidfortUsername: ${{ secrets.RAPIDFORT_USERNAME }}
          rapidfortPassword: ${{ secrets.RAPIDFORT_PASSWORD }}

      - name: Deploy K3d + Latest UDS Core Slim Bundle
        if: ${{ github.event_name != 'pull_request' }}
        run: |
          uds run -f tasks/deploy.yaml latest-slim-bundle-release --no-progress

      - name: Deploy K3d + Source UDS Core Slim Bundle
        if: ${{ github.event_name == 'pull_request' }}
        run: |
          uds run slim-dev --no-progress

      - name: Create Checkpoint Package
        run: |
          uds run -f tasks/create.yaml checkpoint-dev-package --no-progress

      - name: Deploy Checkpoint Package
        run: |
          uds run -f tasks/deploy.yaml checkpoint-package --no-progress

      - name: Test Checkpoint Package
        continue-on-error: ${{ matrix.architecture == 'arm64' }}
        run: |
          npm ci
          uds run test:slim-dev --no-progress

      - name: Debug Output
        if: always()
        uses: ./.github/actions/debug-output

      - name: Publish Checkpoint Package
        if: ${{ github.event_name != 'pull_request' }}
        run: uds run -f tasks/publish.yaml checkpoint-package --no-progress

      - name: Save logs
        if: always()
        uses: ./.github/actions/save-logs
        with:
          suffix: -${{ matrix.architecture }}
