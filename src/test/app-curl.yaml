# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: v1
kind: Service
metadata:
  name: curl-pkg-deny-all-1
  namespace: curl-ns-deny-all-1
  labels:
    name: curl-pkg-deny-all-1
    namespace: curl-ns-deny-all-1
spec:
  ports:
    - name: port8080
      port: 8080
      targetPort: 8080
  selector:
    app: curl-pkg-deny-all-1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl-pkg-deny-all-1
  namespace: curl-ns-deny-all-1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl-pkg-deny-all-1
  template:
    metadata:
      labels:
        app: curl-pkg-deny-all-1
      annotations:
        sidecar.istio.io/proxyCPU: "10m"
        sidecar.istio.io/proxyMemory: "16Mi"
    spec:
      containers:
        - name: curl-pkg-deny-all-1
          image: curlimages/curl
          imagePullPolicy: IfNotPresent
          command: ["sleep", "3600"]
          resources:
            limits:
              cpu: 50m
              memory: 64Mi
            requests:
              cpu: 10m
              memory: 16Mi
          ports:
            - containerPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: curl-pkg-deny-all-2
  namespace: curl-ns-deny-all-2
  labels:
    name: curl-pkg-deny-all-2
    namespace: curl-ns-deny-all-2
spec:
  ports:
    - name: port8080
      port: 8080
      targetPort: 8080
  selector:
    app: curl-pkg-deny-all-2
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl-pkg-deny-all-2
  namespace: curl-ns-deny-all-2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl-pkg-deny-all-2
  template:
    metadata:
      labels:
        app: curl-pkg-deny-all-2
      annotations:
        sidecar.istio.io/proxyCPU: "10m"
        sidecar.istio.io/proxyMemory: "16Mi"
    spec:
      containers:
        - name: curl-pkg-deny-all-2
          image: curlimages/curl
          imagePullPolicy: IfNotPresent
          command: ["sleep", "3600"]
          resources:
            limits:
              cpu: 50m
              memory: 64Mi
            requests:
              cpu: 10m
              memory: 16Mi
          ports:
            - containerPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: curl-pkg-allow-all
  namespace: curl-ns-allow-all
  labels:
    name: curl-pkg-allow-all
    namespace: curl-ns-allow-all
spec:
  ports:
    - name: port8080
      port: 8080
      targetPort: 8080
    - name: port8081
      port: 8081
      targetPort: 8081
  selector:
    app: curl-pkg-allow-all
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl-pkg-allow-all
  namespace: curl-ns-allow-all
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl-pkg-allow-all
  template:
    metadata:
      labels:
        app: curl-pkg-allow-all
      annotations:
        sidecar.istio.io/proxyCPU: "10m"
        sidecar.istio.io/proxyMemory: "16Mi"
    spec:
      containers:
        - name: curl-pkg-allow-all
          image: hashicorp/http-echo
          imagePullPolicy: IfNotPresent
          args:
            - "-text=Hello from curl-pkg-allow-all on port 8080"
            - "-status-code=200"
            - "-listen=:8080"
          ports:
            - containerPort: 8080
        - name: curl-pkg-allow-all-8081
          image: hashicorp/http-echo
          imagePullPolicy: IfNotPresent
          args:
            - "-text=Hello from curl-pkg-allow-all on port 8081"
            - "-status-code=200"
            - "-listen=:8081"
          ports:
            - containerPort: 8081
---
apiVersion: v1
kind: Service
metadata:
  name: curl-pkg-remote-ns-egress
  namespace: curl-ns-remote-ns-1
  labels:
    name: curl-pkg-remote-ns-egress
    namespace: curl-ns-remote-ns-1
spec:
  ports:
    - name: port8080
      port: 8080
      targetPort: 8080
  selector:
    app: curl-pkg-remote-ns-egress
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl-pkg-remote-ns-egress
  namespace: curl-ns-remote-ns-1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl-pkg-remote-ns-egress
  template:
    metadata:
      labels:
        app: curl-pkg-remote-ns-egress
      annotations:
        sidecar.istio.io/proxyCPU: "10m"
        sidecar.istio.io/proxyMemory: "16Mi"
    spec:
      containers:
        - name: curl-pkg-remote-ns-egress
          image: curlimages/curl
          imagePullPolicy: IfNotPresent
          command: ["sleep", "3600"]
          resources:
            limits:
              cpu: 50m
              memory: 64Mi
            requests:
              cpu: 10m
              memory: 16Mi
          ports:
            - containerPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: curl-pkg-remote-ns-ingress
  namespace: curl-ns-remote-ns-2
  labels:
    name: curl-pkg-remote-ns-ingress
    namespace: curl-ns-remote-ns-2
spec:
  ports:
    - name: port8080
      port: 8080
      targetPort: 8080
  selector:
    app: curl-pkg-remote-ns-ingress
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl-pkg-remote-ns-ingress
  namespace: curl-ns-remote-ns-2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl-pkg-remote-ns-ingress
  template:
    metadata:
      labels:
        app: curl-pkg-remote-ns-ingress
      annotations:
        sidecar.istio.io/proxyCPU: "10m"
        sidecar.istio.io/proxyMemory: "16Mi"
    spec:
      containers:
        - name: curl-pkg-remote-ns-ingress
          image: hashicorp/http-echo
          imagePullPolicy: IfNotPresent
          args:
            - "-text=Hello from curl-pkg-remote-ns-ingress on port 8080"
            - "-status-code=200"
            - "-listen=:8080"
          ports:
            - containerPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: curl-pkg-kube-api
  namespace: curl-ns-kube-api
  labels:
    name: curl-pkg-kube-api
    namespace: curl-ns-kube-api
spec:
  ports:
    - name: port8080
      port: 8080
      targetPort: 8080
  selector:
    app: curl-pkg-kube-api
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl-pkg-kube-api
  namespace: curl-ns-kube-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl-pkg-kube-api
  template:
    metadata:
      labels:
        app: curl-pkg-kube-api
      annotations:
        sidecar.istio.io/proxyCPU: "10m"
        sidecar.istio.io/proxyMemory: "16Mi"
    spec:
      containers:
        - name: curl-pkg-kube-api
          image: curlimages/curl
          imagePullPolicy: IfNotPresent
          command: ["sleep", "3600"]
          resources:
            limits:
              cpu: 50m
              memory: 64Mi
            requests:
              cpu: 10m
              memory: 16Mi
          ports:
            - containerPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: curl-pkg-remote-cidr
  namespace: curl-ns-remote-cidr
  labels:
    name: curl-pkg-remote-cidr
    namespace: curl-ns-remote-cidr
spec:
  ports:
    - name: port8080
      port: 8080
      targetPort: 8080
  selector:
    app: curl-pkg-remote-cidr
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl-pkg-remote-cidr
  namespace: curl-ns-remote-cidr
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl-pkg-remote-cidr
  template:
    metadata:
      labels:
        app: curl-pkg-remote-cidr
      annotations:
        sidecar.istio.io/proxyCPU: "10m"
        sidecar.istio.io/proxyMemory: "16Mi"
    spec:
      containers:
        - name: curl-pkg-remote-cidr
          image: hashicorp/http-echo
          imagePullPolicy: IfNotPresent
          args:
            - "-text=Hello from curl-pkg-remote-cidr on port 8080"
            - "-status-code=200"
            - "-listen=:8080"
          ports:
            - containerPort: 8080
