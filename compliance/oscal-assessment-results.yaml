# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

assessment-results:
  import-ap:
    href: ""
  metadata:
    last-modified: 2024-10-16T20:13:51.735141137Z
    oscal-version: 1.1.2
    published: 2024-06-30T22:27:28.032093229Z
    remarks: Assessment Results generated from Lula
    title: '[System Name] Security Assessment Results (SAR)'
    version: 0.0.1
  results:
    - description: Assessment results for performing Validations with Lula version v0.9.1
      findings:
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 41c51dc3-7db1-4717-b071-83e57897f478
            Istio implements with service to service and provides authorization policies that require authentication to access any non-public features.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: 9d69895a-0eed-4052-8102-ff9070d66851
          target:
            status:
              state: satisfied
            target-id: ac-14
            type: objective-id
          title: 'Validation Result - Control: ac-14'
          uuid: 6bcf8d54-c497-483a-8b02-257d3ad14a13
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 069521de-43bc-4dce-ac4e-4adc9a559c3f
            # Control Description "a. Define and document the types of accounts allowed and specifically prohibited for use within the system; b. Assign account managers; c. Require [Assignment: organization-defined prerequisites and criteria] for group and role membership; d. Specify: 1. Authorized users of the system; 2. Group and role membership; and 3. Access authorizations (i.e., privileges) and [Assignment: organization-defined attributes (as required)] for each account; e. Require approvals by [Assignment: organization-defined personnel or roles] for requests to create accounts; f. Create, enable, modify, disable, and remove accounts in accordance with [Assignment: organization-defined policy, procedures, prerequisites, and criteria]; g. Monitor the use of accounts; h. Notify account managers and [Assignment: organization-defined personnel or roles] within: 1. [Assignment: twenty-four (24) hours] when accounts are no longer required; 2. [Assignment: eight (8) hours] when users are terminated or transferred; and 3. [Assignment: eight (8) hours] when system usage or need-to-know changes for an individual; i. Authorize access to the system based on: 1. A valid access authorization; 2. Intended system usage; and 3. [Assignment: organization-defined attributes (as required)]; j. Review accounts for compliance with account management requirements [Assignment: monthly for privileged accessed, every six (6) months for non-privileged access]; k. Establish and implement a process for changing shared or group account authenticators (if deployed) when individuals are removed from the group; and l. Align account management processes with personnel termination and transfer processes."
            # Control Implementation NeuVector supports internal user accounts and roles in addition to LDAP and SSO for providing RBAC access.
          target:
            status:
              state: not-satisfied
            target-id: ac-2
            type: objective-id
          title: 'Validation Result - Control: ac-2'
          uuid: 94067e2e-ca10-4798-8077-c4128c8ad4f2
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: bf59763a-0c22-4046-ab00-1d2b47dad8df
            # Control Description Support the management of system accounts using [Assignment: organization-defined automated mechanisms].
            # Control Implementation NeuVector supports internal user accounts and roles in addition to LDAP and SSO for providing RBAC access.
          target:
            status:
              state: not-satisfied
            target-id: ac-2.1
            type: objective-id
          title: 'Validation Result - Control: ac-2.1'
          uuid: 4c4f8dc5-9be1-44fe-a04b-e369943af6c0
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 051af8b7-75aa-4c26-9132-0cb46d5965aa
            # Control Description Enforce approved authorizations for logical access to information and system resources in accordance with applicable access control policies.
            # Control Implementation NeuVector supports internal user accounts and roles in addition to LDAP and SSO for providing RBAC access.
          target:
            status:
              state: not-satisfied
            target-id: ac-3
            type: objective-id
          title: 'Validation Result - Control: ac-3'
          uuid: bd6ddf39-0886-4922-b9d5-8ee80f03fcf4
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 210f730b-7fed-42dd-99b4-42466951b080
            Istio encrypts all in-mesh communication at runtime using FIPS verified mTLS in addition to ingress and egress gateways for controlling communication.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: adb9497f-436d-4862-8043-6691bce1352c
            - observation-uuid: f10278ec-5f1b-4307-8709-a3745bf12d36
            - observation-uuid: a9c35339-fc94-4e59-bdf1-a89c1664bac0
          target:
            status:
              state: satisfied
            target-id: ac-4
            type: objective-id
          title: 'Validation Result - Control: ac-4'
          uuid: 66a85471-6739-4958-ac74-007040a6df48
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: ee9e5fae-1c95-46c7-9265-dc0035e2bb05
            Istio is configured to use ingress and egress gateways to provide logical flow separation.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: 2c61853a-96af-4195-8dc9-f3313f6035f1
            - observation-uuid: 20f98c91-2308-4356-837b-c253353d7479
            - observation-uuid: 4e1e697c-4a92-4c57-ae95-c75ba272dc1d
          target:
            status:
              state: not-satisfied
            target-id: ac-4.21
            type: objective-id
          title: 'Validation Result - Control: ac-4.21'
          uuid: eadbe4cd-7f8b-4ff3-bfd9-beaac5eae67f
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 386fb410-27e5-413d-8e6d-607afa86bb72
            # Control Description "a. Identify and document [Assignment: organization-defined duties of individuals requiring separation]; and b. Define system access authorizations to support separation of duties."
            # Control Implementation Loki implements RBAC to define system authorization and separation of duties.
          target:
            status:
              state: not-satisfied
            target-id: ac-5
            type: objective-id
          title: 'Validation Result - Control: ac-5'
          uuid: 5d48147b-ff9e-4752-b015-3588cec166a6
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 60ad5f60-3852-49a1-961b-b6454edb8319
            # Control Description Employ the principle of least privilege, allowing only authorized accesses for users (or processes acting on behalf of users) that are necessary to accomplish assigned organizational tasks.
            # Control Implementation Loki implements RBAC to employ principle of least privilege.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: df51cf5f-9c1b-4004-ae4a-195a663594ac
            # Control Description Employ the principle of least privilege, allowing only authorized accesses for users (or processes acting on behalf of users) that are necessary to accomplish assigned organizational tasks.
            # Control Implementation NeuVector supports mapping internal user accounts and roles in addition to LDAP and SSO roles or groups for providing RBAC access.
          target:
            status:
              state: not-satisfied
            target-id: ac-6
            type: objective-id
          title: 'Validation Result - Control: ac-6'
          uuid: c24dc3af-b777-4d89-9569-cd57de7981dc
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: f1b66def-f822-4859-a448-5d5f77cd6f75
            # Control Description "Authorize access for [Assignment: organization-defined individuals or roles] to: (a) [Assignment: organization-defined all functions not publicly accessible]; and (b) [Assignment: organization-defined all security-relevant information not publicly available]."
            # Control Implementation NeuVector supports mapping internal user accounts and roles in addition to LDAP and SSO roles or groups for providing RBAC access.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: e7721974-f672-47cf-9421-e1530aec1217
            # Control Description "Authorize access for [Assignment: organization-defined individuals or roles] to: (a) [Assignment: all functions not publicly accessible]]; and (b) [Assignment: all security-relevant information not publicly available]]."
            # Control Implementation Loki implements RBAC to employ principle of least privilege.
          target:
            status:
              state: not-satisfied
            target-id: ac-6.1
            type: objective-id
          title: 'Validation Result - Control: ac-6.1'
          uuid: 0016a2a5-efcf-493f-a816-e1fd880c6e41
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: d0ffa50d-d91f-4dc3-8827-24e0f84b49d2
            # Control Description Prevent non-privileged users from executing privileged functions.
            # Control Implementation Loki layers an additional RBAC layer that prohibits non-privileged users from executing privileged functions.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: e196edcd-fd88-42c2-9a99-0e67e2ba8919
            # Control Description Prevent non-privileged users from executing privileged functions.
            # Control Implementation NeuVector supports mapping internal user accounts and roles in addition to LDAP and SSO roles or groups for providing RBAC access.
          target:
            status:
              state: not-satisfied
            target-id: ac-6.10
            type: objective-id
          title: 'Validation Result - Control: ac-6.10'
          uuid: f03df0d2-9c46-420d-9eb0-925367a8e9ce
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 0b3faf98-8a76-4b49-8e4b-c785cf26cfbe
            # Control Description Authorize network access to [Assignment: all privileged commands] only for [Assignment: organization-defined compelling operational needs] and document the rationale for such access in the security plan for the system.
            # Control Implementation NeuVector supports mapping internal user accounts and roles in addition to LDAP and SSO roles or groups for providing RBAC access.
          target:
            status:
              state: not-satisfied
            target-id: ac-6.3
            type: objective-id
          title: 'Validation Result - Control: ac-6.3'
          uuid: 5f634c21-9332-4242-ae7d-d315cbc8e6fe
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 954ba9c8-452c-4503-a43f-c880a01b828d
            # Control Description
              Misuse of privileged functions, either intentionally or unintentionally by authorized users, or by unauthorized external entities that have compromised information system accounts, is a serious and ongoing concern and can have significant adverse impacts on organizations.
              Auditing the use of privileged functions is one way to detect such misuse, and in doing so, help mitigate the risk from insider threats and the advanced persistent threat (APT).

            # Control Implementation
              Vector can be configured to collect all logs from Kubernetes and underlying operating systems, allowing the aggregation of privileged function calls.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: c6d9abd2-0136-468a-908d-181d9bd51962
            Istio produces logs for all traffic in the information system.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 14db5706-570c-44a2-b430-29a8a8e2d249
            # Control Description Log the execution of privileged functions.
            # Control Implementation Privileged events, including updating the deployment of an application, or use of privileged containers are collected as metrics by prometheus and displayed by Grafana.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: e36ba9d5-f12d-4524-a777-a041a0203bb6
            # Control Description Log the execution of privileged functions.
            # Control Implementation Privileged events that modify the application are logged in the application itself.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 921ec1c7-923c-4a28-a4dd-b59c1d3d9998
            # Control Description Log the execution of privileged functions.
            # Control Implementation NeuVector provides logging access related audit events.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 4d1f5291-8f3f-429c-af2f-b05455ef30f0
            # Control Description Log the execution of privileged functions.
            # Control Implementation Privileged events, including updating the deployment of an application, or use of privileged containers are collected as metrics by prometheus and displayed by Grafana.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: a858d60b-5192-41de-bac4-a79479a91f64
            - observation-uuid: f10278ec-5f1b-4307-8709-a3745bf12d36
            - observation-uuid: 39cf41d1-048e-4132-9681-f7274e4be4ea
            - observation-uuid: 572f5829-517b-40d0-8b50-6d9bf9c54c77
          target:
            status:
              state: not-satisfied
            target-id: ac-6.9
            type: objective-id
          title: 'Validation Result - Control: ac-6.9'
          uuid: 1ee97cb7-b6d8-4319-b031-cd417e313f6f
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 20ecdb48-997e-4958-b74c-21f462049877
            # Control Description Retain audit records for [Assignment: at least one (1) year] to provide support for after-the-fact investigations of incidents and to meet regulatory and organizational information retention requirements.
            # Control Implementation Can configure audit record storage retention policy for defined periods of time via the store(s) Loki is configured to use.
          target:
            status:
              state: not-satisfied
            target-id: au-11
            type: objective-id
          title: 'Validation Result - Control: au-11'
          uuid: e4a8f249-89f4-4af5-9928-ab6eabfecdc3
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 87f99f34-6980-49e1-91cf-c0264fa3407c
            Istio provides audit record generation capabilities for a variety of event types, including session, connection, transaction, or activity durations, and the number of bytes received and sent.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: a858d60b-5192-41de-bac4-a79479a91f64
          target:
            status:
              state: satisfied
            target-id: au-12
            type: objective-id
          title: 'Validation Result - Control: au-12'
          uuid: 527b3d8a-ce49-483d-956e-5931140eeca6
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 58766714-a477-42b9-bae4-856f14b58cea
            # Control Description Compile audit records from [Assignment: all network, data storage, and computing devices] into a system-wide (logical or physical) audit trail that is time-correlated to within [Assignment: organization-defined level of tolerance for the relationship between time stamps of individual records in the audit trail].
            # Control Implementation Provides time-series event compilation capabilities.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 41a6f729-7ab6-4ffe-8da1-cb60fd35dffd
            # Control Description Compile audit records from [Assignment: organization-defined system components] into a system-wide (logical or physical) audit trail that is time-correlated to within [Assignment: organization-defined level of tolerance for the relationship between time stamps of individual records in the audit trail].
            # Control Implementation Compatible metrics endpoints emitted from each application is compiled by Prometheus and displayed through Grafana with associated timestamps of when the data was collected
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 301093ed-d023-4bf8-a915-e624589acadd
            # Control Description Compile audit records from [Assignment: all network, data storage, and computing devices] into a system-wide (logical or physical) audit trail that is time-correlated to within [Assignment: organization-defined level of tolerance for the relationship between time stamps of individual records in the audit trail].
            # Control Implementation Compatible metrics endpoints emitted from each application is compiled by Prometheus and displayed through Grafana with associated timestamps of when the data was collected.
          target:
            status:
              state: not-satisfied
            target-id: au-12.1
            type: objective-id
          title: 'Validation Result - Control: au-12.1'
          uuid: 77724c00-0dc6-493c-aed8-c3797f01e94a
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 49775d12-e0ba-4aa6-85e7-5aedd00e8fbc
            # Control Description "a. Identify the types of events that the system is capable of logging in support of the audit function: [Assignment: successful and unsuccessful account logon events, account management events, object access, policy change, privilege functions, process tracking, and system events. For Web applications: all administrator activity, authentication checks, authorization checks, data deletions, data access, data changes, and permission changes]; b. Coordinate the event logging function with other organizational entities requiring audit-related information to guide and inform the selection criteria for events to be logged; c. Specify the following event types for logging within the system: [Assignment: organization-defined subset of the auditable events defined in AU-2a to be audited continually for each identified event.) along with the frequency of (or situation requiring) logging for each identified event type]; d. Provide a rationale for why the event types selected for logging are deemed to be adequate to support after-the-fact investigations of incidents; and e. Review and update the event types selected for logging [Assignment: annually or whenever there is a change in the threat environment]."
            # Control Implementation API endpoints suitable for capturing application level metrics are present on each of the supported applications running as containers. In addition, system and cluster level metrics are emitted by containers with read only access to host level information. Metrics are captured and stored by Prometheus, an web server capable of scraping endpoints formatted in the appropriate dimensional data format. Metrics information is stored on disk in a time series data base, and later queried through a separate component providing a web interface for the query language: PromQL.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: fc829f66-2354-4546-8e5d-f1e5d0287200
            # Control Description "a. Identify the types of events that the system is capable of logging in support of the audit function: [Assignment: successful and unsuccessful account logon events, account management events, object access, policy change, privilege functions, process tracking, and system events. For Web applications: all administrator activity, authentication checks, authorization checks, data deletions, data access, data changes, and permission changes]; b. Coordinate the event logging function with other organizational entities requiring audit-related information to guide and inform the selection criteria for events to be logged; c. Specify the following event types for logging within the system: [Assignment: organization-defined subset of the auditable events defined in AU-2a to be audited continually for each identified event) along with the frequency of (or situation requiring) logging for each identified event type]; d. Provide a rationale for why the event types selected for logging are deemed to be adequate to support after-the-fact investigations of incidents; and e. Review and update the event types selected for logging [Assignment: annually or whenever there is a change in the threat environment]."
            # Control Implementation NeuVector provides logging access related audit events.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: b3ed3dba-3164-4785-98db-ef22c96c7c62
            Istio logs all Istio event logs within the system's mesh network.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 7449f733-6809-4a0b-a6f9-7857f46a106e
            # Control Description a. Identify the types of events that the system is capable of logging in support of the audit function: [Assignment: successful and unsuccessful account logon events, account management events, object access, policy change, privilege functions, process tracking, and system events. For Web applications: all administrator activity, authentication checks, authorization checks, data deletions, data access, data changes, and permission changes]; b. Coordinate the event logging function with other organizational entities requiring audit-related information to guide and inform the selection criteria for events to be logged; c. Specify the following event types for logging within the system: [Assignment: organization-defined event types (subset of the event types defined in AU-2a.) along with the frequency of (or situation requiring) logging for each identified event type]; d. Provide a rationale for why the event types selected for logging are deemed to be adequate to support after-the-fact investigations of incidents; and e. Review and update the event types selected for logging [Assignment: annually or whenever there is a change in the threat environment].
            # Control Implementation API endpoints suitable for capturing application level metrics are present on each of the supported applications running as containers. In addition, system and cluster level metrics are emitted by containers with read only access to host level information. Metrics are captured and stored by Prometheus, an web server capable of scraping endpoints formatted in the appropriate dimensional data format. Metrics information is stored on disk in a time series data base, and later queried through a separate component providing a web interface for the query language: PromQL. Metrics data can be displayed through a Grafana dashboard for visualization.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 2a25a5a4-4fbc-4fbc-88e3-2e34ddc3fb0e
            # Control Description
              An event is any observable occurrence in an organizational information system.
              Organizations identify audit events as those events which are significant and relevant to the security of information systems and the environments in which those systems operate in order to meet specific and ongoing audit needs.
              Audit events can include, for example, password changes, failed logons, or failed accesses related to information systems, administrative privilege usage, PIV credential usage, or third-party credential usage.
              In determining the set of auditable events, organizations consider the auditing appropriate for each of the security controls to be implemented.
              To balance auditing requirements with other information system needs, this control also requires identifying that subset of auditable events that are audited at a given point in time.

            # Control Implementation
              Logging daemons are present on each node that BigBang is installed on.  Out of the box, the following events are captured:
              * all containers emitting to STDOUT or STDERR (captured by container runtime creating containers logs under /var/log/pods).
              * all kubernetes api server requests.
              * all events emitted by the kubelet.
          related-observations:
            - observation-uuid: 39cf41d1-048e-4132-9681-f7274e4be4ea
            - observation-uuid: 0e3f9f2a-c596-4688-8841-0f9d2845f0a2
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: a858d60b-5192-41de-bac4-a79479a91f64
          target:
            status:
              state: not-satisfied
            target-id: au-2
            type: objective-id
          title: 'Validation Result - Control: au-2'
          uuid: cb369876-9857-41d9-ab42-81d476d6f974
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 762604db-77ec-415f-8728-c296873ab48b
            # Control Description
              Audit record content that may be necessary to satisfy the requirement of this control, includes, for example, time stamps, source and destination addresses, user/process identifiers, event descriptions, success/fail indications, filenames involved, and access control or flow control rules invoked.
              Event outcomes can include indicators of event success or failure and event-specific results (e.g., the security state of the information system after the event occurred).

            # Control Implementation
              Logs are captured by vector from the node. The node logs will contain the necessary log data from all pods/applications inside the selected nodes as well as Kubernetes audit logs.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 79dee0b0-5848-4b1e-826b-a2e4ec567b90
            Istio logs all Istio event logs within the system's mesh network.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: e342a5af-b7d4-474b-9416-61e844083531
            # Control Description "Ensure that audit records contain information that establishes the following: a. What type of event occurred; b. When the event occurred; c. Where the event occurred; d. Source of the event; e. Outcome of the event; and f. Identity of any individuals, subjects, or objects/entities associated with the event."
            # Control Implementation NeuVector provides logging access related audit events.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: ca312bc9-b8cd-4a30-9b72-50ffee51e828
            - observation-uuid: 39cf41d1-048e-4132-9681-f7274e4be4ea
            - observation-uuid: e72b33d8-64ba-4871-ab47-2f0b1a5c18e7
          target:
            status:
              state: not-satisfied
            target-id: au-3
            type: objective-id
          title: 'Validation Result - Control: au-3'
          uuid: e914d51b-58ff-4693-abd5-ea62add986d0
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: ee431ef9-3a99-42f4-b37c-6334660da2b2
            # Control Description Generate audit records containing the following additional information: [Assignment: organizatiosession, connection, transaction, or activity duration; for client-server transactions, the number of bytes received and bytes sent; additional informational messages to diagnose or identify the event; characteristics that describe or identify the object or resource being acted upon; individual identities of group account users; full-text of privileged commands].
            # Control Implementation Grafana has pre-configured dashboards showing the audit records from Cluster Auditor saved in Prometheus.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 6700f065-8e51-4224-a5a0-8d3aff9d8d96
            # Control Description Generate audit records containing the following additional information: [Assignment: session, connection, transaction, or activity duration; for client-server transactions, the number of bytes received and bytes sent; additional informational messages to diagnose or identify the event; characteristics that describe or identify the object or resource being acted upon; individual identities of group account users; full-text of privileged commands].
            # Control Implementation Grafana has pre-configured dashboards showing the audit records from Cluster Auditor saved in Prometheus.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: b855fff0-5f57-4ea0-b9a7-52973e81784d
            Istio has been configured to implement event logging within our environment. This includes capturing metrics related to the duration of sessions, connections, transactions, or activities. Specifically, Istio's telemetry features are utilized to capture these metrics, which provide valuable data that can be used to infer the duration of sessions or connections.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: 64a22438-eba3-4fcb-bfe8-68ed8bad61ba
            - observation-uuid: f10278ec-5f1b-4307-8709-a3745bf12d36
            - observation-uuid: 6e9c7750-ccda-4f25-b8de-89e8e3e3d525
          target:
            status:
              state: not-satisfied
            target-id: au-3.1
            type: objective-id
          title: 'Validation Result - Control: au-3.1'
          uuid: e645c9b0-2376-4d8c-9792-18a44c6dbdba
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 836408b9-1ae9-4c99-8510-6ee35a4d11e9
            # Control Description Allocate audit log storage capacity to accommodate [Assignment: organization-defined audit log retention requirements].
            # Control Implementation Loki uses scalable object storage.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: d5d13192-3cae-4a88-8e64-cab44219ab2e
            # Control Description Allocate audit log storage capacity to accommodate [Assignment: organization-defined audit log retention requirements].
            # Control Implementation Prometheus is the log aggregator for audit logs since it is used to scrape/collect violations from ClusterAuditor. The storage capability can be configured in prometheus to use PVCs to ensure metrics have log retention compliance with the org-defined audit-log retention requirements.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 7562092e-d076-49f9-8f03-9e5e7908752c
            # Control Description Allocate audit log storage capacity to accommodate [Assignment: organization-defined audit log retention requirements].
            # Control Implementation NeuVector can scale elastically based upon actual workload demands to allocate audit log storage capacity.
          target:
            status:
              state: not-satisfied
            target-id: au-4
            type: objective-id
          title: 'Validation Result - Control: au-4'
          uuid: f80f5478-4554-4906-b6e6-599f0847cad8
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: e2e6d28f-bdf6-462c-8301-bdfa102671ee
            # Control Description Provide a warning to [Assignment: organization-defined personnel, roles, and/or locations] within [Assignment: organization-defined time period] when allocated audit log storage volume reaches [Assignment: organization-defined percentage] of repository maximum audit log storage capacity.
            # Control Implementation Alertmanager has pre-built alerts for PVC storage thresholds that would fire for PVCs supporting prometheus metrics storage.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 36f95dfb-626f-4fce-8417-4d808560b9d3
            # Control Description Provide a warning to [Assignment: organization-defined personnel, roles, and/or locations] within [Assignment: organization-defined time period] when allocated audit log storage volume reaches [Assignment: organization-defined percentage] of repository maximum audit log storage capacity.
            # Control Implementation Alertmanager has pre-built alerts for PVC storage thresholds that would fire for PVCs supporting prometheus metrics storage. Metrics data can be displayed through a Grafana dashboard for visualization.
          target:
            status:
              state: not-satisfied
            target-id: au-5.1
            type: objective-id
          title: 'Validation Result - Control: au-5.1'
          uuid: 96eb22a4-5e88-431e-9bbf-2285afdecaa1
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: bea82b61-fbb6-486b-a8fa-50053715b904
            # Control Description Provide an alert within [Assignment: real-time] to [Assignment: service provider personnel with authority to address failed audit events] when the following audit failure events occur: [Assignment: audit failure events requiring real-time alerts, as defined by organization audit policy].
            # Control Implementation Alertmanager has pre-build alerts for failed pods that would show when ClusterAuditor is not processing events, or prometheus is unable to scrape events. Prometheus also has a deadman's alert to ensure end users are seeing events from prometheus as part of its configuration.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: d2d90ddf-dcc9-4087-ad71-ac67b66a154a
            # Control Description Provide an alert within [Assignment: real-time] to [Assignment: service provider personnel with authority to address failed audit events] when the following audit failure events occur: [Assignment: audit failure events requiring real-time alerts, as defined by organization audit policy].
            # Control Implementation Alertmanager has pre-built alerts for failed pods that would show when ClusterAuditor is not processing events, or prometheus is unable to scrape events. Prometheus also has a deadman's alert to ensure end users are seeing events from prometheus as part of its configuration. Data can be displayed through a Grafana dashboard for visualization.
          target:
            status:
              state: not-satisfied
            target-id: au-5.2
            type: objective-id
          title: 'Validation Result - Control: au-5.2'
          uuid: 1b355cc0-7a5a-44a1-ae85-cf9079faf888
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 25477ca3-4607-449e-9d33-a2a67ede0019
            # Control Description "a. Review and analyze system audit records [Assignment: at least weekly] for indications of [Assignment: organization-defined inappropriate or unusual activity] and the potential impact of the inappropriate or unusual activity; b. Report findings to [Assignment: organization-defined personnel or roles]; and c. Adjust the level of audit record review, analysis, and reporting within the system when there is a change in risk based on law enforcement information, intelligence information, or other credible sources of information."
            # Control Implementation Provides audit record query and analysis capabilities. Organization will implement record review and analysis.
          target:
            status:
              state: not-satisfied
            target-id: au-6
            type: objective-id
          title: 'Validation Result - Control: au-6'
          uuid: 4114b048-0ef8-4863-9d2c-15ce9585badc
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 3f8f6178-4c57-4592-8c1c-df79507b21cd
            # Control Description Integrate audit record review, analysis, and reporting processes using [Assignment: organization-defined automated mechanisms].
            # Control Implementation Cluster Auditor Events/Alerts could be exported from Prometheus to an external system. Integration for specific tooling would need to be completed by end user.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 29fdcbbd-02cc-4db1-a24e-5a146cccc254
            # Control Description Integrate audit record review, analysis, and reporting processes using [Assignment: organization-defined automated mechanisms].
            # Control Implementation Provides audit record query and analysis capabilities. Organization will implement record review and analysis.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 042fae4b-2779-4cfb-b68d-6f2dcbaa10ad
            # Control Description Integrate audit record review, analysis, and reporting processes using [Assignment: organization-defined automated mechanisms].
            # Control Implementation Cluster Auditor Events/Alerts could be exported from Prometheus to an external system. Integration for specific tooling would need to be completed by end user. Metrics data can be displayed through a Grafana dashboard for visualization.
          target:
            status:
              state: not-satisfied
            target-id: au-6.1
            type: objective-id
          title: 'Validation Result - Control: au-6.1'
          uuid: 8c4e94bb-1143-4980-8531-c29eb700c13e
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 35897d1f-3fcd-4a79-b235-f75e2bbd398a
            # Control Description Analyze and correlate audit records across different repositories to gain organization-wide situational awareness.
            # Control Implementation Aggregating cluster auditor events across multiple sources (clusters) is possible with a multi-cluster deployment of prometheus/grafana.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: c79cf2fa-2081-4034-831f-2c8016a275da
            # Control Description Analyze and correlate audit records across different repositories to gain organization-wide situational awareness.
            # Control Implementation Aggregating cluster auditor events across multiple sources (clusters) is possible with a multi-cluster deployment of prometheus/grafana.
          target:
            status:
              state: not-satisfied
            target-id: au-6.3
            type: objective-id
          title: 'Validation Result - Control: au-6.3'
          uuid: 580039a0-f7e7-4279-96cd-d7acb79f3bcf
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 6b0cd4b8-ab38-4012-b637-de2ca4bf5497
            # Control Description Integrate analysis of audit records with analysis of [Selection (one or more): vulnerability scanning information; performance data; system monitoring information; [Assignment: organization-defined data/information collected from other sources]] to further enhance the ability to identify inappropriate or unusual activity.
            # Control Implementation Cluster Auditor's audit data is consolidated with system monitoring tooling (node exporters) for consolidated view to enhance inappropriate or unusual activity.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 80de1b87-8288-49ac-8a6b-fc71509df64b
            # Control Description Integrate analysis of audit records with analysis of Selection (one or more): vulnerability scanning information; performance data; information system monitoring information; penetration test data; [Assignment: organization-defined data/information collected from other sources]] to further enhance the ability to identify inappropriate or unusual activity.
            # Control Implementation Cluster Auditor's audit data is consolidated with system monitoring tooling (node exporters) for consolidated view to enhance inappropriate or unusual activity. Metrics data can be displayed through a Grafana dashboard for visualization.
          target:
            status:
              state: not-satisfied
            target-id: au-6.5
            type: objective-id
          title: 'Validation Result - Control: au-6.5'
          uuid: 62521aad-6089-45a5-991c-af39844fe2e6
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: f6d4527a-d4b6-4141-9272-c2c211b1709f
            # Control Description Correlate information from audit records with information obtained from monitoring physical access to further enhance the ability to identify suspicious, inappropriate, unusual, or malevolent activity.
            # Control Implementation Cluster Auditor data in prometheus would enable this, but would require prometheus to also obtain access to physical metrics.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: b8c17326-**************-64d571540e37
            # Control Description Correlate information from audit records with information obtained from monitoring physical access to further enhance the ability to identify suspicious, inappropriate, unusual, or malevolent activity.
            # Control Implementation Cluster Auditor data in prometheus would enable this, but would require prometheus to also obtain access to physical metrics. Metrics data can be displayed through a Grafana dashboard for visualization.
          target:
            status:
              state: not-satisfied
            target-id: au-6.6
            type: objective-id
          title: 'Validation Result - Control: au-6.6'
          uuid: 05e4c2aa-5b83-415d-a529-c421db846827
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 8abbc53e-0ec4-49c6-8ef1-a1c237695f96
            # Control Description Provide and implement an audit record reduction and report generation capability that: a. Supports on-demand audit record review, analysis, and reporting requirements and after-the-fact investigations of incidents; and b. Does not alter the original content or time ordering of audit records.
            # Control Implementation Grafana is configured with a pre-built dashboard for policy violations that displays data collected by Cluster Auditor.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 18f4f45b-d707-417f-91ac-28ab503313d8
            # Control Description "Provide and implement an audit record reduction and report generation capability that: a. Supports on-demand audit record review, analysis, and reporting requirements and after-the-fact investigations of incidents; and b. Does not alter the original content or time ordering of audit records."
            # Control Implementation Grafana is configured with a pre-built dashboard for policy violations that displays data collected by Cluster Auditor.
          target:
            status:
              state: not-satisfied
            target-id: au-7
            type: objective-id
          title: 'Validation Result - Control: au-7'
          uuid: 22bb2862-f635-4d98-9d47-30d21d6fe72d
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 56d09aae-ab73-49d8-b2a4-1e81db2878eb
            # Control Description Provide and implement the capability to process, sort, and search audit records for events of interest based on the following content: [Assignment: organization-defined fields within audit records].
            # Control Implementation Grafana is configured with a pre-built dashboard for policy violations that displays data collected by Cluster Auditor.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 0a4d39e4-979d-4284-a190-e7e5b4aa7162
            # Control description Provide and implement the capability to process, sort, and search audit records for events of interest based on the following content: [Assignment: organization-defined fields within audit records].
            # Control Implementation Grafana is configured with a pre-built dashboard for policy violations that displays data collected by Cluster Auditor.
          target:
            status:
              state: not-satisfied
            target-id: au-7.1
            type: objective-id
          title: 'Validation Result - Control: au-7.1'
          uuid: d002dab9-22bf-471c-af83-98e5c21b567b
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 9ad7ddfb-4701-4c34-88f7-9d85abb13d60
            # Control Description
              Time stamps generated by the information system include date and time.
              Time is commonly expressed in Coordinated Universal Time (UTC), a modern continuation of Greenwich Mean Time (GMT), or local time with an offset from UTC.
              Granularity of time measurements refers to the degree of synchronization between information system clocks and reference clocks, for example, clocks synchronizing within hundreds of milliseconds or within tens of milliseconds.
              Organizations may define different time granularities for different system components.
              Time service can also be critical to other security capabilities such as access control and identification and authentication, depending on the nature of the mechanisms used to support those capabilities.

            # Control Implementation
              Records captured by the logging daemon are enriched to  ensure the following are always present:
              * time of the event (UTC).
              * source of event (pod, namespace, container id).
              Applications are responsible for providing all other information.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 689aa5d6-2b4b-40ca-a49f-51df0e220ec5
            # Control Description "a. Use internal system clocks to generate time stamps for audit records; and b. Record time stamps for audit records that meet [Assignment: organization-defined granularity of time measurement] and that use Coordinated Universal Time, have a fixed local time offset from Coordinated Universal Time, or that include the local time offset as part of the time stamp."
            # Control Implementation Prometheus stores all data as time-series data, so the timestamps of when those violations were present is part of the data-stream.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 9be1e683-93e1-4769-aa7d-951e2c8f8627
            # Control Description a. Use internal system clocks to generate time stamps for audit records; and b. Record time stamps for audit records that meet [Assignment: one second granularity of time measurement] and that use Coordinated Universal Time, have a fixed local time offset from Coordinated Universal Time, or that include the local time offset as part of the time stamp.
            # Control Implementation Prometheus stores all data as time-series data, so the timestamps of when those violations were present is part of the data-stream. Metrics data can be displayed through a Grafana dashboard for visualization.
          related-observations:
            - observation-uuid: 39cf41d1-048e-4132-9681-f7274e4be4ea
            - observation-uuid: e72b33d8-64ba-4871-ab47-2f0b1a5c18e7
          target:
            status:
              state: not-satisfied
            target-id: au-8
            type: objective-id
          title: 'Validation Result - Control: au-8'
          uuid: 09180600-c757-434e-93c5-028bdce2a2be
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: bfd070e8-d053-4e48-925a-baf9bcbd9335
            # Control Description "a. Protect audit information and audit logging tools from unauthorized access, modification, and deletion; and b. Alert [Assignment: organization-defined personnel or roles] upon detection of unauthorized access, modification, or deletion of audit information."
            # Control Implementation Grafana has the ability to provide Role Based Access Control to limit the data sources that end users can view by leveraging an identity provider. Grafana can also limit users to subsets of metrics within a datasource by the use of Label Based Access Control when using Grafana Enterprise.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: f800923b-6367-4468-9f42-1afae4b6d38d
            # Control Description a. Protect audit information and audit logging tools from unauthorized access, modification, and deletion; and b. Alert [Assignment: organization-defined personnel or roles] upon detection of unauthorized access, modification, or deletion of audit information.
            # Control Implementation Grafana has the ability to provide Role Based Access Control to limit the data sources that end users can view by leveraging an identity provider. Grafana can also limit users to subsets of metrics within a datasource by the use of Label Based Access Control when using Grafana Enterprise.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 21879fc4-927e-4ad4-a049-c96cb581e260
            # Control Description "a. Protect audit information and audit logging tools from unauthorized access, modification, and deletion; and b. Alert [Assignment: organization-defined personnel or roles] upon detection of unauthorized access, modification, or deletion of audit information."
            # Control Implementation Access to metrics can be restricted to org-defined personnel behind a private endpoint and not given to mission owners.
          target:
            status:
              state: not-satisfied
            target-id: au-9
            type: objective-id
          title: 'Validation Result - Control: au-9'
          uuid: eef40459-0315-41a1-aaae-333cf89990d0
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 3c4bf1e8-b873-4c43-a912-5f443fc0208f
            # Control Description Store audit records [Assignment: at least weekly] in a repository that is part of a physically different system or system component than the system or component being audited.
            # Control Implementation Prometheus can scrape external components outside of the system, but this configuration is not easily supported as part of the current big bang configuration of ClusterAuditor since external access to ClusterAuditor metrics is not exposed via Istio. Metrics data can be displayed through a Grafana dashboard for visualization.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 27f26f6a-706e-4514-97c0-45390d6fdf6a
            # Control Description Store audit records [Assignment: organization-defined frequency] in a repository that is part of a physically different system or system component than the system or component being audited.
            # Control Implementation Prometheus can scrape external components outside of the system, but this configuration is not easily supported as part of the current UDS Coreg configuration of ClusterAuditor since external access to ClusterAuditor metrics is not exposed via Istio.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: b89edef2-5668-407b-b3d5-86ca68862536
            # Control Description Store audit records [Assignment: at least weekly] in a repository that is part of a physically different system or system component than the system or component being audited.
            # Control Implementation Supports any object storage.
          target:
            status:
              state: not-satisfied
            target-id: au-9.2
            type: objective-id
          title: 'Validation Result - Control: au-9.2'
          uuid: 3f0dbc02-5d1e-48b9-bcdb-c8d569102724
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: f3292e9a-1c10-45cd-9178-aeecbaec0283
            # Control Description Authorize access to management of audit logging functionality to only [Assignment: organization-defined subset of privileged users or roles].
            # Control Implementation Enterprise version (Loki) implements RBAC.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 3c5ff037-ea46-4e41-b601-a9b223da30a8
            # Control Description Authorize access to management of audit logging functionality to only [Assignment: organization-defined subset of privileged users or roles].
            # Control Implementation Grafana has the ability to provide Role Based Access Control to limit the data sources that end users can view by leveraging an identity provider. Grafana can also limit users to subsets of metrics within a datasource by the use of Label Based Access Control when using Grafana Enterprise.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 0fee5118-57c8-4617-97a1-76189bc69ea3
            # Control Description Authorize access to management of audit logging functionality to only [Assignment: organization-defined subset of privileged users or roles].
            # Control Implementation Grafana has the ability to provide Role Based Access Control to limit the data sources that end users can view by leveraging an identity provider. Grafana can also limit users to subsets of metrics within a datasource by the use of Label Based Access Control when using Grafana Enterprise.
          target:
            status:
              state: not-satisfied
            target-id: au-9.4
            type: objective-id
          title: 'Validation Result - Control: au-9.4'
          uuid: 49e846cc-e1e4-4770-b352-1a674ab7f6ef
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 973c9f19-8c96-4c84-925a-b69f28625962
            # Control Description Provide and implement the capability to process, sort, and search audit records for events of interest based on the following content: [Assignment: organization-defined fields within audit records].
            # Control Implementation Loki provides an API for retrieving and filtering logs.
          target:
            status:
              state: not-satisfied
            target-id: au7.1
            type: objective-id
          title: 'Validation Result - Control: au7.1'
          uuid: 599569ed-dfd0-4f91-b6c3-bd42dd759758
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 9de67d41-1c18-4ebd-af55-cac2573aa77e
            # Control Description
             Include as part of control assessments, [Assignment: at least annually], [Selection: announced; unannounced], [Selection (one or more): in-depth monitoring; security instrumentation; automated security test cases; vulnerability scanning; malicious
             user testing; insider threat assessment; performance and load testing; data leakage or data loss assessment; [Assignment: organization-defined other forms of assessment]].

            # Control Implementation NeuVector continually monitors kubernetes environments and container images to detect misconfigurations, advanced network threats, and vulnerable hosts with all attempts to exploit a vulnerability is documented.
          target:
            status:
              state: not-satisfied
            target-id: ca-2.2
            type: objective-id
          title: 'Validation Result - Control: ca-2.2'
          uuid: 76c192e1-0da1-48e9-bed4-26fd5ed64513
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 2d771492-b5c8-4475-b258-0038287f29e6
            # Control Description "Develop a system-level continuous monitoring strategy and implement continuous monitoring in accordance with the organization-level continuous monitoring strategy that includes: a. Establishing the following system-level metrics to be monitored: [Assignment: organization-defined system-level metrics]; b. Establishing [Assignment: organization-defined frequencies] for monitoring and [Assignment: organization-defined frequencies] for assessment of control effectiveness; c. Ongoing control assessments in accordance with the continuous monitoring strategy; d. Ongoing monitoring of system and organization-defined metrics in accordance with the continuous monitoring strategy; e. Correlation and analysis of information generated by control assessments and monitoring; f. Response actions to address results of the analysis of control assessment and monitoring information; and g. Reporting the security and privacy status of the system to [Assignment: to include JAB/AO] [Assignment: organization-defined frequency]."
            # Control Implementation NeuVector continually monitors kubernetes environments and container images to detect misconfigurations, advanced network threats, and vulnerable hosts with all attempts to exploit a vulnerability is documented.
          target:
            status:
              state: not-satisfied
            target-id: ca-7
            type: objective-id
          title: 'Validation Result - Control: ca-7'
          uuid: 3669e122-aa7f-4a62-9173-f5b750a091c7
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 80a456cf-c642-4b02-a0fb-18b416e90481
            Istio enforces logical access restrictions associated with changes to the system. Istio's Role-Based Access Control (RBAC) features are used to define and enforce access controls, ensuring that only approved personnel can make changes to the system.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: cf774c2c-17dc-48d2-ba4c-2ae6547ea8e0
            - observation-uuid: 1fc0d5bd-18e9-4244-a1a0-3c618aca8652
          target:
            status:
              state: not-satisfied
            target-id: cm-5
            type: objective-id
          title: 'Validation Result - Control: cm-5'
          uuid: e3b8cf43-f476-44be-9f67-6b055afb67de
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 2fb488b2-f7f7-4db9-8fc8-3de7f3a9daba
            # Control Description "a. Establish and document configuration settings for components employed within the system that reflect the most restrictive mode consistent with operational requirements using [Assignment: oUnited States Government Configuration Baseline (USGCB)]; b. Implement the configuration settings; c. Identify, document, and approve any deviations from established configuration settings for [Assignment: organization-defined system components] based on [Assignment: organization-defined operational requirements]; and d. Monitor and control changes to the configuration settings in accordance with organizational policies and procedures."
            # Control Implementation NeuVector is configured using Helm Charts. Default settings can be found.
          target:
            status:
              state: not-satisfied
            target-id: cm-6
            type: objective-id
          title: 'Validation Result - Control: cm-6'
          uuid: 89a6efb7-47b8-491a-a510-9679175322df
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: a9d92277-809d-440f-82c9-35c820ba00b8
            # Control Description "a. Configure the system to provide only [Assignment: organization-defined mission essential capabilities]; and b. Prohibit or restrict the use of the following functions, ports, protocols, software, and/or services: [Assignment: organization-defined prohibited or restricted functions, system ports, protocols, software, and/or services]." "CM-7 (b) Requirement: The service provider shall use the DoD STIGs or Center for Internet Security guidelines to establish list of prohibited or restricted functions, ports, protocols, and/or services or establishes its own list of prohibited or restricted functions, ports, protocols, and/or services if USGCB is not available. CM-7 Guidance: Information on the USGCB checklists can be found at: https://csrc.nist.gov/projects/united-states-government-configuration-baseline."
            # Control Implementation NeuVector is configured securely and only access to required ports are available.
          target:
            status:
              state: not-satisfied
            target-id: cm-7
            type: objective-id
          title: 'Validation Result - Control: cm-7'
          uuid: 7ad3be3a-696f-4fe8-a06a-d20ca88b0815
        - description: |
            Control Implementation: 5108E5FC-C45F-477B-8542-9C5611A92485 / Implemented Requirement: 26B3D98B-0C9D-434B-8DE5-06CBBC46A38C
            Velero can restore application configuration/data from an approved cloud provider or on-premise location on-demand.
          target:
            status:
              state: not-satisfied
            target-id: cp-10
            type: objective-id
          title: 'Validation Result - Control: cp-10'
          uuid: 833dd12f-c1a1-4790-b161-df244240fd59
        - description: |
            Control Implementation: 5108E5FC-C45F-477B-8542-9C5611A92485 / Implemented Requirement: 3EA444B7-61ED-43DD-8B3D-24B55F286E59
            Velero gives you tools to back up and restore your Kubernetes cluster resources and persistent volumes. You can run Velero with a cloud provider or on-premises. This includes: - System components/data. - User-level information/application metadata. - User-level storage/data. - Scheduled back-ups with configurable scopes. - Multi-cloud and on-premise support for availability of backup.
          target:
            status:
              state: not-satisfied
            target-id: cp-10.4
            type: objective-id
          title: 'Validation Result - Control: cp-10.4'
          uuid: 8820d975-0a13-4068-a158-379efbdbd50e
        - description: |
            Control Implementation: 5108E5FC-C45F-477B-8542-9C5611A92485 / Implemented Requirement: 2ADA7512-E0D5-4CAE-81BC-C889C640AF93
            Velero can take backups of your application configuration/data and store them off-site in either an approved cloud environment or on-premise location.
          target:
            status:
              state: not-satisfied
            target-id: cp-6
            type: objective-id
          title: 'Validation Result - Control: cp-6'
          uuid: 9b8d3f8d-acc6-490e-8571-6e716d8b1a8e
        - description: |
            Control Implementation: 5108E5FC-C45F-477B-8542-9C5611A92485 / Implemented Requirement: 6C3339A0-9636-4E35-8FA8-731CF900B326
            Velero can take backups of your application configuration/data and store them off-site in either an approved cloud environment or on-premise location.
          target:
            status:
              state: not-satisfied
            target-id: cp-6.1
            type: objective-id
          title: 'Validation Result - Control: cp-6.1'
          uuid: 463f79ec-a0d5-4372-b093-ffc9707d4b61
        - description: |
            Control Implementation: 5108E5FC-C45F-477B-8542-9C5611A92485 / Implemented Requirement: 2799CCBF-C48D-4451-85BA-EBD9B949C361
            Velero can restore application configuration/data from an approved cloud provider or on-premise location on-demand.
          target:
            status:
              state: not-satisfied
            target-id: cp-6.2
            type: objective-id
          title: 'Validation Result - Control: cp-6.2'
          uuid: 7a97b30a-2246-4a2d-a069-d9a4c5518413
        - description: |
            Control Implementation: 5108E5FC-C45F-477B-8542-9C5611A92485 / Implemented Requirement: 0AE59B43-50A7-4420-881B-E0635CCB8424
            Velero supports back-ups to multiple cloud environments (including geo-separated locations for high availability) and on-premise environments in the event of an accessibility disruptions.
          target:
            status:
              state: not-satisfied
            target-id: cp-6.3
            type: objective-id
          title: 'Validation Result - Control: cp-6.3'
          uuid: fd1dcf1b-226a-4a46-afc8-9551a4c86ff9
        - description: |
            Control Implementation: 5108E5FC-C45F-477B-8542-9C5611A92485 / Implemented Requirement: B11B38B8-8744-4DFD-8C1A-4A4EDD7F9574
            Velero can restore application configuration/data from an approved cloud provider or on-premise location to an alternative deployment environment on-demand.
          target:
            status:
              state: not-satisfied
            target-id: cp-7
            type: objective-id
          title: 'Validation Result - Control: cp-7'
          uuid: e64b4f34-9a67-4f53-9af5-010f7e23fa2f
        - description: |
            Control Implementation: 5108E5FC-C45F-477B-8542-9C5611A92485 / Implemented Requirement: D74C3A8C-E5B0-4F81-895D-FB2A318D723B
            Velero supports back-ups to  and restores from multiple cloud environments (including geo-separated locations for high availability) and on-premise environments in the event of an accessibility disruptions.
          target:
            status:
              state: not-satisfied
            target-id: cp-7.1
            type: objective-id
          title: 'Validation Result - Control: cp-7.1'
          uuid: 3b91723b-8426-4de7-8e34-9fa9fb2623b1
        - description: |
            Control Implementation: 5108E5FC-C45F-477B-8542-9C5611A92485 / Implemented Requirement: 72D7145F-7A3F-47AF-835F-7E3D6EFAE1CC
            Velero supports back-ups to  and restores from multiple cloud environments (including geo-separated locations for high availability) and on-premise environments in the event of an accessibility disruptions.
          target:
            status:
              state: not-satisfied
            target-id: cp-7.2
            type: objective-id
          title: 'Validation Result - Control: cp-7.2'
          uuid: 3a18ef87-2d67-4fe1-a029-db52ff5a2416
        - description: |
            Control Implementation: 5108E5FC-C45F-477B-8542-9C5611A92485 / Implemented Requirement: 5B0AA4CB-9C49-4D32-8242-5631788BD941
            "Velero gives you tools to back up and restore your Kubernetes cluster resources and persistent volumes. You can run Velero with a cloud provider or on-premises. This includes:
              - System components/data.
              - User-level information/application metadata.
              - User-level storage/data.
              - Scheduled back-ups with configurable scopes.
              - Multi-cloud and on-premise support for availability of backup."
          target:
            status:
              state: not-satisfied
            target-id: cp-9
            type: objective-id
          title: 'Validation Result - Control: cp-9'
          uuid: 93de1fe8-4652-4ad9-b558-7f6ae73ed529
        - description: |
            Control Implementation: 5108E5FC-C45F-477B-8542-9C5611A92485 / Implemented Requirement: 8E5917F3-3E45-46C1-8585-48550E19AFFB
            Velero provides feedback/logging of back-up status for configuration/data via kubectl or the Velero CLI tool. Velero can restore your production configuration/data to validation environment to ensure reliability/integrity.
          target:
            status:
              state: not-satisfied
            target-id: cp-9.1
            type: objective-id
          title: 'Validation Result - Control: cp-9.1'
          uuid: 80715954-af49-4dee-90e1-1333f6c79610
        - description: |
            Control Implementation: 5108E5FC-C45F-477B-8542-9C5611A92485 / Implemented Requirement: 51191D0E-0C7B-4D2D-861D-202AC8C505CF
            Velero can be configured to restore only certain components of a back-up when necessary.
          target:
            status:
              state: not-satisfied
            target-id: cp-9.2
            type: objective-id
          title: 'Validation Result - Control: cp-9.2'
          uuid: f70d34be-5cdf-404b-a160-ca35a8d8445f
        - description: |
            Control Implementation: 5108E5FC-C45F-477B-8542-9C5611A92485 / Implemented Requirement: C650411C-33FD-4B59-8899-AC34B43C860F
            Velero supports back-ups to multiple cloud environments (including geo-separated locations for high availability) and on-premise environments.
          target:
            status:
              state: not-satisfied
            target-id: cp-9.3
            type: objective-id
          title: 'Validation Result - Control: cp-9.3'
          uuid: cc3f593a-5138-4603-aaca-64248856b942
        - description: |
            Control Implementation: 5108E5FC-C45F-477B-8542-9C5611A92485 / Implemented Requirement: 8AB09B17-301B-4836-835B-9CE22A9E2300
            Velero gives you tools to back up and restore your Kubernetes cluster resources and persistent volumes. You can run Velero with a cloud provider or on-premises. This includes: - System components/data. - User-level information/application metadata. - User-level storage/data. - Scheduled back-ups with configurable scopes. - Multi-cloud and on-premise support for availability of backup.
          target:
            status:
              state: not-satisfied
            target-id: cp-9.5
            type: objective-id
          title: 'Validation Result - Control: cp-9.5'
          uuid: b4f84755-1e05-485e-8d08-12f81032183f
        - description: |
            Control Implementation: 5108E5FC-C45F-477B-8542-9C5611A92485 / Implemented Requirement: 7FACB782-C183-4585-8C0B-17824438FEA6
            Velero supports encryption of backups via its supported providers' encryption support/mechanisms.
          target:
            status:
              state: not-satisfied
            target-id: cp-9.8
            type: objective-id
          title: 'Validation Result - Control: cp-9.8'
          uuid: 2121fda9-ff81-434f-8add-3785ffa925f7
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 8ef96f45-dfc4-41a8-999a-fc717e746966
            # Control Description "a. Monitor and scan for vulnerabilities in the system and hosted applications [Assignment: monthly operating system/infrastructure; monthly web applications (including APIs) and databases] and when new vulnerabilities potentially affecting the system are identified and reported; b. Employ vulnerability monitoring tools and techniques that facilitate interoperability among tools and automate parts of the vulnerability management process by using standards for: 1. Enumerating platforms, software flaws, and improper configurations; 2. Formatting checklists and test procedures; and 3. Measuring vulnerability impact; c. Analyze vulnerability scan reports and results from vulnerability monitoring; d. Remediate legitimate vulnerabilities [Assignment: high-risk vulnerabilities mitigated within thirty (30) days from date of discovery; moderate-risk vulnerabilities mitigated within ninety (90) days from date of discovery; low risk vulnerabilities mitigated within one hundred and eighty (180) days from date of discovery] in accordance with an organizational assessment of risk; e. Share information obtained from the vulnerability monitoring process and control assessments with [Assignment: organization-defined personnel or roles] to help eliminate similar vulnerabilities in other systems; and f. Employ vulnerability monitoring tools that include the capability to readily update the vulnerabilities to be scanned."
            # Control Implementation NeuVector is Kubernetes and container security tool. NeuVector will scan containers for vulnerabilities in addition to continuous monitoring for active threats.
          target:
            status:
              state: not-satisfied
            target-id: ra-5
            type: objective-id
          title: 'Validation Result - Control: ra-5'
          uuid: d2939750-cb5c-44bd-8b2f-36fd734bd546
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 760dde06-de0b-4575-8575-95a5835f97c0
            # Control Description Update the system vulnerabilities to be scanned [prior to a new scan]; prior to a new scan; when new vulnerabilities are identified and reported].
            # Control Implementation NeuVector container scanning vulnerability database is updated frequently.
          target:
            status:
              state: not-satisfied
            target-id: ra-5.2
            type: objective-id
          title: 'Validation Result - Control: ra-5.2'
          uuid: 04546660-1b4a-428f-8f20-1f3575957efb
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 621595cd-f998-4f55-b68e-f765db48b332
            # Control Description Define the breadth and depth of vulnerability scanning coverage.
            # Control Implementation NeuVector container scanning configurations depth can be modified.
          target:
            status:
              state: not-satisfied
            target-id: ra-5.3
            type: objective-id
          title: 'Validation Result - Control: ra-5.3'
          uuid: 498d8b43-30e0-4f94-9915-abc0233d6104
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 994b03df-8320-4987-887b-fac8088bd944
            # Control Description Implement privileged access authorization to [Assignment: all components that support authentication] for [Assignment: all scans].
            # Control Implementation NeuVector supports mapping internal user accounts and roles in addition to LDAP and SSO roles or groups for providing RBAC access.
          target:
            status:
              state: not-satisfied
            target-id: ra-5.5
            type: objective-id
          title: 'Validation Result - Control: ra-5.5'
          uuid: 881b2fec-7ae0-4a9a-9329-e202e10a75c6
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 5a7bddc2-f94c-46c8-a15a-1e2f4d4ab948
            # Control Description "Require the developer of the system, system component, or system service, at all post-design stages of the system development life cycle, to: a. Develop and implement a plan for ongoing security and privacy control assessments; b. Perform [Selection (one or more): unit; integration; system; regression] testing/evaluation [Assignment: organization-defined frequency] at [Assignment: organization-defined depth and coverage]; c. Produce evidence of the execution of the assessment plan and the results of the testing and evaluation; d. Implement a verifiable flaw remediation process; and e. Correct flaws identified during testing and evaluation."
            # Control Implementation NeuVector continually monitors kubernetes environments and container images to detect misconfigurations, advanced network threats, and vulnerable hosts with all attempts to exploit a vulnerability is documented.
          target:
            status:
              state: not-satisfied
            target-id: sa-11
            type: objective-id
          title: 'Validation Result - Control: sa-11'
          uuid: ef49e421-c79a-4ebf-a963-2d3a94561836
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: b6f194ad-bde3-479f-8a77-0ec4c9a5a77d
            # Control Description Require the developer of the system, system component, or system service to employ static code analysis tools to identify common flaws and document the results of the analysis. Static code analysis provides a technology and methodology for security reviews and includes checking for weaknesses in the code as well as for the incorporation of libraries or other included code with known vulnerabilities or that are out-of-date and not supported. Static code analysis can be used to identify vulnerabilities and enforce secure coding practices. It is most effective when used early in the development process, when each code change can automatically be scanned for potential weaknesses. Static code analysis can provide clear remediation guidance and identify defects for developers to fix. Evidence of the correct implementation of static analysis can include aggregate defect density for critical defect types, evidence that defects were inspected by developers or security professionals, and evidence that defects were remediated. A high density of ignored findings, commonly referred to as false positives, indicates a potential problem with the analysis process or the analysis tool. In such cases, organizations weigh the validity of the evidence against evidence from other sources.
            # Control Implementation NeuVector continually monitors kubernetes environments and container images to detect misconfigurations, advanced network threats, and vulnerable hosts with all attempts to exploit a vulnerability is documented.
          target:
            status:
              state: not-satisfied
            target-id: sa-11.1
            type: objective-id
          title: 'Validation Result - Control: sa-11.1'
          uuid: 3a093444-2d9b-4465-97ed-8745ff1fa60f
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: ad919a09-d186-4edd-9234-ead04f959fff
            Istio is configured to manage network connections associated with specific communication sessions. It can be set up to automatically terminate these connections after periods of inactivity, providing an additional layer of security.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: 825da259-13c9-4b85-b890-75d107573c62
          target:
            status:
              state: not-satisfied
            target-id: sc-10
            type: objective-id
          title: 'Validation Result - Control: sc-10'
          uuid: cd9707b3-f417-4ed1-9f6d-2c49351ca2e2
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 675c0823-8e94-4910-9f61-5266d7e7b38c
            Istio provides FIPS encryption in transit for all applications in the mesh, TLS termination at ingress, and TLS origination at egress.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: adb9497f-436d-4862-8043-6691bce1352c
            - observation-uuid: a9c35339-fc94-4e59-bdf1-a89c1664bac0
            - observation-uuid: 47e30ded-89cf-4862-bed8-b3c5c0b3a17f
            - observation-uuid: 46fd0f63-fefa-40fe-9894-7c52bbce7f9b
          target:
            status:
              state: not-satisfied
            target-id: sc-13
            type: objective-id
          title: 'Validation Result - Control: sc-13'
          uuid: b5ca4929-a54b-4bb9-9aff-7178e4f9b86b
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: dac01dde-3bdf-4e70-9d4d-4081c88de380
            Istio is configured to protect session authenticity, establishing confidence in the ongoing identities of other parties and the validity of transmitted information. This is achieved through Istio's mutual TLS, which ensures secure communication.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: adb9497f-436d-4862-8043-6691bce1352c
          target:
            status:
              state: satisfied
            target-id: sc-23
            type: objective-id
          title: 'Validation Result - Control: sc-23'
          uuid: 5843dd90-f9bf-46fe-aabd-b736cbc69496
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 0e72ca49-e9cb-4a74-8701-6f81091197b6
            Istio is configured to maintain separate execution domains for each executing process. This is achieved through Istio's sidecar proxy design, where each service in the mesh has its own dedicated sidecar proxy to handle its inbound and outbound traffic. This ensures that communication between processes is controlled and one process cannot modify the executing code of another process.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: f10278ec-5f1b-4307-8709-a3745bf12d36
          target:
            status:
              state: satisfied
            target-id: sc-39
            type: objective-id
          title: 'Validation Result - Control: sc-39'
          uuid: fb891c52-8267-4632-b8e8-e4543ce5b872
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 82d3ab37-b934-4731-9198-56ced7d92708
            # Control Description "a. Monitor and control communications at the external managed interfaces to the system and at key internal managed interfaces within the system; b. Implement subnetworks for publicly accessible system components that are [Selection: physically; logically] separated from internal organizational networks; and c. Connect to external networks or systems only through managed interfaces consisting of boundary protection devices arranged in accordance with an organizational security and privacy architecture."
            # Control Implementation NeuVector monitors all communications to external interfaces by only connecting to external networks through managed interfaces and utilizes whitelists and blacklists for rules at Layer 7.
          target:
            status:
              state: not-satisfied
            target-id: sc-7
            type: objective-id
          title: 'Validation Result - Control: sc-7'
          uuid: abd6a4b8-c69f-40ec-b127-1597db93bd36
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: a5bac410-d674-431d-b5fc-2f904842c29c
            Istio is configured to provide managed interfaces for external telecommunication services, establish traffic flow policies, and protect the confidentiality and integrity of transmitted information. It also prevents unauthorized exchange of control plane traffic and filters unauthorized control plane traffic.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: ceb8c9fa-058a-4223-91f9-8361c84e359f
            - observation-uuid: 9d69895a-0eed-4052-8102-ff9070d66851
            - observation-uuid: 4e1e697c-4a92-4c57-ae95-c75ba272dc1d
          target:
            status:
              state: satisfied
            target-id: sc-7.4
            type: objective-id
          title: 'Validation Result - Control: sc-7.4'
          uuid: 48fe83c1-ec77-4f79-bcc1-e233978571cc
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 3f409103-880e-4180-81e7-54f85a6143ae
            Istio is configured to route internal communications traffic to external networks through authenticated proxy servers at managed interfaces, using its Egress Gateway.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: 8cb4f8c0-cade-41b4-9a01-06a14888d13a
            - observation-uuid: d831dc82-53da-438f-b7ff-a6b579bdb3ef
          target:
            status:
              state: not-satisfied
            target-id: sc-7.8
            type: objective-id
          title: 'Validation Result - Control: sc-7.8'
          uuid: b5761b5a-4afb-45ef-8050-d8768f8bc362
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 132fb1ff-8b58-4cfd-8ad4-c01605d89f24
            # Control Description Protect the [confidentiality AND integrity] of transmitted information.
            # Control Implementation Data in transit is protected using a TLS connection and secured between components within the data center using an internal certificate until it is terminated at the application node. This ensures that data in transit is encrypted using SSL.
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: e97a451e-44c7-4240-a7a7-adaadd26f01c
            Istio is configured to protect the confidentiality and integrity of transmitted information across both internal and external networks. This is achieved through Istio's mutual TLS, which encrypts service-to-service communication, ensuring that data in transit is not exposed to the possibility of interception and modification.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: adb9497f-436d-4862-8043-6691bce1352c
            - observation-uuid: f10278ec-5f1b-4307-8709-a3745bf12d36
          target:
            status:
              state: not-satisfied
            target-id: sc-8
            type: objective-id
          title: 'Validation Result - Control: sc-8'
          uuid: 9d7a4bdc-53f2-4a42-b30b-88c5a2c607ac
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: f3b38f79-9bf7-4024-a1b2-00befd67fda7
            Istio is configured to protect the confidentiality and integrity of transmitted information across both internal and external networks. This is achieved through Istio's mutual TLS, which encrypts service-to-service communication, ensuring that data in transit is not exposed to the possibility of interception and modification.
          related-observations:
            - observation-uuid: eb3200f0-0b42-4bab-9987-673684f62d82
            - observation-uuid: adb9497f-436d-4862-8043-6691bce1352c
            - observation-uuid: f10278ec-5f1b-4307-8709-a3745bf12d36
          target:
            status:
              state: satisfied
            target-id: sc-8.1
            type: objective-id
          title: 'Validation Result - Control: sc-8.1'
          uuid: 28aec3a5-8f40-409d-bf88-9a83c476d0a3
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 9b4c7011-aa35-4f61-ade2-7c070bb51767
            # Control Description "a. Generate error messages that provide information necessary for corrective actions without revealing information that could be exploited; and b. Reveal error messages only to [Assignment: organization-defined personnel or roles]."
            # Control Implementation NeuVector correlates configuration data and network traffic for error tracking to provide context around misconfigurations and threats in the form of actionable alerts.
          target:
            status:
              state: not-satisfied
            target-id: si-11
            type: objective-id
          title: 'Validation Result - Control: si-11'
          uuid: f0008256-c408-442d-86f3-3a56c8177874
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 4faa4029-52bc-4d7f-9896-e43c6731d5e5
            # Control Description "(a) Measure the time between flaw identification and flaw remediation; and (b) Establish the following benchmarks for taking corrective actions: [Assignment: organization-defined benchmarks]."
            # Control Implementation NeuVector continually monitors your Kubernetes environments to detect misconfigurations, advanced network threats, and vulnerable hosts with all attempts to exploit a vulnerability is documented.
          target:
            status:
              state: not-satisfied
            target-id: si-2.3
            type: objective-id
          title: 'Validation Result - Control: si-2.3'
          uuid: c4cde847-c1ac-4962-aec2-84f2e373405e
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: c83fdce5-53f5-4860-a586-242d044efaa9
            # Control Description "a. Monitor the system to detect: 1. Attacks and indicators of potential attacks in accordance with the following monitoring objectives: [Assignment: organization-defined monitoring objectives]; and 2. Unauthorized local, network, and remote connections; b. Identify unauthorized use of the system through the following techniques and methods: [Assignment: organization-defined techniques and methods]; c. Invoke internal monitoring capabilities or deploy monitoring devices: 1. Strategically within the system to collect organization-determined essential information; and 2. At ad hoc locations within the system to track specific types of transactions of interest to the organization; d. Analyze detected events and anomalies; e. Adjust the level of system monitoring activity when there is a change in risk to organizational operations and assets, individuals, other organizations, or the Nation; f. Obtain legal opinion regarding system monitoring activities; and g. Provide [Assignment: organization-defined system monitoring information] to [Assignment: organization-defined personnel or roles] [Selection (one or more): as needed; [Assignment: organization-defined frequency]]."
            # Control Implementation NeuVector continually monitors your Kubernetes environments to detect misconfigurations, advanced network threats, and vulnerable hosts with all attempts to exploit a vulnerability is documented.
          target:
            status:
              state: not-satisfied
            target-id: si-4
            type: objective-id
          title: 'Validation Result - Control: si-4'
          uuid: 16c943ed-c12f-417b-8d90-21788f3349a5
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: ac61e461-5fb8-4cf1-89ff-36d002056fda
            # Control Description "a. Receive system security alerts, advisories, and directives from [Assignment: o include US-CERT] on an ongoing basis; b. Generate internal security alerts, advisories, and directives as deemed necessary; c. Disseminate security alerts, advisories, and directives to: [Selection (one or more): [Assignment: organization-defined personnel or roles]; to include system security personnel and administrators with configuration/patch-management responsibilities and d. Implement security directives in accordance with established time frames, or notify the issuing organization of the degree of noncompliance."
            # Control Implementation NeuVector correlates configuration data with user behavior and network traffic to provide context around misconfigurations and threats in the form of actionable alerts.
          target:
            status:
              state: not-satisfied
            target-id: si-5
            type: objective-id
          title: 'Validation Result - Control: si-5'
          uuid: 970ac268-cdf4-44a2-8d86-3b10915f0a32
        - description: |
            Control Implementation: d2afb4c4-2cd8-5305-a6cc-d1bc7b388d0c / Implemented Requirement: 80552838-9db8-41f7-9603-d91f884aa7bb
            # Control Description "a. Verify the correct operation of [Assignment: organization-defined security and privacy functions]; b. Perform the verification of the functions specified in SI-6a [Selection (one or more): [Assignment: to include upon system startup and/or restart]; upon command by user with appropriate privilege; [Assignment: at least monthly]]; c. Alert [Assignment: to include system administrators and security personnel] to failed security and privacy verification tests; and d. [Selection (one or more): Shut the system down; Restart the system; [Assignment: organization-defined alternative action (s)]] when anomalies are discovered."
            # Control Implementation NeuVector correlates configuration data and network traffic to provide context around verification in the form of actionable alerts.
          target:
            status:
              state: not-satisfied
            target-id: si-6
            type: objective-id
          title: 'Validation Result - Control: si-6'
          uuid: 33305746-ba60-43cd-8ed3-9f50fd279f0c
      observations:
        - collected: 2024-10-16T20:13:50.57177773Z
          description: |
            [TEST]: f345c359-3208-46fb-9348-959bd628301e - istio-prometheus-annotations-validation
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: satisfied
              remarks: |
                validate.msg: All pods have correct prometheus annotations.
                validate.msg_exempted_namespaces: istio-system, kube-system, uds-dev-stack, zarf
          uuid: 6e9c7750-ccda-4f25-b8de-89e8e3e3d525
        - collected: 2024-10-16T20:13:50.571902333Z
          description: |
            [TEST]: c3b022eb-19a5-4711-8099-da4a90c9dd5d - egress-gateway-exists-and-configured-PLACEHOLDER
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: not-satisfied
              remarks: |
                Error running validation: provider Evaluate error: opa validation not performed - no resources to validate
          uuid: 8cb4f8c0-cade-41b4-9a01-06a14888d13a
        - collected: 2024-10-16T20:13:50.681017055Z
          description: |
            [TEST]: 1761ac07-80dd-47d2-947e-09f67943b986 - all-pods-istio-injected
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: satisfied
              remarks: |
                validate.msg_exempt_namespaces: istio-admin-gateway, istio-passthrough-gateway, istio-system, istio-tenant-gateway, kube-system, uds-dev-stack, zarf
                validate.msg: All pods have Istio sidecar proxy.
          uuid: f10278ec-5f1b-4307-8709-a3745bf12d36
        - collected: 2024-10-16T20:13:50.689844666Z
          description: |
            [TEST]: 0da39859-a91a-4ca6-bd8b-9b117689188f - all-namespaces-istio-injected
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: not-satisfied
              remarks: |
                validate.msg: Non-Istio-injected namespaces: {"authservice-test-app", "podinfo", "test-admin-app", "test-tenant-app"}
                validate.msg_exempt_namespaces: default, istio-admin-gateway, istio-passthrough-gateway, istio-system, istio-tenant-gateway, kube-node-lease, kube-public, kube-system, uds-crds, uds-dev-stack, uds-policy-exemptions, zarf
          uuid: 2c61853a-96af-4195-8dc9-f3313f6035f1
        - collected: 2024-10-16T20:13:50.693177593Z
          description: |
            [TEST]: 90738c86-6315-450a-ac69-cc50eb4859cc - check-istio-logging-all-traffic
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: satisfied
              remarks: |
                validate.msg: Istio is logging all traffic.
          uuid: a858d60b-5192-41de-bac4-a79479a91f64
        - collected: 2024-10-16T20:13:50.697120548Z
          description: |
            [TEST]: fbd877c8-d6b6-4d88-8685-2c4aaaab02a1 - istio-enforces-authorized-keycloak-access
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: satisfied
              remarks: |
                validate.msg: AuthorizationPolicy restricts access to Keycloak admin.
          uuid: 9d69895a-0eed-4052-8102-ff9070d66851
        - collected: 2024-10-16T20:13:50.697182945Z
          description: |
            [TEST]: fbe5855d-b4ea-4ff5-9f0d-5901d620577a - lula-validation-error
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: not-satisfied
              remarks: |
                Error getting Lula validation #fbe5855d-b4ea-4ff5-9f0d-5901d620577a: schema is invalid: [{/required   "missing properties 'domain', 'provider'" <nil>}]
          uuid: 572f5829-517b-40d0-8b50-6d9bf9c54c77
        - collected: 2024-10-16T20:13:50.697270528Z
          description: |
            [TEST]: 663f5e92-6db4-4042-8b5a-eba3ebe5a622 - communications-terminated-after-inactivity-PLACEHOLDER
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: not-satisfied
              remarks: |
                Error running validation: provider Evaluate error: opa validation not performed - no resources to validate
          uuid: 825da259-13c9-4b85-b890-75d107573c62
        - collected: 2024-10-16T20:13:50.697333466Z
          description: |
            [TEST]: 19faf69a-de74-4b78-a628-64a9f244ae13 - external-traffic-managed-PLACEHOLDER
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: not-satisfied
              remarks: |
                Error running validation: provider Evaluate error: opa validation not performed - no resources to validate
          uuid: d831dc82-53da-438f-b7ff-a6b579bdb3ef
        - collected: 2024-10-16T20:13:50.730168322Z
          description: |
            [TEST]: 570e2dc7-e6c2-4ad5-8ea3-f07974f59747 - secure-communication-with-istiod
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: satisfied
              remarks: |
                validate.msg: NetworkPolicies correctly configured for istiod in required namespaces.
                validate.msg_expected_istiod: Expected Istiod port: 15012, protocol: TCP.
                validate.msg_required_namespaces: authservice, grafana, keycloak, loki, metrics-server, monitoring, neuvector, uds-runtime, vector, velero
          uuid: ceb8c9fa-058a-4223-91f9-8361c84e359f
        - collected: 2024-10-16T20:13:50.730223946Z
          description: |
            [TEST]: 9bfc68e0-381a-4006-9f68-c293e3b20cee - lula-validation-error
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: not-satisfied
              remarks: |
                Error getting Lula validation #9bfc68e0-381a-4006-9f68-c293e3b20cee: schema is invalid: [{/required   "missing properties 'domain', 'provider'" <nil>}]
          uuid: e72b33d8-64ba-4871-ab47-2f0b1a5c18e7
        - collected: 2024-10-16T20:13:50.730273639Z
          description: |
            [TEST]: 8be1601e-5870-4573-ab4f-c1c199944815 - tls-origination-at-egress-PLACEHOLDER
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: not-satisfied
              remarks: |
                Error running validation: provider Evaluate error: opa validation not performed - no resources to validate
          uuid: 47e30ded-89cf-4862-bed8-b3c5c0b3a17f
        - collected: 2024-10-16T20:13:50.738968965Z
          description: |
            [TEST]: 67456ae8-4505-4c93-b341-d977d90cb125 - istio-health-check
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: satisfied
              remarks: |
                validate.msg: Istiod Deployment is healthy. HPA has sufficient replicas.
          uuid: eb3200f0-0b42-4bab-9987-673684f62d82
        - collected: 2024-10-16T20:13:50.745123247Z
          description: |
            [TEST]: ca49ac97-487a-446a-a0b7-92b20e2c83cb - enforce-mtls-strict
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: satisfied
              remarks: |
                validate.msg: All PeerAuthentications have mtls mode set to STRICT.
          uuid: adb9497f-436d-4862-8043-6691bce1352c
        - collected: 2024-10-16T20:13:50.754286064Z
          description: |
            [TEST]: c6c9daf1-4196-406d-8679-312c0512ab2e - check-istio-admin-gateway-and-usage
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: satisfied
              remarks: |
                validate.msg: Admin gateway exists: istio-admin-gateway/admin-gateway. Admin virtual services are using admin gateway.
          uuid: 20f98c91-2308-4356-837b-c253353d7479
        - collected: 2024-10-16T20:13:50.754326089Z
          description: |
            [TEST]: 98b97ec9-a9ce-4444-83d8-71066270a424 - lula-validation-error
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: not-satisfied
              remarks: |
                Error getting Lula validation #98b97ec9-a9ce-4444-83d8-71066270a424: schema is invalid: [{/required   "missing properties 'domain', 'provider'" <nil>}]
          uuid: 39cf41d1-048e-4132-9681-f7274e4be4ea
        - collected: 2024-10-16T20:13:50.868647184Z
          description: |
            [TEST]: 7b045b2a-106f-4c8c-85d9-ae3d7a8e0e28 - istio-rbac-enforcement-check
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: satisfied
              remarks: |
                validate.msg: Istio RBAC enforced
                validate.msg_all_auth_policies: keycloak/keycloak-block-admin-access-from-public-gateway, uds-runtime/runtime-authservice, uds-runtime/runtime-jwt-authz
          uuid: cf774c2c-17dc-48d2-ba4c-2ae6547ea8e0
        - collected: 2024-10-16T20:13:50.868740227Z
          description: |
            [TEST]: 9b361d7b-4e07-40db-8b86-3854ed499a4b - istio-rbac-for-approved-personnel-PLACEHOLDER
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: not-satisfied
              remarks: |
                Error running validation: provider Evaluate error: opa validation not performed - no resources to validate
          uuid: 1fc0d5bd-18e9-4244-a1a0-3c618aca8652
        - collected: 2024-10-16T20:13:51.070408027Z
          description: |
            [TEST]: b0a8f21e-b12f-47ea-a967-2f4a3ec69e44 - gateway-configuration-check
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: satisfied
              remarks: |
                validate.msg_existing_gateways: istio-admin-gateway/admin-gateway, istio-passthrough-gateway/passthrough-gateway, istio-tenant-gateway/tenant-gateway
                validate.msg_allowed_gateways: admin, passthrough, tenant
                validate.msg: Only allowed gateways found. All gateway types found.
          uuid: 4e1e697c-4a92-4c57-ae95-c75ba272dc1d
        - collected: 2024-10-16T20:13:51.266003971Z
          description: |
            [TEST]: f346b797-be35-40a8-a93a-585db6fd56ec - istio-tracing-logging-support
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: not-satisfied
              remarks: |
                validate.msg: Tracing logging not supported.
          uuid: ca312bc9-b8cd-4a30-9b72-50ffee51e828
        - collected: 2024-10-16T20:13:51.470751962Z
          description: |
            [TEST]: fd071676-6b92-4e1c-a4f0-4c8d2bd55aed - ingress-traffic-encrypted
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: satisfied
              remarks: |
                validate.msg: All gateways encrypt ingress traffic
                validate.msg_exempted_gateways: istio-passthrough-gateway/passthrough-gateway
          uuid: a9c35339-fc94-4e59-bdf1-a89c1664bac0
        - collected: 2024-10-16T20:13:51.47086859Z
          description: |
            [TEST]: *************-4894-b7b2-7e583b4a8977 - fips-evaluation-PLACEHOLDER
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: not-satisfied
              remarks: |
                Error running validation: provider Evaluate error: opa validation not performed - no resources to validate
          uuid: 46fd0f63-fefa-40fe-9894-7c52bbce7f9b
        - collected: 2024-10-16T20:13:51.665120832Z
          description: |
            [TEST]: 70d99754-2918-400c-ac9a-319f874fff90 - istio-metrics-logging-configured
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: satisfied
              remarks: |
                validate.msg: Metrics logging supported.
          uuid: 64a22438-eba3-4fcb-bfe8-68ed8bad61ba
        - collected: 2024-10-16T20:13:51.665185653Z
          description: |
            [TEST]: 0be7345d-e9d3-4248-9c14-5fed8e7bfa01 - lula-validation-error
          methods:
            - TEST
          relevant-evidence:
            - description: |
                Result: not-satisfied
              remarks: |
                Error getting Lula validation #0be7345d-e9d3-4248-9c14-5fed8e7bfa01: schema is invalid: [{/required   "missing properties 'domain', 'provider'" <nil>}]
          uuid: 0e3f9f2a-c596-4688-8841-0f9d2845f0a2
      props:
        - name: threshold
          ns: https://docs.lula.dev/oscal/ns
          value: "true"
        - name: target
          ns: https://docs.lula.dev/oscal/ns
          value: il4
      reviewed-controls:
        control-selections:
          - description: Controls Assessed by Lula
            include-controls:
              - control-id: ac-14
              - control-id: ac-2
              - control-id: ac-2.1
              - control-id: ac-3
              - control-id: ac-4
              - control-id: ac-4.21
              - control-id: ac-5
              - control-id: ac-6
              - control-id: ac-6.1
              - control-id: ac-6.10
              - control-id: ac-6.3
              - control-id: ac-6.9
              - control-id: au-11
              - control-id: au-12
              - control-id: au-12.1
              - control-id: au-2
              - control-id: au-3
              - control-id: au-3.1
              - control-id: au-4
              - control-id: au-5.1
              - control-id: au-5.2
              - control-id: au-6
              - control-id: au-6.1
              - control-id: au-6.3
              - control-id: au-6.5
              - control-id: au-6.6
              - control-id: au-7
              - control-id: au-7.1
              - control-id: au-8
              - control-id: au-9
              - control-id: au-9.2
              - control-id: au-9.4
              - control-id: au7.1
              - control-id: ca-2.2
              - control-id: ca-7
              - control-id: cm-5
              - control-id: cm-6
              - control-id: cm-7
              - control-id: cp-10
              - control-id: cp-10.4
              - control-id: cp-6
              - control-id: cp-6.1
              - control-id: cp-6.2
              - control-id: cp-6.3
              - control-id: cp-7
              - control-id: cp-7.1
              - control-id: cp-7.2
              - control-id: cp-9
              - control-id: cp-9.1
              - control-id: cp-9.2
              - control-id: cp-9.3
              - control-id: cp-9.5
              - control-id: cp-9.8
              - control-id: ra-5
              - control-id: ra-5.2
              - control-id: ra-5.3
              - control-id: ra-5.5
              - control-id: sa-11
              - control-id: sa-11.1
              - control-id: sc-10
              - control-id: sc-13
              - control-id: sc-23
              - control-id: sc-39
              - control-id: sc-7
              - control-id: sc-7.4
              - control-id: sc-7.8
              - control-id: sc-8
              - control-id: sc-8.1
              - control-id: si-11
              - control-id: si-2.3
              - control-id: si-4
              - control-id: si-5
              - control-id: si-6
        description: Controls validated
        remarks: Validation performed may indicate full or partial satisfaction
      start: 2024-10-16T20:13:51.671525491Z
      title: Lula Validation Result
      uuid: 2d3dad50-ee92-4dec-a73c-cc683e28c5a2
  uuid: 1f7a298e-29e3-4d62-92c0-e4f4b42078b4
