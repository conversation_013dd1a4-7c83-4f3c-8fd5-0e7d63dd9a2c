# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: v1
kind: Service
metadata:
  name: {{ include "keycloak.fullname" . }}-headless
  namespace: {{ .Release.Namespace }}  
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
    app.kubernetes.io/component: headless
spec:
  type: ClusterIP
  clusterIP: None
  ports:
    - name: http
      port: 80
      targetPort: http
      protocol: TCP
    - name: tcp
      port: 7800
      targetPort: tcp
      protocol: TCP
    - name: tcp-fd
      port: 57800
      targetPort: tcp-fd
      protocol: TCP
  selector:
    {{- include "keycloak.selectorLabels" . | nindent 4 }}
