apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    helm.sh/resource-policy: keep
    meta.helm.sh/release-name: base
    meta.helm.sh/release-namespace: istio-system
  creationTimestamp: "2025-03-10T19:25:39Z"
  generation: 1
  labels:
    app.kubernetes.io/instance: base
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: istio
    app.kubernetes.io/version: 1.24.3
    helm.sh/chart: base-1.24.3
  name: gateways.networking.istio.io
  resourceVersion: "1180"
  uid: 9f2d7c15-3cfa-469d-b5a2-83dc13d74fdf
spec:
  conversion:
    strategy: None
  group: networking.istio.io
  names:
    categories:
    - istio-io
    - networking-istio-io
    kind: Gateway
    listKind: GatewayList
    plural: gateways
    shortNames:
    - gw
    singular: gateway
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        properties:
          spec:
            description: 'Configuration affecting edge load balancer. See more details
              at: https://istio.io/docs/reference/config/networking/gateway.html'
            properties:
              selector:
                additionalProperties:
                  type: string
                description: One or more labels that indicate a specific set of pods/VMs
                  on which this gateway configuration should be applied.
                type: object
              servers:
                description: A list of server specifications.
                items:
                  properties:
                    bind:
                      description: The ip or the Unix domain socket to which the listener
                        should be bound to.
                      type: string
                    defaultEndpoint:
                      type: string
                    hosts:
                      description: One or more hosts exposed by this gateway.
                      items:
                        type: string
                      type: array
                    name:
                      description: An optional name of the server, when set must be
                        unique across all servers.
                      type: string
                    port:
                      description: The Port on which the proxy should listen for incoming
                        connections.
                      properties:
                        name:
                          description: Label assigned to the port.
                          type: string
                        number:
                          description: A valid non-negative integer port number.
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                        protocol:
                          description: The protocol exposed on the port.
                          type: string
                        targetPort:
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                      required:
                      - number
                      - protocol
                      - name
                      type: object
                    tls:
                      description: Set of TLS related options that govern the server's
                        behavior.
                      properties:
                        caCertificates:
                          description: REQUIRED if mode is `MUTUAL` or `OPTIONAL_MUTUAL`.
                          type: string
                        caCrl:
                          description: 'OPTIONAL: The path to the file containing
                            the certificate revocation list (CRL) to use in verifying
                            a presented client side certificate.'
                          type: string
                        cipherSuites:
                          description: 'Optional: If specified, only support the specified
                            cipher list.'
                          items:
                            type: string
                          type: array
                        credentialName:
                          description: For gateways running on Kubernetes, the name
                            of the secret that holds the TLS certs including the CA
                            certificates.
                          type: string
                        httpsRedirect:
                          description: If set to true, the load balancer will send
                            a 301 redirect for all http connections, asking the clients
                            to use HTTPS.
                          type: boolean
                        maxProtocolVersion:
                          description: |-
                            Optional: Maximum TLS protocol version.

                            Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
                          enum:
                          - TLS_AUTO
                          - TLSV1_0
                          - TLSV1_1
                          - TLSV1_2
                          - TLSV1_3
                          type: string
                        minProtocolVersion:
                          description: |-
                            Optional: Minimum TLS protocol version.

                            Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
                          enum:
                          - TLS_AUTO
                          - TLSV1_0
                          - TLSV1_1
                          - TLSV1_2
                          - TLSV1_3
                          type: string
                        mode:
                          description: |-
                            Optional: Indicates whether connections to this port should be secured using TLS.

                            Valid Options: PASSTHROUGH, SIMPLE, MUTUAL, AUTO_PASSTHROUGH, ISTIO_MUTUAL, OPTIONAL_MUTUAL
                          enum:
                          - PASSTHROUGH
                          - SIMPLE
                          - MUTUAL
                          - AUTO_PASSTHROUGH
                          - ISTIO_MUTUAL
                          - OPTIONAL_MUTUAL
                          type: string
                        privateKey:
                          description: REQUIRED if mode is `SIMPLE` or `MUTUAL`.
                          type: string
                        serverCertificate:
                          description: REQUIRED if mode is `SIMPLE` or `MUTUAL`.
                          type: string
                        subjectAltNames:
                          description: A list of alternate names to verify the subject
                            identity in the certificate presented by the client.
                          items:
                            type: string
                          type: array
                        verifyCertificateHash:
                          description: An optional list of hex-encoded SHA-256 hashes
                            of the authorized client certificates.
                          items:
                            type: string
                          type: array
                        verifyCertificateSpki:
                          description: An optional list of base64-encoded SHA-256
                            hashes of the SPKIs of authorized client certificates.
                          items:
                            type: string
                          type: array
                      type: object
                  required:
                  - port
                  - hosts
                  type: object
                type: array
            type: object
          status:
            properties:
              conditions:
                description: Current service state of the resource.
                items:
                  properties:
                    lastProbeTime:
                      description: Last time we probed the condition.
                      format: date-time
                      type: string
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      format: date-time
                      type: string
                    message:
                      description: Human-readable message indicating details about
                        last transition.
                      type: string
                    reason:
                      description: Unique, one-word, CamelCase reason for the condition's
                        last transition.
                      type: string
                    status:
                      description: Status is the status of the condition.
                      type: string
                    type:
                      description: Type is the type of the condition.
                      type: string
                  type: object
                type: array
              observedGeneration:
                anyOf:
                - type: integer
                - type: string
                description: Resource Generation to which the Reconciled Condition
                  refers.
                x-kubernetes-int-or-string: true
              validationMessages:
                description: Includes any errors or warnings detected by Istio's analyzers.
                items:
                  properties:
                    documentationUrl:
                      description: A url pointing to the Istio documentation for this
                        specific error type.
                      type: string
                    level:
                      description: |-
                        Represents how severe a message is.

                        Valid Options: UNKNOWN, ERROR, WARNING, INFO
                      enum:
                      - UNKNOWN
                      - ERROR
                      - WARNING
                      - INFO
                      type: string
                    type:
                      properties:
                        code:
                          description: A 7 character code matching `^IST[0-9]{4}$`
                            intended to uniquely identify the message type.
                          type: string
                        name:
                          description: A human-readable name for the message type.
                          type: string
                      type: object
                  type: object
                type: array
            type: object
            x-kubernetes-preserve-unknown-fields: true
        type: object
    served: true
    storage: false
    subresources:
      status: {}
  - name: v1alpha3
    schema:
      openAPIV3Schema:
        properties:
          spec:
            description: 'Configuration affecting edge load balancer. See more details
              at: https://istio.io/docs/reference/config/networking/gateway.html'
            properties:
              selector:
                additionalProperties:
                  type: string
                description: One or more labels that indicate a specific set of pods/VMs
                  on which this gateway configuration should be applied.
                type: object
              servers:
                description: A list of server specifications.
                items:
                  properties:
                    bind:
                      description: The ip or the Unix domain socket to which the listener
                        should be bound to.
                      type: string
                    defaultEndpoint:
                      type: string
                    hosts:
                      description: One or more hosts exposed by this gateway.
                      items:
                        type: string
                      type: array
                    name:
                      description: An optional name of the server, when set must be
                        unique across all servers.
                      type: string
                    port:
                      description: The Port on which the proxy should listen for incoming
                        connections.
                      properties:
                        name:
                          description: Label assigned to the port.
                          type: string
                        number:
                          description: A valid non-negative integer port number.
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                        protocol:
                          description: The protocol exposed on the port.
                          type: string
                        targetPort:
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                      required:
                      - number
                      - protocol
                      - name
                      type: object
                    tls:
                      description: Set of TLS related options that govern the server's
                        behavior.
                      properties:
                        caCertificates:
                          description: REQUIRED if mode is `MUTUAL` or `OPTIONAL_MUTUAL`.
                          type: string
                        caCrl:
                          description: 'OPTIONAL: The path to the file containing
                            the certificate revocation list (CRL) to use in verifying
                            a presented client side certificate.'
                          type: string
                        cipherSuites:
                          description: 'Optional: If specified, only support the specified
                            cipher list.'
                          items:
                            type: string
                          type: array
                        credentialName:
                          description: For gateways running on Kubernetes, the name
                            of the secret that holds the TLS certs including the CA
                            certificates.
                          type: string
                        httpsRedirect:
                          description: If set to true, the load balancer will send
                            a 301 redirect for all http connections, asking the clients
                            to use HTTPS.
                          type: boolean
                        maxProtocolVersion:
                          description: |-
                            Optional: Maximum TLS protocol version.

                            Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
                          enum:
                          - TLS_AUTO
                          - TLSV1_0
                          - TLSV1_1
                          - TLSV1_2
                          - TLSV1_3
                          type: string
                        minProtocolVersion:
                          description: |-
                            Optional: Minimum TLS protocol version.

                            Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
                          enum:
                          - TLS_AUTO
                          - TLSV1_0
                          - TLSV1_1
                          - TLSV1_2
                          - TLSV1_3
                          type: string
                        mode:
                          description: |-
                            Optional: Indicates whether connections to this port should be secured using TLS.

                            Valid Options: PASSTHROUGH, SIMPLE, MUTUAL, AUTO_PASSTHROUGH, ISTIO_MUTUAL, OPTIONAL_MUTUAL
                          enum:
                          - PASSTHROUGH
                          - SIMPLE
                          - MUTUAL
                          - AUTO_PASSTHROUGH
                          - ISTIO_MUTUAL
                          - OPTIONAL_MUTUAL
                          type: string
                        privateKey:
                          description: REQUIRED if mode is `SIMPLE` or `MUTUAL`.
                          type: string
                        serverCertificate:
                          description: REQUIRED if mode is `SIMPLE` or `MUTUAL`.
                          type: string
                        subjectAltNames:
                          description: A list of alternate names to verify the subject
                            identity in the certificate presented by the client.
                          items:
                            type: string
                          type: array
                        verifyCertificateHash:
                          description: An optional list of hex-encoded SHA-256 hashes
                            of the authorized client certificates.
                          items:
                            type: string
                          type: array
                        verifyCertificateSpki:
                          description: An optional list of base64-encoded SHA-256
                            hashes of the SPKIs of authorized client certificates.
                          items:
                            type: string
                          type: array
                      type: object
                  required:
                  - port
                  - hosts
                  type: object
                type: array
            type: object
          status:
            properties:
              conditions:
                description: Current service state of the resource.
                items:
                  properties:
                    lastProbeTime:
                      description: Last time we probed the condition.
                      format: date-time
                      type: string
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      format: date-time
                      type: string
                    message:
                      description: Human-readable message indicating details about
                        last transition.
                      type: string
                    reason:
                      description: Unique, one-word, CamelCase reason for the condition's
                        last transition.
                      type: string
                    status:
                      description: Status is the status of the condition.
                      type: string
                    type:
                      description: Type is the type of the condition.
                      type: string
                  type: object
                type: array
              observedGeneration:
                anyOf:
                - type: integer
                - type: string
                description: Resource Generation to which the Reconciled Condition
                  refers.
                x-kubernetes-int-or-string: true
              validationMessages:
                description: Includes any errors or warnings detected by Istio's analyzers.
                items:
                  properties:
                    documentationUrl:
                      description: A url pointing to the Istio documentation for this
                        specific error type.
                      type: string
                    level:
                      description: |-
                        Represents how severe a message is.

                        Valid Options: UNKNOWN, ERROR, WARNING, INFO
                      enum:
                      - UNKNOWN
                      - ERROR
                      - WARNING
                      - INFO
                      type: string
                    type:
                      properties:
                        code:
                          description: A 7 character code matching `^IST[0-9]{4}$`
                            intended to uniquely identify the message type.
                          type: string
                        name:
                          description: A human-readable name for the message type.
                          type: string
                      type: object
                  type: object
                type: array
            type: object
            x-kubernetes-preserve-unknown-fields: true
        type: object
    served: true
    storage: false
    subresources:
      status: {}
  - name: v1beta1
    schema:
      openAPIV3Schema:
        properties:
          spec:
            description: 'Configuration affecting edge load balancer. See more details
              at: https://istio.io/docs/reference/config/networking/gateway.html'
            properties:
              selector:
                additionalProperties:
                  type: string
                description: One or more labels that indicate a specific set of pods/VMs
                  on which this gateway configuration should be applied.
                type: object
              servers:
                description: A list of server specifications.
                items:
                  properties:
                    bind:
                      description: The ip or the Unix domain socket to which the listener
                        should be bound to.
                      type: string
                    defaultEndpoint:
                      type: string
                    hosts:
                      description: One or more hosts exposed by this gateway.
                      items:
                        type: string
                      type: array
                    name:
                      description: An optional name of the server, when set must be
                        unique across all servers.
                      type: string
                    port:
                      description: The Port on which the proxy should listen for incoming
                        connections.
                      properties:
                        name:
                          description: Label assigned to the port.
                          type: string
                        number:
                          description: A valid non-negative integer port number.
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                        protocol:
                          description: The protocol exposed on the port.
                          type: string
                        targetPort:
                          maximum: 4294967295
                          minimum: 0
                          type: integer
                      required:
                      - number
                      - protocol
                      - name
                      type: object
                    tls:
                      description: Set of TLS related options that govern the server's
                        behavior.
                      properties:
                        caCertificates:
                          description: REQUIRED if mode is `MUTUAL` or `OPTIONAL_MUTUAL`.
                          type: string
                        caCrl:
                          description: 'OPTIONAL: The path to the file containing
                            the certificate revocation list (CRL) to use in verifying
                            a presented client side certificate.'
                          type: string
                        cipherSuites:
                          description: 'Optional: If specified, only support the specified
                            cipher list.'
                          items:
                            type: string
                          type: array
                        credentialName:
                          description: For gateways running on Kubernetes, the name
                            of the secret that holds the TLS certs including the CA
                            certificates.
                          type: string
                        httpsRedirect:
                          description: If set to true, the load balancer will send
                            a 301 redirect for all http connections, asking the clients
                            to use HTTPS.
                          type: boolean
                        maxProtocolVersion:
                          description: |-
                            Optional: Maximum TLS protocol version.

                            Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
                          enum:
                          - TLS_AUTO
                          - TLSV1_0
                          - TLSV1_1
                          - TLSV1_2
                          - TLSV1_3
                          type: string
                        minProtocolVersion:
                          description: |-
                            Optional: Minimum TLS protocol version.

                            Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
                          enum:
                          - TLS_AUTO
                          - TLSV1_0
                          - TLSV1_1
                          - TLSV1_2
                          - TLSV1_3
                          type: string
                        mode:
                          description: |-
                            Optional: Indicates whether connections to this port should be secured using TLS.

                            Valid Options: PASSTHROUGH, SIMPLE, MUTUAL, AUTO_PASSTHROUGH, ISTIO_MUTUAL, OPTIONAL_MUTUAL
                          enum:
                          - PASSTHROUGH
                          - SIMPLE
                          - MUTUAL
                          - AUTO_PASSTHROUGH
                          - ISTIO_MUTUAL
                          - OPTIONAL_MUTUAL
                          type: string
                        privateKey:
                          description: REQUIRED if mode is `SIMPLE` or `MUTUAL`.
                          type: string
                        serverCertificate:
                          description: REQUIRED if mode is `SIMPLE` or `MUTUAL`.
                          type: string
                        subjectAltNames:
                          description: A list of alternate names to verify the subject
                            identity in the certificate presented by the client.
                          items:
                            type: string
                          type: array
                        verifyCertificateHash:
                          description: An optional list of hex-encoded SHA-256 hashes
                            of the authorized client certificates.
                          items:
                            type: string
                          type: array
                        verifyCertificateSpki:
                          description: An optional list of base64-encoded SHA-256
                            hashes of the SPKIs of authorized client certificates.
                          items:
                            type: string
                          type: array
                      type: object
                  required:
                  - port
                  - hosts
                  type: object
                type: array
            type: object
          status:
            properties:
              conditions:
                description: Current service state of the resource.
                items:
                  properties:
                    lastProbeTime:
                      description: Last time we probed the condition.
                      format: date-time
                      type: string
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      format: date-time
                      type: string
                    message:
                      description: Human-readable message indicating details about
                        last transition.
                      type: string
                    reason:
                      description: Unique, one-word, CamelCase reason for the condition's
                        last transition.
                      type: string
                    status:
                      description: Status is the status of the condition.
                      type: string
                    type:
                      description: Type is the type of the condition.
                      type: string
                  type: object
                type: array
              observedGeneration:
                anyOf:
                - type: integer
                - type: string
                description: Resource Generation to which the Reconciled Condition
                  refers.
                x-kubernetes-int-or-string: true
              validationMessages:
                description: Includes any errors or warnings detected by Istio's analyzers.
                items:
                  properties:
                    documentationUrl:
                      description: A url pointing to the Istio documentation for this
                        specific error type.
                      type: string
                    level:
                      description: |-
                        Represents how severe a message is.

                        Valid Options: UNKNOWN, ERROR, WARNING, INFO
                      enum:
                      - UNKNOWN
                      - ERROR
                      - WARNING
                      - INFO
                      type: string
                    type:
                      properties:
                        code:
                          description: A 7 character code matching `^IST[0-9]{4}$`
                            intended to uniquely identify the message type.
                          type: string
                        name:
                          description: A human-readable name for the message type.
                          type: string
                      type: object
                  type: object
                type: array
            type: object
            x-kubernetes-preserve-unknown-fields: true
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    categories:
    - istio-io
    - networking-istio-io
    kind: Gateway
    listKind: GatewayList
    plural: gateways
    shortNames:
    - gw
    singular: gateway
  conditions:
  - lastTransitionTime: "2025-03-10T19:25:39Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-03-10T19:25:39Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1beta1
