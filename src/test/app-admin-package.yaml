# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: v1
kind: Namespace
metadata:
  name: test-admin-app
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: httpbin
  namespace: test-admin-app
spec:
  network:
    expose:
      - service: httpbin
        selector:
          app: httpbin
        gateway: admin
        host: demo
        port: 8000
        targetPort: 80
        advancedHTTP:
          match:
            - name: test-get-and-prefix
              method:
                # Only allow GET requests
                regex: GET
              uri:
                # Only allow routing to /status/2*, everything else should 404
                prefix: /status/2
            - name: test-exact
              uri:
                # Only allow routing to /status/410
                exact: /status/410
    allow:
      - direction: Egress
        selector:
          app: httpbin
        remoteGenerated: Anywhere
        ports:
          - 443
          - 8080
          - 80
