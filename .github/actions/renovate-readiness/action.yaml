# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: renovate-readiness
description: "Check if Renovate PRs are ready for testing"

inputs:
  github_token:
    description: "GitHub token for API calls"
    required: true

runs:
  using: composite
  steps:
    # Check if PR has the renovate-ready label (manual override)
    - name: Check if PR has the ready label
      id: check-ready-label
      shell: bash
      run: |
        if [[ "${{ contains(github.event.pull_request.labels.*.name, 'renovate-ready') }}" == "true" ]]; then
          echo "PR has the renovate-ready label. Skipping readiness check."
          echo "should_process=false" >> $GITHUB_OUTPUT
        else
          echo "PR does not have the renovate-ready label. Proceeding with readiness check."
          echo "should_process=true" >> $GITHUB_OUTPUT
        fi

    # Process branch name to determine package
    - name: Process branch name
      id: process-branch
      if: steps.check-ready-label.outputs.should_process == 'true'
      shell: bash
      env:
        BRANCH_NAME: ${{ github.head_ref }}
      run: |
        echo "Branch name: $BRANCH_NAME"

        # Remove 'renovate/' prefix if present
        if [[ $BRANCH_NAME == renovate/* ]]; then
          PACKAGE_NAME=${BRANCH_NAME#renovate/}
          echo "Package name after removing prefix: $PACKAGE_NAME"
        else
          PACKAGE_NAME=$BRANCH_NAME
          echo "Branch doesn't have renovate/ prefix, using as is: $PACKAGE_NAME"
        fi

        # Handle special cases
        if [[ "$PACKAGE_NAME" == "pepr" ]]; then
          echo "Detected Pepr update"
          echo "package=pepr" >> $GITHUB_OUTPUT
          echo "is_pepr=true" >> $GITHUB_OUTPUT
          echo "needs_comparison=false" >> $GITHUB_OUTPUT
        elif [[ "$PACKAGE_NAME" == "support-deps" ]]; then
          echo "Detected support dependencies update"
          echo "package=support-deps" >> $GITHUB_OUTPUT
          echo "is_support_deps=true" >> $GITHUB_OUTPUT
          echo "needs_comparison=false" >> $GITHUB_OUTPUT
        else
          echo "Regular package update: $PACKAGE_NAME"
          echo "package=$PACKAGE_NAME" >> $GITHUB_OUTPUT
          echo "needs_comparison=true" >> $GITHUB_OUTPUT
        fi

    # Handle Pepr updates
    - name: Handle Pepr update
      if: steps.process-branch.outputs.is_pepr == 'true'
      shell: bash
      env:
        GH_TOKEN: ${{ inputs.github_token }}
      run: |
        # Get Pepr version from package.json
        PEPR_VERSION=$(jq -r '.dependencies.pepr' package.json)
        echo "Pepr version from package.json: $PEPR_VERSION"

        # Get image versions from tasks/create.yaml
        IRONBANK_IMAGE_VERSION=$(yq e '.variables[] | select(.name == "REGISTRY1_PEPR_IMAGE") | .default | split(":")[1]' tasks/create.yaml)
        IRONBANK_IMAGE_VERSION=${IRONBANK_IMAGE_VERSION#v}
        echo "Ironbank image version: $IRONBANK_IMAGE_VERSION"

        # Compare versions
        if [[ "$PEPR_VERSION" != "$IRONBANK_IMAGE_VERSION" ]]; then
          echo "Pepr version mismatch. Waiting on Ironbank image update."
          gh pr edit ${{ github.event.pull_request.number }} --add-label "waiting on ironbank"
          exit 1
        else
          echo "Pepr versions match. Ready for review."
          gh pr edit ${{ github.event.pull_request.number }} --remove-label "waiting on ironbank" || true
          gh pr edit ${{ github.event.pull_request.number }} --add-label "needs-review"
        fi

    # Handle support dependencies
    - name: Handle support dependencies
      if: steps.process-branch.outputs.is_support_deps == 'true'
      shell: bash
      env:
        GH_TOKEN: ${{ inputs.github_token }}
      run: |
        echo "Support dependencies update detected. Needs manual review."
        gh pr edit ${{ github.event.pull_request.number }} --add-label "needs-review"
        # Fail the job to prevent excessive CI runs of IAC clusters
        exit 1

    # Checkout PR branch (sparse checkout of src/<pkg>)
    - name: Checkout PR branch
      if: steps.process-branch.outputs.needs_comparison == 'true'
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      with:
        ref: ${{ github.event.pull_request.head.ref }}
        path: new
        sparse-checkout: |
          src/${{ steps.process-branch.outputs.package }}
        sparse-checkout-cone-mode: false

    # Checkout main branch (sparse checkout of src/<pkg>)
    - name: Checkout main branch
      if: steps.process-branch.outputs.needs_comparison == 'true'
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      with:
        ref: main
        path: old
        sparse-checkout: |
          src/${{ steps.process-branch.outputs.package }}
        sparse-checkout-cone-mode: false

    # Install dependencies
    - name: Install dependencies
      if: steps.process-branch.outputs.needs_comparison == 'true'
      shell: bash
      run: |
        cd scripts/renovate
        npm install

    # Extract images and charts from old branch
    - name: Extract images and charts from old branch
      if: steps.process-branch.outputs.needs_comparison == 'true'
      shell: bash
      run: |
        cd scripts/renovate
        npx ts-node getImagesAndCharts.ts $GITHUB_WORKSPACE/old

    # Extract images and charts from new branch
    - name: Extract images and charts from new branch
      if: steps.process-branch.outputs.needs_comparison == 'true'
      shell: bash
      run: |
        cd scripts/renovate
        npx ts-node getImagesAndCharts.ts $GITHUB_WORKSPACE/new

    # Compare images and charts
    - name: Compare images and charts
      id: compare
      if: steps.process-branch.outputs.needs_comparison == 'true'
      shell: bash
      run: |
        cd scripts/renovate
        OUTPUT=$(npx ts-node compareImagesAndCharts.ts $GITHUB_WORKSPACE/old/extract $GITHUB_WORKSPACE/new/extract)
        echo "$OUTPUT"

        # Extract labels from output
        LABELS=$(echo "$OUTPUT" | grep "LABELS=" | cut -d'=' -f2)
        echo "labels=$LABELS" >> $GITHUB_OUTPUT

        # Check if waiting on labels are present, or this is a helm chart update only
        if [[ "$LABELS" == *"waiting on ironbank"* ]] || [[ "$LABELS" == *"waiting on rapidfort"* ]] || [[ "$LABELS" == *"helm-chart-only"* ]]; then
          echo "waiting=true" >> $GITHUB_OUTPUT
        else
          echo "waiting=false" >> $GITHUB_OUTPUT
        fi

    # Apply labels
    - name: Apply labels
      if: steps.process-branch.outputs.needs_comparison == 'true'
      shell: bash
      env:
        GH_TOKEN: ${{ inputs.github_token }}
      run: |
        LABELS="${{ steps.compare.outputs.labels }}"
        if [[ -n "$LABELS" ]]; then
          echo "New labels to apply: $LABELS"

          # Get current labels on the PR
          CURRENT_LABELS=$(gh pr view ${{ github.event.pull_request.number }} --json labels --jq '.labels[].name' | tr '\n' ' ')
          echo "Current labels: $CURRENT_LABELS"

          # Define the managed labels we care about
          MANAGED_LABELS=("waiting on ironbank" "waiting on rapidfort" "needs-review" "helm-chart-only" "major-helm-update" "major-image-update")

          # Remove labels that are currently on the PR but not in the new set
          for LABEL in "${MANAGED_LABELS[@]}"; do
            if [[ "$CURRENT_LABELS" == *"$LABEL"* ]] && [[ "$LABELS" != *"$LABEL"* ]]; then
              echo "Removing outdated label: $LABEL"
              gh pr edit ${{ github.event.pull_request.number }} --remove-label "$LABEL" || true
            fi
          done

          # Add the new labels
          gh pr edit ${{ github.event.pull_request.number }} --add-label "$LABELS"
        fi

    # Fail if waiting on images
    - name: Fail if waiting on images or helm update only
      if: steps.compare.outputs.waiting == 'true'
      shell: bash
      run: |
        echo "PR is waiting on image updates or only contains a helm chart update. Failing job."
        exit 1
