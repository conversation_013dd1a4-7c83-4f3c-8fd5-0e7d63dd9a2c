<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" scale="1" border="0" version="26.1.0">
  <diagram id="C5RBs43oDa-KdzZeNtuy" name="Page-1">
    <mxGraphModel dx="837" dy="1616" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="0" pageScale="1" pageWidth="827" pageHeight="1169" background="light-dark(#FFFFFF,#111827)" math="0" shadow="0">
      <root>
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="EmsiOr3HLBwYRn2PYn47-3" value="Service CreatedOrUpdated" style="locked=1;" parent="WIyWlLk6GJQsqaUBKTNV-0" visible="0" />
        <mxCell id="EmsiOr3HLBwYRn2PYn47-4" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#24B0FF,#24B0FF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-3" vertex="1">
          <mxGeometry x="5254" y="-240" width="710" height="1960" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-16" value="&lt;font style=&quot;color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0)); font-weight: normal;&quot;&gt;a.Service&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontFamily=Teko;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DTeko;fontSize=40;" parent="EmsiOr3HLBwYRn2PYn47-3" vertex="1">
          <mxGeometry x="5394" y="-227.99857142857144" width="435" height="40.714285714285715" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-17" value="" style="group;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-3" vertex="1" connectable="0">
          <mxGeometry x="5324" y="409" width="599.5" height="1300" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-75" value="" style="group;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" vertex="1" connectable="0">
          <mxGeometry x="-30.5" y="-580" width="630" height="1850" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-5" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#144A8F,#144A8F);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-75" vertex="1">
          <mxGeometry width="630" height="1850" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-15" value="&lt;font style=&quot;font-size: 26px; color: light-dark(rgb(255, 255, 255), rgb(255, 255, 255));&quot;&gt;&lt;b&gt;Reconcile When Created or Updated&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-75" vertex="1">
          <mxGeometry x="56.5" y="35.35" width="512.5" height="35.35" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-18" value="" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#9E9E9E,#9E9E9E);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" vertex="1">
          <mxGeometry width="569" height="660" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-19" value="&lt;font style=&quot;font-size: 16px; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;&lt;b&gt;updateApiServerCIDR(kind.Service, kind.EndpointSlice)&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" vertex="1">
          <mxGeometry x="27.18543999999997" y="10" width="514.63774" height="25.06" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-20" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" source="EmsiOr3HLBwYRn2PYn47-24" target="EmsiOr3HLBwYRn2PYn47-28" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="170.72975675675673" y="145" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-21" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];container=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-20" vertex="1" connectable="0">
          <mxGeometry x="-0.1129" y="1" relative="1" as="geometry">
            <mxPoint y="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-22" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" source="EmsiOr3HLBwYRn2PYn47-24" target="EmsiOr3HLBwYRn2PYn47-26" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="398.2682432432432" y="145" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-23" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];container=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-22" vertex="1" connectable="0">
          <mxGeometry x="-0.2952" y="2" relative="1" as="geometry">
            <mxPoint x="4" y="-7" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-24" value="Is CIDR&amp;nbsp;&lt;div&gt;Statically&amp;nbsp;&lt;/div&gt;&lt;div&gt;Defined&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" vertex="1">
          <mxGeometry x="227.61437837837832" y="70" width="113.76924324324322" height="100" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-25" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" source="EmsiOr3HLBwYRn2PYn47-26" target="EmsiOr3HLBwYRn2PYn47-29" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-26" value="Construct CIDRs From Endpoints" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" vertex="1">
          <mxGeometry x="322.42208108108116" y="200" width="151.69232432432432" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-27" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" source="EmsiOr3HLBwYRn2PYn47-28" target="EmsiOr3HLBwYRn2PYn47-29" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-28" value="Use Static CIDR String" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" vertex="1">
          <mxGeometry x="94.88359459459457" y="200" width="151.69232432432432" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-29" value="" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" vertex="1">
          <mxGeometry x="170.72975675675673" y="310" width="227.53848648648645" height="230" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-30" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" source="EmsiOr3HLBwYRn2PYn47-31" target="EmsiOr3HLBwYRn2PYn47-33" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-31" value="For Each Endpoint in EndpointSlice" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" vertex="1">
          <mxGeometry x="202.3323243243243" y="360" width="164.33335135135133" height="40" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-32" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" source="EmsiOr3HLBwYRn2PYn47-33" target="EmsiOr3HLBwYRn2PYn47-34" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-33" value="Extract IP Addresses and Append &quot;/32&quot;" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" vertex="1">
          <mxGeometry x="202.3323243243243" y="420" width="164.33335135135133" height="40" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-34" value="Add API Server Cluster IP to CIDR List" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" vertex="1">
          <mxGeometry x="202.3323243243243" y="480" width="164.33335135135133" height="40" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-35" value="&lt;font style=&quot;color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;Generate CIDR List&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" vertex="1">
          <mxGeometry x="189.69129729729718" y="320" width="189.61540540540537" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-36" value="Convert CIDRs to V1NetworkPolicyPeer Objects" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" vertex="1">
          <mxGeometry x="164.40924324324317" y="560" width="240.1795135135135" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-37" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-17" source="EmsiOr3HLBwYRn2PYn47-29" target="EmsiOr3HLBwYRn2PYn47-36" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-39" value="" style="group;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-3" vertex="1" connectable="0">
          <mxGeometry x="5343.5" y="1109" width="530" height="531" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-40" value="" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#9E9E9E,#9E9E9E);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-39" vertex="1">
          <mxGeometry width="530" height="531" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-41" value="&lt;font style=&quot;font-size: 16px; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;&lt;b&gt;updateKubeAPINetworkPolicies(V1NetworkPolicyPeer[])&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-39" vertex="1">
          <mxGeometry x="42.5" y="11" width="445" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-42" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-39" source="EmsiOr3HLBwYRn2PYn47-45" target="EmsiOr3HLBwYRn2PYn47-47" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-43" value="Failure" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-42" vertex="1" connectable="0">
          <mxGeometry x="-0.2463" y="-1" relative="1" as="geometry">
            <mxPoint x="12" y="-11" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-44" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-39" source="EmsiOr3HLBwYRn2PYn47-45" target="EmsiOr3HLBwYRn2PYn47-49" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-45" value="Fetch Kubernetes Network Policies" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-39" vertex="1">
          <mxGeometry x="41.25" y="101" width="205" height="40" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-46" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=1;entryDx=0;entryDy=0;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-39" source="EmsiOr3HLBwYRn2PYn47-47" target="EmsiOr3HLBwYRn2PYn47-45" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="194.75" y="141" as="targetPoint" />
            <Array as="points">
              <mxPoint x="433.75" y="171" />
              <mxPoint x="245.75" y="171" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-47" value="Log Warning, Wait, Retry ( max 5 times)" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-39" vertex="1">
          <mxGeometry x="313.75" y="101" width="175" height="40" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-48" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-39" source="EmsiOr3HLBwYRn2PYn47-49" target="EmsiOr3HLBwYRn2PYn47-52" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-49" value="For Each NetworkPolicy" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-39" vertex="1">
          <mxGeometry x="68.75" y="171" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-50" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-39" source="EmsiOr3HLBwYRn2PYn47-52" target="EmsiOr3HLBwYRn2PYn47-53" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-51" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-50" vertex="1" connectable="0">
          <mxGeometry x="-0.3666" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-52" value="If existing&lt;div&gt;egress/ingress rules&lt;/div&gt;&lt;div&gt;are different from&lt;/div&gt;&lt;div&gt;new peers&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-39" vertex="1">
          <mxGeometry x="71.88" y="231" width="143.75" height="141" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-53" value="Update NetworkPolicy With&lt;div&gt;New Rules&lt;/div&gt;" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-39" vertex="1">
          <mxGeometry x="53.75" y="420" width="180" height="41" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-72" value="" style="group;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-3" vertex="1" connectable="0">
          <mxGeometry x="5328.5" y="-81" width="560" height="450" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-8" value="" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#9E9E9E,#9E9E9E);container=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-72" vertex="1">
          <mxGeometry width="560" height="450" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-9" value="&lt;font style=&quot;font-size: 16px; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;&lt;b style=&quot;&quot;&gt;updateAPIServerCIDRFromService(kind.Service)&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-72" vertex="1">
          <mxGeometry x="18.918918918918962" width="522.1621621621622" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-10" value="Fetch Kubernetes Service Object to Update API Server CIDR" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-72" vertex="1">
          <mxGeometry x="35" y="300" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-11" value="Log Warning, Wait, Retry ( max 5 times)" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-72" vertex="1">
          <mxGeometry x="315" y="310" width="175" height="40" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-72" source="EmsiOr3HLBwYRn2PYn47-10" target="EmsiOr3HLBwYRn2PYn47-11" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-13" value="Failure" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];container=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-12" vertex="1" connectable="0">
          <mxGeometry x="-0.0759" y="2" relative="1" as="geometry">
            <mxPoint y="-8" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-14" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=1;entryDx=0;entryDy=0;curved=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-72" source="EmsiOr3HLBwYRn2PYn47-11" target="EmsiOr3HLBwYRn2PYn47-10" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="405" y="380" />
              <mxPoint x="255" y="380" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-57" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-72" source="EmsiOr3HLBwYRn2PYn47-55" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="130" y="300" as="targetPoint" />
            <Array as="points">
              <mxPoint x="130" y="300" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-58" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];container=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-57" vertex="1" connectable="0">
          <mxGeometry x="-0.7297" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-55" value="Is Static API&amp;nbsp;&lt;div&gt;CIDR Configured in UDSConfig&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-72" vertex="1">
          <mxGeometry x="65" y="60" width="130" height="120" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-66" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;curved=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-72" source="EmsiOr3HLBwYRn2PYn47-59" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="130" y="300" as="targetPoint" />
            <Array as="points">
              <mxPoint x="309" y="220" />
              <mxPoint x="130" y="220" />
              <mxPoint x="130" y="300" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-67" value="Success" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];container=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-66" vertex="1" connectable="0">
          <mxGeometry x="-0.836" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-68" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-72" source="EmsiOr3HLBwYRn2PYn47-59" target="EmsiOr3HLBwYRn2PYn47-62" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="465" y="135" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-70" value="Failure" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];container=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-68" vertex="1" connectable="0">
          <mxGeometry x="0.3831" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-59" value="Fetch Kubernetes Endpoint Slice" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-72" vertex="1">
          <mxGeometry x="255" y="105" width="215" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-60" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-72" source="EmsiOr3HLBwYRn2PYn47-55" target="EmsiOr3HLBwYRn2PYn47-59" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-61" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];container=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-60" vertex="1" connectable="0">
          <mxGeometry x="-0.1088" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-69" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;curved=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-72" source="EmsiOr3HLBwYRn2PYn47-62" target="EmsiOr3HLBwYRn2PYn47-59" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="363" y="220" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-62" value="Log Warning, Wait, Retry ( max 5 times)" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-72" vertex="1">
          <mxGeometry x="405" y="190" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-73" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-3" source="EmsiOr3HLBwYRn2PYn47-8" target="EmsiOr3HLBwYRn2PYn47-18" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-74" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-3" source="EmsiOr3HLBwYRn2PYn47-18" target="EmsiOr3HLBwYRn2PYn47-40" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <object label="Node Deleted" id="EmsiOr3HLBwYRn2PYn47-2">
          <mxCell style="locked=1;" parent="WIyWlLk6GJQsqaUBKTNV-0" visible="0" />
        </object>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-0" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#24B0FF,#24B0FF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4495" y="-415" width="710" height="2340" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#144A8F,#144A8F);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4535" y="-325" width="630" height="2210" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);" parent="EmsiOr3HLBwYRn2PYn47-2" source="oaF2RFQyBR-ehR7t-r5i-13" target="WwSa83Ogff-Oqx5X9af4-0" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-13" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#9E9E9E,#9E9E9E);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4585" y="142.5" width="530" height="340" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-14" value="&lt;font style=&quot;font-size: 16px; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;&lt;b style=&quot;&quot;&gt;buildNodePolicies(string[])&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4737.5" y="157.5" width="225" height="30" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-15" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="oaF2RFQyBR-ehR7t-r5i-16" target="oaF2RFQyBR-ehR7t-r5i-21" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-16" value="&lt;div&gt;For Each IP in List&lt;/div&gt;" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4700" y="207.5" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-17" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="oaF2RFQyBR-ehR7t-r5i-21" target="oaF2RFQyBR-ehR7t-r5i-22" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-18" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="oaF2RFQyBR-ehR7t-r5i-17" vertex="1" connectable="0">
          <mxGeometry x="-0.0851" y="1" relative="1" as="geometry">
            <mxPoint y="-7" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-19" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="oaF2RFQyBR-ehR7t-r5i-21" target="oaF2RFQyBR-ehR7t-r5i-23" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-20" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="oaF2RFQyBR-ehR7t-r5i-19" vertex="1" connectable="0">
          <mxGeometry x="-0.1835" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-21" value="Does IP&amp;nbsp;&lt;div&gt;Include CIDR Notation &quot;/&quot;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4725" y="262.5" width="110" height="105" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-22" value="Append &quot;/32&quot; to IP" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4880" y="301.25" width="120" height="27.5" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-23" value="Create V1NetworkPolicyPeer Array with Formatted IPs" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4680" y="407.5" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-67" value="&lt;font style=&quot;color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;a.Node&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Teko;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DTeko;fontSize=40;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4686.12" y="-385" width="327.75" height="30" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-68" value="&lt;font style=&quot;font-size: 26px; color: light-dark(rgb(255, 255, 255), rgb(255, 255, 255));&quot;&gt;&lt;b&gt;Reconcile When Deleted&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4633.75" y="-305" width="412.5" height="30" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-69" value="" style="group;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1" connectable="0">
          <mxGeometry x="4585" y="-245" width="530" height="340" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-4" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#9E9E9E,#9E9E9E);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="oaF2RFQyBR-ehR7t-r5i-69" vertex="1">
          <mxGeometry width="530" height="340" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="oaF2RFQyBR-ehR7t-r5i-69" source="oaF2RFQyBR-ehR7t-r5i-6" target="oaF2RFQyBR-ehR7t-r5i-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-6" value="Extract Internal IP From Node Object" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="oaF2RFQyBR-ehR7t-r5i-69" vertex="1">
          <mxGeometry x="155" y="75" width="220" height="30" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="oaF2RFQyBR-ehR7t-r5i-69" source="oaF2RFQyBR-ehR7t-r5i-9" target="oaF2RFQyBR-ehR7t-r5i-10" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-8" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="oaF2RFQyBR-ehR7t-r5i-7" vertex="1" connectable="0">
          <mxGeometry x="-0.1111" y="1" relative="1" as="geometry">
            <mxPoint x="-1" y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-9" value="Is IP is&lt;div&gt;&amp;nbsp;present&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="oaF2RFQyBR-ehR7t-r5i-69" vertex="1">
          <mxGeometry x="225" y="135" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-10" value="Delete Node IP From Set" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="oaF2RFQyBR-ehR7t-r5i-69" vertex="1">
          <mxGeometry x="205" y="265" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-11" value="&lt;font style=&quot;font-size: 16px; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;&lt;b&gt;updateKubeNodesFromDelete(kind.Node)&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="oaF2RFQyBR-ehR7t-r5i-69" vertex="1">
          <mxGeometry x="82" y="15" width="366" height="30" as="geometry" />
        </mxCell>
        <mxCell id="oaF2RFQyBR-ehR7t-r5i-70" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="oaF2RFQyBR-ehR7t-r5i-4" target="oaF2RFQyBR-ehR7t-r5i-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-0" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#9E9E9E,#9E9E9E);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4584.99" y="530" width="530" height="1300" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-1" value="&lt;font style=&quot;font-size: 16px; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;&lt;b&gt;updateKubeNodesNetworkPolicies()&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4695.49" y="550" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="WwSa83Ogff-Oqx5X9af4-4" target="WwSa83Ogff-Oqx5X9af4-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-3" value="Success" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WwSa83Ogff-Oqx5X9af4-2" vertex="1" connectable="0">
          <mxGeometry x="-0.0381" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-4" value="Fetch All Network Policies with &quot;uds/generated=KubeNodes&quot; Label" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4642.49" y="610" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="WwSa83Ogff-Oqx5X9af4-6" target="WwSa83Ogff-Oqx5X9af4-11" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-6" value="For Each Network Policy" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4673.99" y="710" width="157" height="30" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="WwSa83Ogff-Oqx5X9af4-11" target="WwSa83Ogff-Oqx5X9af4-12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-8" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WwSa83Ogff-Oqx5X9af4-7" vertex="1" connectable="0">
          <mxGeometry x="-0.1846" y="-1" relative="1" as="geometry">
            <mxPoint x="-1" y="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-9" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="WwSa83Ogff-Oqx5X9af4-11" target="WwSa83Ogff-Oqx5X9af4-17" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-10" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WwSa83Ogff-Oqx5X9af4-9" vertex="1" connectable="0">
          <mxGeometry x="-0.2512" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-11" value="Is the NetworkPolicy Spec&amp;nbsp;&lt;div&gt;Defined&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4694.99" y="770" width="115.5" height="100" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-12" value="Log Warning, Skip to Next Policy" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4858.49" y="800" width="196.5" height="40" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="WwSa83Ogff-Oqx5X9af4-17" target="WwSa83Ogff-Oqx5X9af4-18" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-14" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WwSa83Ogff-Oqx5X9af4-13" vertex="1" connectable="0">
          <mxGeometry x="-0.1644" y="3" relative="1" as="geometry">
            <mxPoint y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-15" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="WwSa83Ogff-Oqx5X9af4-17" target="WwSa83Ogff-Oqx5X9af4-23" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-16" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WwSa83Ogff-Oqx5X9af4-15" vertex="1" connectable="0">
          <mxGeometry x="-0.1365" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-17" value="Are the NetworkPolicy Egress Rules Defined" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4692.24" y="952.5" width="120.5" height="110" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-18" value="Initialize Egress to Empty" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4864.99" y="988.75" width="190" height="37.5" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-19" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="WwSa83Ogff-Oqx5X9af4-23" target="WwSa83Ogff-Oqx5X9af4-24" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-20" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WwSa83Ogff-Oqx5X9af4-19" vertex="1" connectable="0">
          <mxGeometry x="-0.0088" y="2" relative="1" as="geometry">
            <mxPoint x="-6" y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-21" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="WwSa83Ogff-Oqx5X9af4-23" target="WwSa83Ogff-Oqx5X9af4-29" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-22" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WwSa83Ogff-Oqx5X9af4-21" vertex="1" connectable="0">
          <mxGeometry x="-0.019" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-23" value="Do New&amp;nbsp;&lt;div&gt;Node&amp;nbsp;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;Egress CIDRs Match Existing&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;Egress&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;CIDRs&lt;/span&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4677.99" y="1120" width="149" height="120" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-24" value="Set Update Required and Update Egress CIDRs" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4868.49" y="1150" width="190" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-25" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="WwSa83Ogff-Oqx5X9af4-29" target="WwSa83Ogff-Oqx5X9af4-30" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-26" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WwSa83Ogff-Oqx5X9af4-25" vertex="1" connectable="0">
          <mxGeometry x="-0.1549" y="2" relative="1" as="geometry">
            <mxPoint x="4" y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="WwSa83Ogff-Oqx5X9af4-29" target="WwSa83Ogff-Oqx5X9af4-35" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-28" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WwSa83Ogff-Oqx5X9af4-27" vertex="1" connectable="0">
          <mxGeometry x="-0.2994" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-29" value="Are the NetworkPolicy Ingress Rules Defined" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4694.99" y="1280" width="117.75" height="110" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-30" value="Initialize Ingress to Empty" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4874.99" y="1310" width="190" height="40" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-31" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="WwSa83Ogff-Oqx5X9af4-35" target="WwSa83Ogff-Oqx5X9af4-36" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-32" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WwSa83Ogff-Oqx5X9af4-31" vertex="1" connectable="0">
          <mxGeometry x="-0.0874" y="2" relative="1" as="geometry">
            <mxPoint y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-33" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="WwSa83Ogff-Oqx5X9af4-35" target="WwSa83Ogff-Oqx5X9af4-39" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-34" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WwSa83Ogff-Oqx5X9af4-33" vertex="1" connectable="0">
          <mxGeometry x="-0.6114" y="-2" relative="1" as="geometry">
            <mxPoint y="2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-35" value="Do New&amp;nbsp;&lt;div&gt;Node&amp;nbsp;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;Ingress CIDRs Match Existing&amp;nbsp;&lt;/span&gt;&lt;div&gt;Ingress&amp;nbsp;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;CIDRs&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4679.37" y="1430" width="149" height="120" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-36" value="Set Update Required and Update Ingress CIDRs" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4878.49" y="1460" width="190" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-37" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="WwSa83Ogff-Oqx5X9af4-39" target="WwSa83Ogff-Oqx5X9af4-42" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-38" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WwSa83Ogff-Oqx5X9af4-37" vertex="1" connectable="0">
          <mxGeometry x="-0.0857" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-39" value="Is Update Required" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4707.62" y="1580" width="92.5" height="90" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-40" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" source="WwSa83Ogff-Oqx5X9af4-42" target="WwSa83Ogff-Oqx5X9af4-43" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-41" value="Failure" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WwSa83Ogff-Oqx5X9af4-40" vertex="1" connectable="0">
          <mxGeometry x="-0.4129" y="-2" relative="1" as="geometry">
            <mxPoint x="10" y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-42" value="Remove managedFields and Apply Updated Network Policy" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4669.49" y="1710" width="166" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WwSa83Ogff-Oqx5X9af4-43" value="Log Error, Throw Exception" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EmsiOr3HLBwYRn2PYn47-2" vertex="1">
          <mxGeometry x="4893.49" y="1725" width="175" height="30" as="geometry" />
        </mxCell>
        <object label="Node CreatedOrUpdated" id="tMXGoEb_9TJprHVc1VYA-122">
          <mxCell style="locked=1;" parent="WIyWlLk6GJQsqaUBKTNV-0" visible="0" />
        </object>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-92" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#24B0FF,#24B0FF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3730" y="-420" width="710" height="2340" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-91" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#144A8F,#144A8F);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3770" y="-330" width="630" height="2210" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-74" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#9E9E9E,#9E9E9E);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3820" y="520" width="530" height="1300" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-97" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=light-dark(#000000,#000000);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-9" target="GJTeJAITUz0S9s2f7WXq-87" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-9" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#9E9E9E,#9E9E9E);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3820" y="-255" width="530" height="340" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-0" target="GJTeJAITUz0S9s2f7WXq-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-0" value="Extract Internal IP From Node Object" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3975" y="-180" width="220" height="30" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-1" target="GJTeJAITUz0S9s2f7WXq-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-5" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-4" vertex="1" connectable="0">
          <mxGeometry x="-0.1111" y="1" relative="1" as="geometry">
            <mxPoint x="-1" y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-1" value="Is IP is&lt;div&gt;&amp;nbsp;present&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="4045" y="-120" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-3" value="Add Node IP to Set" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="4025" y="10" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-85" value="&lt;font style=&quot;font-size: 16px; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;&lt;b&gt;updateKubeNodesFromCreateUpdate(kind.Node)&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3902" y="-240" width="366" height="30" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-96" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=light-dark(#000000,#000000);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-87" target="GJTeJAITUz0S9s2f7WXq-74" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-87" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#9E9E9E,#9E9E9E);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3820" y="137.5" width="530" height="340" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-83" value="&lt;font style=&quot;font-size: 16px; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;&lt;b&gt;buildNodePolicies(string[])&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3972.5" y="152.5" width="225" height="30" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-76" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-6" target="GJTeJAITUz0S9s2f7WXq-75" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-6" value="&lt;div&gt;For Each IP in List&lt;/div&gt;" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3935" y="202.5" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-78" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-75" target="GJTeJAITUz0S9s2f7WXq-77" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-79" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-78" vertex="1" connectable="0">
          <mxGeometry x="-0.0851" y="1" relative="1" as="geometry">
            <mxPoint y="-7" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-81" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-75" target="GJTeJAITUz0S9s2f7WXq-80" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-82" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-81" vertex="1" connectable="0">
          <mxGeometry x="-0.1835" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-75" value="Does IP&amp;nbsp;&lt;div&gt;Include CIDR Notation &quot;/&quot;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3960" y="257.5" width="110" height="105" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-77" value="Append &quot;/32&quot; to IP" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="4115" y="296.25" width="120" height="27.5" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-80" value="Create V1NetworkPolicyPeer Array with Formatted IPs" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3922.5" y="402.5" width="185" height="60" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-89" value="&lt;font style=&quot;font-size: 16px; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;&lt;b&gt;updateKubeNodesNetworkPolicies()&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3930.5" y="540" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-13" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-10" target="GJTeJAITUz0S9s2f7WXq-12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-27" value="Success" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-13" vertex="1" connectable="0">
          <mxGeometry x="-0.0381" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-10" value="Fetch All Network Policies with &quot;uds/generated=KubeNodes&quot; Label" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3877.5" y="600" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-29" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-12" target="GJTeJAITUz0S9s2f7WXq-28" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-12" value="For Each Network Policy" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3909" y="700" width="157" height="30" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-31" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-28" target="GJTeJAITUz0S9s2f7WXq-30" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-32" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-31" vertex="1" connectable="0">
          <mxGeometry x="-0.1846" y="-1" relative="1" as="geometry">
            <mxPoint x="-1" y="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-34" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-28" target="GJTeJAITUz0S9s2f7WXq-33" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-59" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-34" vertex="1" connectable="0">
          <mxGeometry x="-0.2512" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-28" value="Is the NetworkPolicy Spec&amp;nbsp;&lt;div&gt;Defined&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3930" y="760" width="115.5" height="100" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-30" value="Log Warning, Skip to Next Policy" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="4093.5" y="790" width="196.5" height="40" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-33" target="GJTeJAITUz0S9s2f7WXq-35" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-42" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-37" vertex="1" connectable="0">
          <mxGeometry x="-0.1644" y="3" relative="1" as="geometry">
            <mxPoint y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-39" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-33" target="GJTeJAITUz0S9s2f7WXq-38" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-44" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-39" vertex="1" connectable="0">
          <mxGeometry x="-0.1365" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-33" value="Are the NetworkPolicy Egress Rules Defined" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3927.25" y="942.5" width="120.5" height="110" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-35" value="Initialize Egress to Empty" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="4100" y="978.75" width="190" height="37.5" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-41" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-38" target="GJTeJAITUz0S9s2f7WXq-40" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-43" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-41" vertex="1" connectable="0">
          <mxGeometry x="-0.0088" y="2" relative="1" as="geometry">
            <mxPoint x="-6" y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-46" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-38" target="GJTeJAITUz0S9s2f7WXq-45" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-47" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-46" vertex="1" connectable="0">
          <mxGeometry x="-0.019" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-38" value="Do New&amp;nbsp;&lt;div&gt;Node&amp;nbsp;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;Egress CIDRs Match Existing&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;Egress&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;CIDRs&lt;/span&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3913" y="1110" width="149" height="120" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-40" value="Set Update Required and Update Egress CIDRs" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="4103.5" y="1140" width="190" height="60" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-49" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-45" target="GJTeJAITUz0S9s2f7WXq-48" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-50" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-49" vertex="1" connectable="0">
          <mxGeometry x="-0.1549" y="2" relative="1" as="geometry">
            <mxPoint x="4" y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-54" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-45" target="GJTeJAITUz0S9s2f7WXq-53" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-58" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-54" vertex="1" connectable="0">
          <mxGeometry x="-0.2994" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-45" value="Are the NetworkPolicy Ingress Rules Defined" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3930" y="1270" width="117.75" height="110" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-48" value="Initialize Ingress to Empty" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="4110" y="1300" width="190" height="40" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-56" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-53" target="GJTeJAITUz0S9s2f7WXq-55" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-57" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-56" vertex="1" connectable="0">
          <mxGeometry x="-0.0874" y="2" relative="1" as="geometry">
            <mxPoint y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-63" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-53" target="GJTeJAITUz0S9s2f7WXq-62" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-64" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-63" vertex="1" connectable="0">
          <mxGeometry x="-0.6114" y="-2" relative="1" as="geometry">
            <mxPoint y="2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-53" value="Do New&amp;nbsp;&lt;div&gt;Node&amp;nbsp;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;Ingress CIDRs Match Existing&amp;nbsp;&lt;/span&gt;&lt;div&gt;Ingress&amp;nbsp;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;CIDRs&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3914.38" y="1420" width="149" height="120" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-55" value="Set Update Required and Update Ingress CIDRs" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="4113.5" y="1450" width="190" height="60" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-66" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-62" target="GJTeJAITUz0S9s2f7WXq-65" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-67" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-66" vertex="1" connectable="0">
          <mxGeometry x="-0.0857" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-62" value="Is Update Required" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3942.63" y="1570" width="92.5" height="90" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-69" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" source="GJTeJAITUz0S9s2f7WXq-65" target="GJTeJAITUz0S9s2f7WXq-68" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-70" value="Failure" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="GJTeJAITUz0S9s2f7WXq-69" vertex="1" connectable="0">
          <mxGeometry x="-0.4129" y="-2" relative="1" as="geometry">
            <mxPoint x="10" y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-65" value="Remove managedFields and Apply Updated Network Policy" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3904.5" y="1700" width="166" height="60" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-68" value="Log Error, Throw Exception" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="4128.5" y="1715" width="175" height="30" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-93" value="&lt;font style=&quot;color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;a.Node&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Teko;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DTeko;fontSize=40;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3921.12" y="-390" width="327.75" height="30" as="geometry" />
        </mxCell>
        <mxCell id="GJTeJAITUz0S9s2f7WXq-95" value="&lt;font style=&quot;font-size: 26px; color: light-dark(rgb(255, 255, 255), rgb(255, 255, 255));&quot;&gt;&lt;b&gt;Reconcile When Created or Updated&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-122" vertex="1">
          <mxGeometry x="3840" y="-310" width="481.25" height="30" as="geometry" />
        </mxCell>
        <object label="EndpointSlice CreatedOrUpdated" id="tMXGoEb_9TJprHVc1VYA-123">
          <mxCell style="locked=1;" parent="WIyWlLk6GJQsqaUBKTNV-0" visible="0" />
        </object>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-71" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#24B0FF,#24B0FF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-123" vertex="1">
          <mxGeometry x="2903" y="-136" width="770" height="1680" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-70" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#144A8F,#144A8F);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-123" vertex="1">
          <mxGeometry x="2973" y="-56" width="630" height="1570" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-64" value="" style="group;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-123" vertex="1" connectable="0">
          <mxGeometry x="3007" y="24" width="560" height="140" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-63" value="" style="group;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-64" vertex="1" connectable="0">
          <mxGeometry width="560" height="140" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-61" value="" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#9E9E9E,#9E9E9E);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-63" vertex="1">
          <mxGeometry width="560" height="140" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-62" value="&lt;font style=&quot;font-size: 16px; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;&lt;b style=&quot;&quot;&gt;updateAPIServerCIDRFromEndpointSlice(kind.EndpointSlice)&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-63" vertex="1">
          <mxGeometry x="18.91891891891892" width="522.1621621621622" height="30" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-0" value="Fetch Kubernetes Service Object to Update API Server CIDR" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-64" vertex="1">
          <mxGeometry x="70" y="40" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-57" value="Log Warning, Wait, Retry ( max 5 times)" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-64" vertex="1">
          <mxGeometry x="350" y="50" width="175" height="40" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-58" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-64" source="SbLU_7_F6Z1DZpxcM8TY-0" target="SbLU_7_F6Z1DZpxcM8TY-57" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-59" value="Failure" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-58" vertex="1" connectable="0">
          <mxGeometry x="-0.0759" y="2" relative="1" as="geometry">
            <mxPoint y="-8" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-60" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=1;entryDx=0;entryDy=0;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-64" source="SbLU_7_F6Z1DZpxcM8TY-57" target="SbLU_7_F6Z1DZpxcM8TY-0" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="440" y="120" />
              <mxPoint x="290" y="120" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-72" value="&lt;font style=&quot;font-size: 26px; color: light-dark(rgb(255, 255, 255), rgb(255, 255, 255));&quot;&gt;&lt;b&gt;Reconcile When Created or Updated&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-123" vertex="1">
          <mxGeometry x="3040" y="-36" width="495.5" height="30" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-73" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Teko&quot; face=&quot;Teko&quot; style=&quot;color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0)); font-size: 40px; font-weight: normal;&quot;&gt;a.EndpointSlice&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-123" vertex="1">
          <mxGeometry x="3053" y="-110" width="435" height="30" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-74" value="" style="group;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-123" vertex="1" connectable="0">
          <mxGeometry x="3002" y="214" width="569" height="660" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-27" value="" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#9E9E9E,#9E9E9E);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" vertex="1">
          <mxGeometry width="569" height="660" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-28" value="&lt;font style=&quot;font-size: 16px; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;&lt;b&gt;updateApiServerCIDR(kind.Service, kind.EndpointSlice)&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" vertex="1">
          <mxGeometry x="27.18543999999997" y="10" width="514.63774" height="25.06" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-13" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" source="SbLU_7_F6Z1DZpxcM8TY-4" target="SbLU_7_F6Z1DZpxcM8TY-9" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="170.72975675675673" y="145" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-15" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];container=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-13" vertex="1" connectable="0">
          <mxGeometry x="-0.1129" y="1" relative="1" as="geometry">
            <mxPoint y="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-14" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" source="SbLU_7_F6Z1DZpxcM8TY-4" target="SbLU_7_F6Z1DZpxcM8TY-6" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="398.2682432432432" y="145" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-16" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];container=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-14" vertex="1" connectable="0">
          <mxGeometry x="-0.2952" y="2" relative="1" as="geometry">
            <mxPoint x="4" y="-7" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-4" value="Is CIDR&amp;nbsp;&lt;div&gt;Statically&amp;nbsp;&lt;/div&gt;&lt;div&gt;Defined&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" vertex="1">
          <mxGeometry x="227.61437837837832" y="70" width="113.76924324324322" height="100" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-19" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" source="SbLU_7_F6Z1DZpxcM8TY-6" target="SbLU_7_F6Z1DZpxcM8TY-17" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-6" value="Construct CIDRs From Endpoints" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" vertex="1">
          <mxGeometry x="322.42208108108116" y="200" width="151.69232432432432" height="60" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-18" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" source="SbLU_7_F6Z1DZpxcM8TY-9" target="SbLU_7_F6Z1DZpxcM8TY-17" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-9" value="Use Static CIDR String" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" vertex="1">
          <mxGeometry x="94.88359459459457" y="200" width="151.69232432432432" height="60" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-17" value="" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" vertex="1">
          <mxGeometry x="170.72975675675673" y="310" width="227.53848648648645" height="230" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-23" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" source="SbLU_7_F6Z1DZpxcM8TY-20" target="SbLU_7_F6Z1DZpxcM8TY-22" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-20" value="For Each Endpoint in EndpointSlice" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" vertex="1">
          <mxGeometry x="202.3323243243243" y="360" width="164.33335135135133" height="40" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-25" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" source="SbLU_7_F6Z1DZpxcM8TY-22" target="SbLU_7_F6Z1DZpxcM8TY-24" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-22" value="Extract IP Addresses and Append &quot;/32&quot;" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" vertex="1">
          <mxGeometry x="202.3323243243243" y="420" width="164.33335135135133" height="40" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-24" value="Add API Server Cluster IP to CIDR List" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" vertex="1">
          <mxGeometry x="202.3323243243243" y="480" width="164.33335135135133" height="40" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-26" value="Generate CIDR List" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" vertex="1">
          <mxGeometry x="189.69129729729718" y="320" width="189.61540540540537" height="30" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-31" value="Convert CIDRs to V1NetworkPolicyPeer Objects" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" vertex="1">
          <mxGeometry x="164.40924324324317" y="560" width="240.1795135135135" height="60" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-32" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-74" source="SbLU_7_F6Z1DZpxcM8TY-17" target="SbLU_7_F6Z1DZpxcM8TY-31" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-76" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-123" source="SbLU_7_F6Z1DZpxcM8TY-61" target="SbLU_7_F6Z1DZpxcM8TY-27" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-77" value="" style="group;fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-123" vertex="1" connectable="0">
          <mxGeometry x="3022" y="934" width="530" height="531" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-33" value="" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#9E9E9E,#9E9E9E);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-77" vertex="1">
          <mxGeometry width="530" height="531" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-34" value="&lt;font style=&quot;font-size: 16px; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;&lt;b&gt;updateKubeAPINetworkPolicies(V1NetworkPolicyPeer[])&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-77" vertex="1">
          <mxGeometry x="42.5" y="11" width="445" height="30" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-38" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-77" source="SbLU_7_F6Z1DZpxcM8TY-35" target="SbLU_7_F6Z1DZpxcM8TY-37" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-39" value="Failure" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-38" vertex="1" connectable="0">
          <mxGeometry x="-0.2463" y="-1" relative="1" as="geometry">
            <mxPoint x="12" y="-11" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-42" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-77" source="SbLU_7_F6Z1DZpxcM8TY-35" target="SbLU_7_F6Z1DZpxcM8TY-41" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-35" value="Fetch Kubernetes Network Policies" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-77" vertex="1">
          <mxGeometry x="41.25" y="101" width="205" height="40" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-40" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=1;entryDx=0;entryDy=0;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-77" source="SbLU_7_F6Z1DZpxcM8TY-37" target="SbLU_7_F6Z1DZpxcM8TY-35" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="194.75" y="141" as="targetPoint" />
            <Array as="points">
              <mxPoint x="433.75" y="171" />
              <mxPoint x="245.75" y="171" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-37" value="Log Warning, Wait, Retry ( max 5 times)" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-77" vertex="1">
          <mxGeometry x="313.75" y="101" width="175" height="40" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-49" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-77" source="SbLU_7_F6Z1DZpxcM8TY-41" target="SbLU_7_F6Z1DZpxcM8TY-48" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-41" value="For Each NetworkPolicy" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-77" vertex="1">
          <mxGeometry x="68.75" y="171" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-51" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-77" source="SbLU_7_F6Z1DZpxcM8TY-48" target="SbLU_7_F6Z1DZpxcM8TY-50" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-52" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-51" vertex="1" connectable="0">
          <mxGeometry x="-0.3666" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-48" value="If existing&lt;div&gt;egress/ingress&amp;nbsp;&lt;/div&gt;&lt;div&gt;rules&amp;nbsp;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;are&amp;nbsp;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;different from&lt;/span&gt;&lt;/div&gt;&lt;div&gt;new peers&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-77" vertex="1">
          <mxGeometry x="73.75" y="231" width="140" height="140" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-50" value="Update NetworkPolicy With&lt;div&gt;New Rules&lt;/div&gt;" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="SbLU_7_F6Z1DZpxcM8TY-77" vertex="1">
          <mxGeometry x="53.75" y="420" width="180" height="41" as="geometry" />
        </mxCell>
        <mxCell id="SbLU_7_F6Z1DZpxcM8TY-78" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-123" source="SbLU_7_F6Z1DZpxcM8TY-27" target="SbLU_7_F6Z1DZpxcM8TY-33" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <object label="UDS Exemption CreatedOrUpdated" id="tMXGoEb_9TJprHVc1VYA-124">
          <mxCell style="locked=1;" parent="WIyWlLk6GJQsqaUBKTNV-0" visible="0" />
        </object>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-0" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#24B0FF,#24B0FF);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;strokeColor=light-dark(#000000,#000000);" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2270" y="20" width="510" height="1240" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-35" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#144A8F,#144A8F);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;strokeColor=light-dark(#000000,#000000);" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2290" y="74" width="470" height="1166" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-32" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#9E9E9E,#9E9E9E);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2330" y="144" width="390" height="1076" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" source="UhoujK6dF2gCW-Bu2JzR-0" target="h0-33P9cP_2n5sfKz10X-0" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UhoujK6dF2gCW-Bu2JzR-0" value="Retrieve Exemption Data from Request" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2365" y="220" width="249" height="40" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" source="h0-33P9cP_2n5sfKz10X-0" target="h0-33P9cP_2n5sfKz10X-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-19" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="h0-33P9cP_2n5sfKz10X-3" vertex="1" connectable="0">
          <mxGeometry x="-0.3" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-0" value="is Allow All Namespaces Exemptions&lt;div&gt;&amp;nbsp;Enabled&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2427" y="280" width="126" height="120" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" source="h0-33P9cP_2n5sfKz10X-2" target="h0-33P9cP_2n5sfKz10X-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-2" value="For Each Exemption in Exemptions List" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2422" y="440" width="136" height="60" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" source="h0-33P9cP_2n5sfKz10X-4" target="h0-33P9cP_2n5sfKz10X-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-4" value="For Each Policy in Exemption" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2422" y="520" width="136" height="60" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-9" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" source="h0-33P9cP_2n5sfKz10X-6" target="h0-33P9cP_2n5sfKz10X-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-18" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="h0-33P9cP_2n5sfKz10X-9" vertex="1" connectable="0">
          <mxGeometry x="-0.12" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-24" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;" parent="tMXGoEb_9TJprHVc1VYA-124" source="h0-33P9cP_2n5sfKz10X-6" target="z9HT1GgTeo10kBzWfQ4S-0" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2606" y="665" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-25" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="h0-33P9cP_2n5sfKz10X-24" vertex="1" connectable="0">
          <mxGeometry x="-0.2063" y="1" relative="1" as="geometry">
            <mxPoint x="-51" y="-151" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-6" value="is Policy&amp;nbsp;&lt;div&gt;Compatible with Matcher Kind&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2425" y="610" width="128" height="108.5" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-11" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" source="h0-33P9cP_2n5sfKz10X-8" target="h0-33P9cP_2n5sfKz10X-10" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-8" value="For Each Exemption in Exemptions List" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2422" y="770" width="136" height="60" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-27" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;" parent="tMXGoEb_9TJprHVc1VYA-124" source="h0-33P9cP_2n5sfKz10X-10" target="z9HT1GgTeo10kBzWfQ4S-0" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2606" y="912.8000000000002" as="targetPoint" />
            <Array as="points">
              <mxPoint x="2635" y="913" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-28" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="h0-33P9cP_2n5sfKz10X-27" vertex="1" connectable="0">
          <mxGeometry x="-0.1626" y="1" relative="1" as="geometry">
            <mxPoint x="-51" y="-70" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="d8Z4GK2wHJgT9erUlwGJ-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);" parent="tMXGoEb_9TJprHVc1VYA-124" source="h0-33P9cP_2n5sfKz10X-10" target="h0-33P9cP_2n5sfKz10X-12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="d8Z4GK2wHJgT9erUlwGJ-8" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="d8Z4GK2wHJgT9erUlwGJ-5" vertex="1" connectable="0">
          <mxGeometry x="-0.2748" y="-2" relative="1" as="geometry">
            <mxPoint x="1" y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-10" value="Does&amp;nbsp;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;Matcher&lt;/span&gt;&lt;div&gt;&lt;div&gt;Name Contain leading/trailing&amp;nbsp;&lt;/div&gt;&lt;div&gt;Slashes&lt;/div&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2413.5" y="848.5" width="153" height="128.5" as="geometry" />
        </mxCell>
        <mxCell id="d8Z4GK2wHJgT9erUlwGJ-6" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;" parent="tMXGoEb_9TJprHVc1VYA-124" source="h0-33P9cP_2n5sfKz10X-12" target="z9HT1GgTeo10kBzWfQ4S-0" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2606" y="1068.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="d8Z4GK2wHJgT9erUlwGJ-9" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="d8Z4GK2wHJgT9erUlwGJ-6" vertex="1" connectable="0">
          <mxGeometry x="-0.0965" y="1" relative="1" as="geometry">
            <mxPoint x="-42" y="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="d8Z4GK2wHJgT9erUlwGJ-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);" parent="tMXGoEb_9TJprHVc1VYA-124" source="h0-33P9cP_2n5sfKz10X-12" target="h0-33P9cP_2n5sfKz10X-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="d8Z4GK2wHJgT9erUlwGJ-10" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="d8Z4GK2wHJgT9erUlwGJ-7" vertex="1" connectable="0">
          <mxGeometry x="-0.6903" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-12" value="Is Matcher&amp;nbsp;&lt;div&gt;Name a Valid&amp;nbsp;&lt;/div&gt;&lt;div&gt;Regular&amp;nbsp;&lt;/div&gt;&lt;div&gt;Expression&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2425" y="1008.5" width="130" height="121.5" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-14" value="Approve Request" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2430" y="1160" width="120" height="35" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-33" value="&lt;font style=&quot;font-size: 18px; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;exemptValidator(PeprValidateRequest)&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2355.63" y="170" width="348.75" height="30" as="geometry" />
        </mxCell>
        <mxCell id="h0-33P9cP_2n5sfKz10X-36" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;Validate When Created or Updated&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2291.25" y="100" width="467.5" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EmsiOr3HLBwYRn2PYn47-1" value="&lt;font style=&quot;color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;UDS Exemption&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Teko;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DTeko;fontSize=40;" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2385" y="40" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7Y3VQc__NgfHaYByrYpb-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.979;exitY=0.496;exitDx=0;exitDy=0;exitPerimeter=0;strokeColor=light-dark(#000000,#000000);entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;" parent="tMXGoEb_9TJprHVc1VYA-124" source="h0-33P9cP_2n5sfKz10X-0" target="z9HT1GgTeo10kBzWfQ4S-0" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2725" y="340" as="sourcePoint" />
            <mxPoint x="2606" y="340" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="7Y3VQc__NgfHaYByrYpb-7" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="7Y3VQc__NgfHaYByrYpb-6" vertex="1" connectable="0">
          <mxGeometry x="-0.4306" y="1" relative="1" as="geometry">
            <mxPoint x="-51" y="-176" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="z9HT1GgTeo10kBzWfQ4S-0" value="Deny Request" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="tMXGoEb_9TJprHVc1VYA-124" vertex="1">
          <mxGeometry x="2575" y="1160" width="120" height="35" as="geometry" />
        </mxCell>
        <object label="UDS Package CreatedOrUpdated" id="G6NHiv4yU3Q8GBfmJWzk-166">
          <mxCell style="locked=1;" parent="WIyWlLk6GJQsqaUBKTNV-0" />
        </object>
        <mxCell id="ALniYcZCjree9EDMam5--12" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=light-dark(#24B0FF,#24B0FF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="G6NHiv4yU3Q8GBfmJWzk-166" vertex="1">
          <mxGeometry x="1180" y="-331" width="1413" height="1561" as="geometry" />
        </mxCell>
        <mxCell id="ALniYcZCjree9EDMam5--10" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=light-dark(#144A8F,#144A8F);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="G6NHiv4yU3Q8GBfmJWzk-166" vertex="1">
          <mxGeometry x="1196.5" y="-288" width="1380" height="1498" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-345" value="&lt;h1&gt;&lt;font style=&quot;font-size: 40px; font-weight: normal;&quot;&gt;UDS Package&lt;/font&gt;&lt;/h1&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=light-dark(#000000,#000000);fontFamily=Teko;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DTeko;fontSize=23;fontStyle=1" parent="G6NHiv4yU3Q8GBfmJWzk-166" vertex="1">
          <mxGeometry x="1733.84" y="-324" width="260.31" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ALniYcZCjree9EDMam5--11" value="&lt;b&gt;&lt;font style=&quot;font-size: 26px;&quot;&gt;Validate, Reconcile, and Finalize When Created. Updated, or Deleted&lt;/font&gt;&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="G6NHiv4yU3Q8GBfmJWzk-166" vertex="1">
          <mxGeometry x="1429.69" y="-258" width="926.62" height="30" as="geometry" />
        </mxCell>
        <mxCell id="OzjPrkt5FES2q72cQZmu-0" value="" style="group" vertex="1" connectable="0" parent="G6NHiv4yU3Q8GBfmJWzk-166">
          <mxGeometry x="1233" y="-211" width="429" height="1352.6399999999999" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-267" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=light-dark(#9E9E9E,#9E9E9E);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-0" vertex="1">
          <mxGeometry width="429" height="1352.64" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-343" value="&lt;h1&gt;&lt;font style=&quot;color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0)); font-size: 22px;&quot;&gt;validator(PeprValidateRequest)&lt;/font&gt;&lt;/h1&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-0" vertex="1">
          <mxGeometry x="9.02185595567886" y="15.781089121202626" width="399.93074792243766" height="43.65834896794143" as="geometry" />
        </mxCell>
        <mxCell id="n4SWNpKfFiwBBUjUZvT6-4" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;strokeColor=light-dark(#000000,#000000);" parent="OzjPrkt5FES2q72cQZmu-0" source="sjCE7q7Lh9UsFCw3rUA--10" target="1MT28-nw7Wf4NEG4kALT-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--10" value="Migrate Request Package to latest version" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;fontColor=light-dark(#000000,#000000);fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontSize=14;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-0" vertex="1">
          <mxGeometry x="46.3188365650966" y="57.8518131889149" width="182.0775623268698" height="51.96040202966432" as="geometry" />
        </mxCell>
        <mxCell id="1MT28-nw7Wf4NEG4kALT-11" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);curved=0;" parent="OzjPrkt5FES2q72cQZmu-0" source="sjCE7q7Lh9UsFCw3rUA--12" target="sjCE7q7Lh9UsFCw3rUA--235" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="1MT28-nw7Wf4NEG4kALT-19" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot;&gt;No&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="1MT28-nw7Wf4NEG4kALT-11" vertex="1" connectable="0">
          <mxGeometry x="-0.9128" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--12" value="Is only Package resource in Namespace" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;fontColor=light-dark(#000000,#000000);fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontSize=14;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-0" vertex="1">
          <mxGeometry x="45.110000000000014" y="197.64" width="186.15" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--147" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;jumpStyle=none;" parent="OzjPrkt5FES2q72cQZmu-0" source="sjCE7q7Lh9UsFCw3rUA--151" target="sjCE7q7Lh9UsFCw3rUA--235" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="260.1313751018406" y="1322.9113281303667" as="sourcePoint" />
            <mxPoint x="324.9963988919667" y="1322.8697598087429" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="n4SWNpKfFiwBBUjUZvT6-10" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot;&gt;No&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="sjCE7q7Lh9UsFCw3rUA--147" vertex="1" connectable="0">
          <mxGeometry x="-0.9134" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--20" value="Approve Request" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;fontColor=light-dark(#000000,#000000);fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontSize=14;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-0" vertex="1">
          <mxGeometry x="62.288144044321484" y="1290.4434103786102" width="149.792243767313" height="36.37228142076503" as="geometry" />
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--77" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);curved=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="OzjPrkt5FES2q72cQZmu-0" source="sjCE7q7Lh9UsFCw3rUA--34" target="sjCE7q7Lh9UsFCw3rUA--151" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="101.65121883656514" y="862.9531448243561" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--89" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot;&gt;Yes&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="sjCE7q7Lh9UsFCw3rUA--77" vertex="1" connectable="0">
          <mxGeometry x="-0.592" y="-1" relative="1" as="geometry">
            <mxPoint y="8" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--34" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;For expose list:&lt;/span&gt;&lt;/div&gt;- Valid advancedHTTP usage&lt;div&gt;&amp;nbsp;-&amp;nbsp;&lt;span style=&quot;background-color: transparent;&quot;&gt;Valid &lt;/span&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;directResponse usage&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;div&gt;- Unique names&lt;/div&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fontColor=light-dark(#000000,#000000);fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontSize=12;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-0" vertex="1">
          <mxGeometry x="14.156842105263195" y="575.7629508196721" width="244.5394736842105" height="154.1803278688525" as="geometry" />
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--47" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=light-dark(#000000,#000000);curved=0;" parent="OzjPrkt5FES2q72cQZmu-0" source="sjCE7q7Lh9UsFCw3rUA--49" target="sjCE7q7Lh9UsFCw3rUA--235" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="313.36204986149573" y="398.1751113934426" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="n4SWNpKfFiwBBUjUZvT6-8" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot;&gt;Yes&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="sjCE7q7Lh9UsFCw3rUA--47" vertex="1" connectable="0">
          <mxGeometry x="-0.8536" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--49" value="&lt;div style=&quot;line-height: 90%;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;&lt;font&gt;is&amp;nbsp;&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;line-height: 90%;&quot;&gt;&lt;font&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;Namespace&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;a&amp;nbsp;&lt;/span&gt;&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;line-height: 90%;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;&lt;font&gt;system&amp;nbsp;&lt;/font&gt;&lt;/span&gt;&lt;div&gt;&lt;div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;&lt;font&gt;Namespace&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fontColor=light-dark(#000000,#000000);fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontSize=11;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;align=center;verticalAlign=middle;horizontal=1;" parent="OzjPrkt5FES2q72cQZmu-0" vertex="1">
          <mxGeometry x="75.2301939058168" y="288.634013423107" width="123.90581717451522" height="114.31288446526152" as="geometry" />
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--69" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;" parent="OzjPrkt5FES2q72cQZmu-0" source="sjCE7q7Lh9UsFCw3rUA--73" target="sjCE7q7Lh9UsFCw3rUA--235" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="336.63074792243765" y="2062.5364747814206" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="n4SWNpKfFiwBBUjUZvT6-12" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="sjCE7q7Lh9UsFCw3rUA--69" vertex="1" connectable="0">
          <mxGeometry x="-0.5139" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--71" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;curved=0;" parent="OzjPrkt5FES2q72cQZmu-0" source="sjCE7q7Lh9UsFCw3rUA--73" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="137.184265927978" y="1293.4434103786102" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--72" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot;&gt;Yes&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="sjCE7q7Lh9UsFCw3rUA--71" vertex="1" connectable="0">
          <mxGeometry x="-0.5169" y="1" relative="1" as="geometry">
            <mxPoint x="-1" y="5" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--73" value="&lt;font style=&quot;font-size: 12px; font-weight: normal;&quot;&gt;For monitor list:&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 12px; font-weight: normal;&quot;&gt;- Unique names&lt;/font&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fontStyle=1;fontColor=light-dark(#000000,#000000);fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontSize=14;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-0" vertex="1">
          <mxGeometry x="55.85842105263151" y="1143.9826229508199" width="161.13631578947368" height="110.1567049180328" as="geometry" />
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--149" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;curved=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="OzjPrkt5FES2q72cQZmu-0" source="sjCE7q7Lh9UsFCw3rUA--151" target="sjCE7q7Lh9UsFCw3rUA--185" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="102.00664819944598" y="1314.366923271055" as="sourcePoint" />
            <mxPoint x="102.9230769230769" y="1184.677212604261" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--150" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot;&gt;Yes&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="sjCE7q7Lh9UsFCw3rUA--149" vertex="1" connectable="0">
          <mxGeometry x="-0.5174" relative="1" as="geometry">
            <mxPoint y="4" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--151" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;For network.allow list:&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;- Valid remoteGenerated usage&lt;/span&gt;&lt;/div&gt;&lt;div&gt;- Unique names&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fontColor=light-dark(#000000,#000000);fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontSize=12;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-0" vertex="1">
          <mxGeometry x="14.67631578947362" y="766.1137704918033" width="243.*************" height="123.**************" as="geometry" />
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--185" value="&lt;div style=&quot;&quot;&gt;&lt;font style=&quot;line-height: 80%;&quot;&gt;For sso list:&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;&quot;&gt;&lt;font style=&quot;background-color: transparent; line-height: 80%;&quot;&gt;- Valid usage of&amp;nbsp;&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;serviceAccountsEnabled&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;- Valid usage of publicClient&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;- Valid usage&lt;/span&gt;&lt;/div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;of&lt;/span&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;&amp;nbsp;standardFlowEnabled&lt;/span&gt;&lt;/div&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;div style=&quot;&quot;&gt;&lt;font style=&quot;line-height: 9.6px;&quot;&gt;- Unique client ID&lt;/font&gt;&lt;/div&gt;&lt;/div&gt;&lt;div style=&quot;&quot;&gt;&lt;font style=&quot;line-height: 9.6px;&quot;&gt;&lt;br&gt;&lt;/font&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fontColor=light-dark(#000000,#000000);fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontSize=12;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-0" vertex="1">
          <mxGeometry x="3.789473684210577" y="923.5727868852459" width="265.2631578947369" height="185.01639344262296" as="geometry" />
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--187" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="OzjPrkt5FES2q72cQZmu-0" source="sjCE7q7Lh9UsFCw3rUA--185" target="sjCE7q7Lh9UsFCw3rUA--235" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="359.89944598337945" y="2147.75153411007" as="targetPoint" />
            <mxPoint x="246.0443993101676" y="1712.4610976478975" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="n4SWNpKfFiwBBUjUZvT6-11" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot;&gt;No&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="sjCE7q7Lh9UsFCw3rUA--187" vertex="1" connectable="0">
          <mxGeometry x="-0.839" y="2" relative="1" as="geometry">
            <mxPoint x="-13" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--220" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);curved=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="OzjPrkt5FES2q72cQZmu-0" source="sjCE7q7Lh9UsFCw3rUA--185" target="sjCE7q7Lh9UsFCw3rUA--73" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="104.27231121281477" y="1780.7572739538432" as="sourcePoint" />
            <mxPoint x="104.59587257617659" y="1818.8627874277909" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--221" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot;&gt;Yes&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="sjCE7q7Lh9UsFCw3rUA--220" vertex="1" connectable="0">
          <mxGeometry x="-0.3457" y="-1" relative="1" as="geometry">
            <mxPoint x="1" y="2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--235" value="Deny Request" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;fontColor=light-dark(#000000,#000000);fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontSize=14;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-0" vertex="1">
          <mxGeometry x="269.0572853185596" y="1293.4434103786102" width="127.97783933518006" height="36.37228142076503" as="geometry" />
        </mxCell>
        <mxCell id="1MT28-nw7Wf4NEG4kALT-21" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;strokeColor=light-dark(#000000,#000000);" parent="OzjPrkt5FES2q72cQZmu-0" source="1MT28-nw7Wf4NEG4kALT-4" target="sjCE7q7Lh9UsFCw3rUA--12" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="137.8578947368419" y="191.65311475409828" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="1MT28-nw7Wf4NEG4kALT-4" value="Extract Package Name and Namespace" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;fontColor=light-dark(#000000,#000000);fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontSize=14;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-0" vertex="1">
          <mxGeometry x="43.91052631578947" y="129.81377049180327" width="187.8947368421053" height="47.28196721311476" as="geometry" />
        </mxCell>
        <mxCell id="03F5VFJSvC48mrg4Thkz-20" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);curved=0;" parent="OzjPrkt5FES2q72cQZmu-0" source="sjCE7q7Lh9UsFCw3rUA--34" target="sjCE7q7Lh9UsFCw3rUA--235" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="n4SWNpKfFiwBBUjUZvT6-7" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot;&gt;No&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="03F5VFJSvC48mrg4Thkz-20" vertex="1" connectable="0">
          <mxGeometry x="-0.9206" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="1MT28-nw7Wf4NEG4kALT-15" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.503;entryY=0.031;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=light-dark(#000000,#000000);curved=0;" parent="OzjPrkt5FES2q72cQZmu-0" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="137.12499999999977" y="401.8932937579938" as="sourcePoint" />
            <mxPoint x="137.16019736842122" y="435.5425409836065" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="1MT28-nw7Wf4NEG4kALT-16" value="&lt;span style=&quot;&quot;&gt;&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot; style=&quot;&quot;&gt;No&lt;/font&gt;&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="1MT28-nw7Wf4NEG4kALT-15" vertex="1" connectable="0">
          <mxGeometry x="-0.0266" relative="1" as="geometry">
            <mxPoint y="-5" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="n4SWNpKfFiwBBUjUZvT6-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.503;entryY=0.04;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=light-dark(#000000,#000000);" parent="OzjPrkt5FES2q72cQZmu-0" source="sjCE7q7Lh9UsFCw3rUA--12" target="sjCE7q7Lh9UsFCw3rUA--49" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="n4SWNpKfFiwBBUjUZvT6-6" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot;&gt;Yes&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="n4SWNpKfFiwBBUjUZvT6-5" vertex="1" connectable="0">
          <mxGeometry x="-0.2147" y="-1" relative="1" as="geometry">
            <mxPoint y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="LSs545wM7-lhFVqXI70S-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);" parent="OzjPrkt5FES2q72cQZmu-0" source="LSs545wM7-lhFVqXI70S-0" target="sjCE7q7Lh9UsFCw3rUA--34" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="-zB5yF5eb09xboKKVwZB-0" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="LSs545wM7-lhFVqXI70S-1" vertex="1" connectable="0">
          <mxGeometry x="-0.4828" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="-zB5yF5eb09xboKKVwZB-1" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;fontColor=#000000;strokeColor=light-dark(#000000,#000000);" parent="OzjPrkt5FES2q72cQZmu-0" source="LSs545wM7-lhFVqXI70S-0" target="sjCE7q7Lh9UsFCw3rUA--235" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="-zB5yF5eb09xboKKVwZB-2" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="-zB5yF5eb09xboKKVwZB-1" vertex="1" connectable="0">
          <mxGeometry x="-0.8484" y="1" relative="1" as="geometry">
            <mxPoint x="-5" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="LSs545wM7-lhFVqXI70S-0" value="Ambient Mode&lt;div&gt;and AuthService&lt;/div&gt;&lt;div&gt;Enabled&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fontColor=light-dark(#000000,#000000);fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontSize=12;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-0" vertex="1">
          <mxGeometry x="55" y="432.64" width="162" height="113" as="geometry" />
        </mxCell>
        <mxCell id="OzjPrkt5FES2q72cQZmu-1" value="" style="group" vertex="1" connectable="0" parent="G6NHiv4yU3Q8GBfmJWzk-166">
          <mxGeometry x="1704" y="-199" width="410" height="1330" as="geometry" />
        </mxCell>
        <mxCell id="ALniYcZCjree9EDMam5--0" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=light-dark(#9E9E9E,#9E9E9E);strokeColor=light-dark(#000000,#000000);container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry width="410" height="1330" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-348" value="&lt;h1&gt;&lt;font style=&quot;font-size: 23px; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;packageReconciler(UDSPackage)&lt;/font&gt;&lt;/h1&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="17.000000000000455" y="30" width="376" height="40" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-401" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" source="G6NHiv4yU3Q8GBfmJWzk-402" target="G6NHiv4yU3Q8GBfmJWzk-407" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-402" value="Log Initial Processing Info" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="45.49000000000069" y="74" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-403" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" source="G6NHiv4yU3Q8GBfmJWzk-407" target="G6NHiv4yU3Q8GBfmJWzk-408" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-404" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="G6NHiv4yU3Q8GBfmJWzk-403" vertex="1" connectable="0">
          <mxGeometry x="-0.4062" y="2" relative="1" as="geometry">
            <mxPoint x="4" y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-405" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" source="G6NHiv4yU3Q8GBfmJWzk-407" target="G6NHiv4yU3Q8GBfmJWzk-413" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-406" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="G6NHiv4yU3Q8GBfmJWzk-405" vertex="1" connectable="0">
          <mxGeometry x="0.3997" relative="1" as="geometry">
            <mxPoint y="-4" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-407" value="is Package reconciling" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="66.79999999999973" y="140" width="117.38" height="101.93" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-408" value="Log Skip and Return" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="215.4900000000007" y="163.75" width="169.99" height="46.25" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-409" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" source="G6NHiv4yU3Q8GBfmJWzk-413" target="G6NHiv4yU3Q8GBfmJWzk-415" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-410" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="G6NHiv4yU3Q8GBfmJWzk-409" vertex="1" connectable="0">
          <mxGeometry x="-0.1566" y="1" relative="1" as="geometry">
            <mxPoint y="-7" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-411" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" source="G6NHiv4yU3Q8GBfmJWzk-413" target="G6NHiv4yU3Q8GBfmJWzk-417" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-412" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="G6NHiv4yU3Q8GBfmJWzk-411" vertex="1" connectable="0">
          <mxGeometry x="0.3444" y="-3" relative="1" as="geometry">
            <mxPoint x="3" y="-7" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-413" value="Is Retry Attempt" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="77.75" y="262" width="95.48" height="84" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-414" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);curved=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" source="G6NHiv4yU3Q8GBfmJWzk-415" target="G6NHiv4yU3Q8GBfmJWzk-417" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="275.4800000000005" y="412.1500000000001" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-415" value="Wait for Backoff" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="215.4900000000007" y="280.25" width="169.99" height="47.5" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-416" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" source="G6NHiv4yU3Q8GBfmJWzk-417" target="G6NHiv4yU3Q8GBfmJWzk-419" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-417" value="Migrate Request Package to latest version" style="rounded=1;whiteSpace=wrap;html=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="38.48999999999978" y="382.1500000000001" width="174.01" height="60" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-418" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" source="G6NHiv4yU3Q8GBfmJWzk-419" target="G6NHiv4yU3Q8GBfmJWzk-421" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-419" value="Update Package State to &quot;Pending&quot;" style="rounded=1;whiteSpace=wrap;html=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="47.470000000000255" y="465.1500000000001" width="156.01" height="60" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-420" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" source="G6NHiv4yU3Q8GBfmJWzk-421" target="G6NHiv4yU3Q8GBfmJWzk-423" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-421" value="Configure Namespace and Network Policies" style="rounded=1;whiteSpace=wrap;html=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="51.470000000000255" y="545.1500000000001" width="148.01" height="60" as="geometry" />
        </mxCell>
        <mxCell id="d8Z4GK2wHJgT9erUlwGJ-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="OzjPrkt5FES2q72cQZmu-1" source="G6NHiv4yU3Q8GBfmJWzk-423" target="G6NHiv4yU3Q8GBfmJWzk-428" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-423" value="Enable Istio Service Mesh Integration" style="rounded=1;whiteSpace=wrap;html=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="65.49499999999989" y="624" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ENsQ7zEtRUTXkWdEzoXz-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="OzjPrkt5FES2q72cQZmu-1" source="G6NHiv4yU3Q8GBfmJWzk-428" target="ENsQ7zEtRUTXkWdEzoXz-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ENsQ7zEtRUTXkWdEzoXz-4" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot;&gt;Yes&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="ENsQ7zEtRUTXkWdEzoXz-3" vertex="1" connectable="0">
          <mxGeometry x="-0.3594" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ENsQ7zEtRUTXkWdEzoXz-10" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);curved=0;" parent="OzjPrkt5FES2q72cQZmu-1" source="G6NHiv4yU3Q8GBfmJWzk-428" target="G6NHiv4yU3Q8GBfmJWzk-433" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="31" y="760" />
              <mxPoint x="31" y="1110" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ENsQ7zEtRUTXkWdEzoXz-11" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot;&gt;No&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="ENsQ7zEtRUTXkWdEzoXz-10" vertex="1" connectable="0">
          <mxGeometry x="-0.8295" y="-1" relative="1" as="geometry">
            <mxPoint x="15" y="-8" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-428" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;SSO&amp;nbsp;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;Clients&amp;nbsp;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;Present&lt;/span&gt;&amp;nbsp;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="70.99000000000001" y="710" width="109.01" height="100" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-430" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" source="G6NHiv4yU3Q8GBfmJWzk-431" target="G6NHiv4yU3Q8GBfmJWzk-433" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-431" value="Configure SSO and AuthService Clients" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="91.58999999999969" y="1000" width="177.01" height="54.15" as="geometry" />
        </mxCell>
        <mxCell id="sjCE7q7Lh9UsFCw3rUA--8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);" parent="OzjPrkt5FES2q72cQZmu-1" source="G6NHiv4yU3Q8GBfmJWzk-433" target="G6NHiv4yU3Q8GBfmJWzk-440" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-433" value="Generate Istio Resources (VirtualServices, ServiceEntries)" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="75.58999999999969" y="1080" width="209.01" height="60" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-440" value="Generate Service and Pod monitors" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="67.23000000000002" y="1160" width="224.99" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ay6lkLb2DpxKqKg8CqS4-0" value="Update Package Status to Ready" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="66.79999999999995" y="1240" width="224.99" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ay6lkLb2DpxKqKg8CqS4-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);" parent="OzjPrkt5FES2q72cQZmu-1" source="G6NHiv4yU3Q8GBfmJWzk-440" target="ay6lkLb2DpxKqKg8CqS4-0" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ENsQ7zEtRUTXkWdEzoXz-8" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);curved=0;" parent="OzjPrkt5FES2q72cQZmu-1" source="ENsQ7zEtRUTXkWdEzoXz-1" target="G6NHiv4yU3Q8GBfmJWzk-431" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ENsQ7zEtRUTXkWdEzoXz-9" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot;&gt;Yes&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="ENsQ7zEtRUTXkWdEzoXz-8" vertex="1" connectable="0">
          <mxGeometry x="-0.3032" relative="1" as="geometry">
            <mxPoint x="14" y="-9" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ENsQ7zEtRUTXkWdEzoXz-1" value="Is Identity&amp;nbsp;&lt;div&gt;Deployed&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="74.97000000000003" y="850" width="101.01" height="100" as="geometry" />
        </mxCell>
        <mxCell id="ENsQ7zEtRUTXkWdEzoXz-5" value="Log error, throw exception" style="whiteSpace=wrap;html=1;fontSize=14;fontFamily=Poppins;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);rounded=1;arcSize=14;strokeWidth=2;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-1" vertex="1">
          <mxGeometry x="230.55999999999995" y="880" width="139.44" height="40" as="geometry" />
        </mxCell>
        <mxCell id="ENsQ7zEtRUTXkWdEzoXz-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="OzjPrkt5FES2q72cQZmu-1" source="ENsQ7zEtRUTXkWdEzoXz-1" target="ENsQ7zEtRUTXkWdEzoXz-5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ENsQ7zEtRUTXkWdEzoXz-7" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot;&gt;No&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="ENsQ7zEtRUTXkWdEzoXz-6" vertex="1" connectable="0">
          <mxGeometry x="-0.0577" relative="1" as="geometry">
            <mxPoint x="-2" y="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="OzjPrkt5FES2q72cQZmu-2" value="" style="group" vertex="1" connectable="0" parent="G6NHiv4yU3Q8GBfmJWzk-166">
          <mxGeometry x="2156" y="-44" width="400" height="1020" as="geometry" />
        </mxCell>
        <mxCell id="ALniYcZCjree9EDMam5--1" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=light-dark(#9E9E9E,#9E9E9E);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-2" vertex="1">
          <mxGeometry width="400" height="1020" as="geometry" />
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);" parent="OzjPrkt5FES2q72cQZmu-2" source="G6NHiv4yU3Q8GBfmJWzk-442" target="jztyWWzp8NN_jCGZkJXg-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-442" value="Log Information for Removal" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);fontColor=light-dark(#000000,#000000);strokeColor=light-dark(#000000,#000000);fontSize=14;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-2" vertex="1">
          <mxGeometry x="21" y="338.40999999999997" width="210" height="51.59" as="geometry" />
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-41" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);" parent="OzjPrkt5FES2q72cQZmu-2" source="G6NHiv4yU3Q8GBfmJWzk-449" target="G6NHiv4yU3Q8GBfmJWzk-451" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="03F5VFJSvC48mrg4Thkz-3" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot; style=&quot;color: light-dark(rgb(0, 0, 0), rgb(10, 10, 10)); background-color: light-dark(rgb(255, 255, 255), rgb(255, 255, 255)); font-size: 12px;&quot;&gt;success&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="jztyWWzp8NN_jCGZkJXg-41" vertex="1" connectable="0">
          <mxGeometry x="-0.0691" y="-2" relative="1" as="geometry">
            <mxPoint y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="03F5VFJSvC48mrg4Thkz-6" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);curved=0;" parent="OzjPrkt5FES2q72cQZmu-2" source="G6NHiv4yU3Q8GBfmJWzk-449" target="jztyWWzp8NN_jCGZkJXg-26" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-449" value="Remove SSO Clients" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);fontColor=light-dark(#000000,#000000);strokeColor=light-dark(#000000,#000000);fontSize=14;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-2" vertex="1">
          <mxGeometry x="21" y="626.0799999999999" width="210" height="53.92" as="geometry" />
        </mxCell>
        <mxCell id="03F5VFJSvC48mrg4Thkz-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);" parent="OzjPrkt5FES2q72cQZmu-2" source="G6NHiv4yU3Q8GBfmJWzk-451" target="03F5VFJSvC48mrg4Thkz-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="03F5VFJSvC48mrg4Thkz-11" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#080808);curved=0;" parent="OzjPrkt5FES2q72cQZmu-2" source="G6NHiv4yU3Q8GBfmJWzk-451" target="jztyWWzp8NN_jCGZkJXg-26" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="n4SWNpKfFiwBBUjUZvT6-13" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);" parent="OzjPrkt5FES2q72cQZmu-2" source="G6NHiv4yU3Q8GBfmJWzk-451" target="03F5VFJSvC48mrg4Thkz-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="esVc_AWjQ2o6MDnTzkv9-0" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot;&gt;success&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);" parent="n4SWNpKfFiwBBUjUZvT6-13" vertex="1" connectable="0">
          <mxGeometry x="-0.1586" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-451" value="Remove AuthService Configuration" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);fontColor=light-dark(#000000,#000000);strokeColor=light-dark(#000000,#000000);fontSize=14;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-2" vertex="1">
          <mxGeometry x="21" y="737.6700000000001" width="210" height="52.33" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-352" value="&lt;h1&gt;&lt;font style=&quot;font-size: 23px; color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0));&quot;&gt;packageFinalizer(UDSPackage)&lt;/font&gt;&lt;/h1&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-2" vertex="1">
          <mxGeometry x="21" y="29.889999999999908" width="356" height="41.11" as="geometry" />
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-2" value="is Package removing" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-2" vertex="1">
          <mxGeometry x="41" y="71" width="117.38" height="101.93" as="geometry" />
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-3" value="Log Skip and Return" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-2" vertex="1">
          <mxGeometry x="198" y="98.83999999999992" width="190" height="46.25" as="geometry" />
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="OzjPrkt5FES2q72cQZmu-2" source="jztyWWzp8NN_jCGZkJXg-2" target="jztyWWzp8NN_jCGZkJXg-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="158" y="131" as="sourcePoint" />
            <mxPoint x="194.80000000000018" y="201.66000000000008" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-6" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="jztyWWzp8NN_jCGZkJXg-5" vertex="1" connectable="0">
          <mxGeometry x="-0.4062" y="2" relative="1" as="geometry">
            <mxPoint x="4" y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-7" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;is Package Ready or&lt;br&gt;&amp;nbsp;Failed" style="rhombus;whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-2" vertex="1">
          <mxGeometry x="42" y="202" width="117.38" height="101.93" as="geometry" />
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-9" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="OzjPrkt5FES2q72cQZmu-2" source="jztyWWzp8NN_jCGZkJXg-2" target="jztyWWzp8NN_jCGZkJXg-7" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="168" y="141" as="sourcePoint" />
            <mxPoint x="221" y="141" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-10" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="jztyWWzp8NN_jCGZkJXg-9" vertex="1" connectable="0">
          <mxGeometry x="-0.4062" y="2" relative="1" as="geometry">
            <mxPoint x="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-38" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;strokeColor=light-dark(#000000,#000000);entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="OzjPrkt5FES2q72cQZmu-2" source="jztyWWzp8NN_jCGZkJXg-14" target="jztyWWzp8NN_jCGZkJXg-39" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="126" y="446.1400000000003" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-14" value="Update Status to Removing" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);fontColor=light-dark(#000000,#000000);strokeColor=light-dark(#000000,#000000);fontSize=14;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-2" vertex="1">
          <mxGeometry x="21" y="430" width="210" height="50" as="geometry" />
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-19" value="Log Waiting and Return" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-2" vertex="1">
          <mxGeometry x="198" y="229.83999999999992" width="190" height="46.25" as="geometry" />
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);" parent="OzjPrkt5FES2q72cQZmu-2" source="jztyWWzp8NN_jCGZkJXg-26" target="jztyWWzp8NN_jCGZkJXg-31" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-26" value="Create Failure Event" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-2" vertex="1">
          <mxGeometry x="254.69999999999982" y="850" width="126.31" height="50" as="geometry" />
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-31" value="Update Status to RemovalFailed" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontSize=14;container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-2" vertex="1">
          <mxGeometry x="254.69999999999982" y="931.75" width="126.31" height="48.25" as="geometry" />
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-33" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="OzjPrkt5FES2q72cQZmu-2" source="jztyWWzp8NN_jCGZkJXg-7" target="jztyWWzp8NN_jCGZkJXg-19" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="153" y="132" as="sourcePoint" />
            <mxPoint x="211" y="132" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-34" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="jztyWWzp8NN_jCGZkJXg-33" vertex="1" connectable="0">
          <mxGeometry x="-0.4062" y="2" relative="1" as="geometry">
            <mxPoint x="4" y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-35" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="OzjPrkt5FES2q72cQZmu-2" source="jztyWWzp8NN_jCGZkJXg-7" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="95" y="183" as="sourcePoint" />
            <mxPoint x="101" y="338" as="targetPoint" />
            <Array as="points">
              <mxPoint x="100" y="320" />
              <mxPoint x="101" y="320" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-36" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="jztyWWzp8NN_jCGZkJXg-35" vertex="1" connectable="0">
          <mxGeometry x="-0.4062" y="2" relative="1" as="geometry">
            <mxPoint x="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-40" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);" parent="OzjPrkt5FES2q72cQZmu-2" source="jztyWWzp8NN_jCGZkJXg-39" target="G6NHiv4yU3Q8GBfmJWzk-449" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="03F5VFJSvC48mrg4Thkz-2" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot; style=&quot;color: light-dark(rgb(0, 0, 0), rgb(0, 0, 0)); background-color: light-dark(rgb(255, 255, 255), rgb(255, 255, 255)); font-size: 12px;&quot;&gt;success&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="jztyWWzp8NN_jCGZkJXg-40" vertex="1" connectable="0">
          <mxGeometry x="-0.1786" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="03F5VFJSvC48mrg4Thkz-4" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);curved=0;" parent="OzjPrkt5FES2q72cQZmu-2" source="jztyWWzp8NN_jCGZkJXg-39" target="jztyWWzp8NN_jCGZkJXg-26" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="03F5VFJSvC48mrg4Thkz-7" value="&lt;font data-font-src=&quot;https://fonts.googleapis.com/css?family=Poppins&quot; face=&quot;Poppins&quot; style=&quot;color: light-dark(rgb(0, 0, 0), rgb(25, 25, 25)); background-color: light-dark(rgb(255, 255, 255), rgb(255, 255, 255)); font-size: 12px;&quot;&gt;failure&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="03F5VFJSvC48mrg4Thkz-4" vertex="1" connectable="0">
          <mxGeometry x="0.0738" y="-2" relative="1" as="geometry">
            <mxPoint y="142" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jztyWWzp8NN_jCGZkJXg-39" value="Remove Istio from Namespace" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);fontColor=light-dark(#000000,#000000);strokeColor=light-dark(#000000,#000000);fontSize=14;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-2" vertex="1">
          <mxGeometry x="21" y="516.7" width="210" height="55.3" as="geometry" />
        </mxCell>
        <mxCell id="03F5VFJSvC48mrg4Thkz-8" value="Log Success and Remove Finalizer" style="whiteSpace=wrap;html=1;rounded=1;arcSize=14;strokeWidth=2;fillColor=light-dark(#FFFFFF,#FFFFFF);fontColor=light-dark(#000000,#000000);strokeColor=light-dark(#000000,#000000);fontSize=14;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="OzjPrkt5FES2q72cQZmu-2" vertex="1">
          <mxGeometry x="21" y="850.0000000000001" width="210" height="52.33" as="geometry" />
        </mxCell>
        <mxCell id="OzjPrkt5FES2q72cQZmu-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;strokeColor=light-dark(#000000,#121212);" edge="1" parent="G6NHiv4yU3Q8GBfmJWzk-166" source="G6NHiv4yU3Q8GBfmJWzk-267" target="ALniYcZCjree9EDMam5--0">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="OzjPrkt5FES2q72cQZmu-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;strokeColor=light-dark(#000000,#121212);" edge="1" parent="G6NHiv4yU3Q8GBfmJWzk-166" source="ALniYcZCjree9EDMam5--0" target="ALniYcZCjree9EDMam5--1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TqU2hf1_4hmcHqV1vsfY-16" value="Custom Resource Overview" style="locked=1;" parent="WIyWlLk6GJQsqaUBKTNV-0" visible="0" />
        <mxCell id="QcH75vGbOHyAUbfCnX0q-0" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=light-dark(#24B0FF,#24B0FF);strokeColor=light-dark(#000000,#000000);container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-16" vertex="1">
          <mxGeometry x="549" y="246" width="587" height="515" as="geometry" />
        </mxCell>
        <mxCell id="QcH75vGbOHyAUbfCnX0q-2" value="&lt;font&gt;UDS Pepr Operator&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=48;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Teko;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DTeko;" parent="TqU2hf1_4hmcHqV1vsfY-16" vertex="1">
          <mxGeometry x="642" y="267" width="405" height="60" as="geometry" />
        </mxCell>
        <mxCell id="QcH75vGbOHyAUbfCnX0q-1" value="&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-size: 0px; text-align: start; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22group%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22-240%22%20y%3D%22160%22%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%223%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%23B1DDF0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%224%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%2210%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%225%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BexitPerimeter%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%228%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%226%22%20value%3D%22If%20Kube%20Node%26lt%3Bdiv%26gt%3BCIDRs%20Disabled%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22strokeWidth%3D2%3Bhtml%3D1%3Bshape%3Dmxgraph.flowchart.decision%3BwhiteSpace%3Dwrap%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22105%22%20y%3D%22100%22%20width%3D%22150%22%20height%3D%2270%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%227%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%228%22%20target%3D%2213%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%228%22%20value%3D%22Created%20or%20Updated%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2240%22%20y%3D%22210%22%20width%3D%22130%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%229%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2210%22%20target%3D%2217%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2210%22%20value%3D%22Deleted%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22230%22%20y%3D%22210%22%20width%3D%2280%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2211%22%20value%3D%22a.Node%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfontSize%3D12%3Bglass%3D0%3BstrokeWidth%3D1%3Bshadow%3D0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22120%22%20y%3D%2230%22%20width%3D%22120%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2212%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2213%22%20target%3D%2214%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2213%22%20value%3D%22Add%20Node%20IP%20to%20CIDR%20List%26amp%3Bnbsp%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2214%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2215%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2216%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2217%22%20target%3D%2215%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2217%22%20value%3D%22Remove%20Node%20IP%20to%26amp%3Bnbsp%3B%26lt%3Bdiv%26gt%3BCIDR%20List%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2218%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BentryPerimeter%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2211%22%20target%3D%226%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#144A8F,#144A8F);container=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-16" vertex="1">
          <mxGeometry x="577" y="351" width="525" height="382" as="geometry" />
        </mxCell>
        <mxCell id="QcH75vGbOHyAUbfCnX0q-53" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;b&gt;Custom Resources&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=light-dark(#FFFFFF,#FFFFFF);container=0;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-16" vertex="1">
          <mxGeometry x="744.74" y="368" width="189.52" height="21.14" as="geometry" />
        </mxCell>
        <mxCell id="QcH75vGbOHyAUbfCnX0q-45" value="&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-size: 0px; text-align: start; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22group%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22-240%22%20y%3D%22160%22%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%223%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%23B1DDF0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%224%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%2210%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%225%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BexitPerimeter%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%228%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%226%22%20value%3D%22If%20Kube%20Node%26lt%3Bdiv%26gt%3BCIDRs%20Disabled%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22strokeWidth%3D2%3Bhtml%3D1%3Bshape%3Dmxgraph.flowchart.decision%3BwhiteSpace%3Dwrap%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22105%22%20y%3D%22100%22%20width%3D%22150%22%20height%3D%2270%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%227%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%228%22%20target%3D%2213%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%228%22%20value%3D%22Created%20or%20Updated%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2240%22%20y%3D%22210%22%20width%3D%22130%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%229%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2210%22%20target%3D%2217%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2210%22%20value%3D%22Deleted%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22230%22%20y%3D%22210%22%20width%3D%2280%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2211%22%20value%3D%22a.Node%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfontSize%3D12%3Bglass%3D0%3BstrokeWidth%3D1%3Bshadow%3D0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22120%22%20y%3D%2230%22%20width%3D%22120%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2212%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2213%22%20target%3D%2214%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2213%22%20value%3D%22Add%20Node%20IP%20to%20CIDR%20List%26amp%3Bnbsp%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2214%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2215%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2216%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2217%22%20target%3D%2215%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2217%22%20value%3D%22Remove%20Node%20IP%20to%26amp%3Bnbsp%3B%26lt%3Bdiv%26gt%3BCIDR%20List%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2218%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BentryPerimeter%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2211%22%20target%3D%226%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#9E9E9E,#9E9E9E);container=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-16" vertex="1">
          <mxGeometry x="607" y="409" width="220" height="300" as="geometry" />
        </mxCell>
        <mxCell id="QcH75vGbOHyAUbfCnX0q-46" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-16" source="QcH75vGbOHyAUbfCnX0q-47" target="QcH75vGbOHyAUbfCnX0q-49" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="QcH75vGbOHyAUbfCnX0q-47" value="UDS Package" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-16" vertex="1">
          <mxGeometry x="657" y="449" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="QcH75vGbOHyAUbfCnX0q-48" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-16" source="QcH75vGbOHyAUbfCnX0q-49" target="QcH75vGbOHyAUbfCnX0q-51" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="QcH75vGbOHyAUbfCnX0q-49" value="Package CR Validation" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-16" vertex="1">
          <mxGeometry x="657" y="509" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="QcH75vGbOHyAUbfCnX0q-50" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-16" source="QcH75vGbOHyAUbfCnX0q-51" target="QcH75vGbOHyAUbfCnX0q-52" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="QcH75vGbOHyAUbfCnX0q-51" value="Queue Package for Processing" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-16" vertex="1">
          <mxGeometry x="657" y="569" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="QcH75vGbOHyAUbfCnX0q-52" value="Finalize Package (deletions)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-16" vertex="1">
          <mxGeometry x="657" y="629" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="QcH75vGbOHyAUbfCnX0q-26" value="&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-size: 0px; text-align: start; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22group%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22-240%22%20y%3D%22160%22%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%223%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%23B1DDF0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%224%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%2210%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%225%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BexitPerimeter%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%228%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%226%22%20value%3D%22If%20Kube%20Node%26lt%3Bdiv%26gt%3BCIDRs%20Disabled%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22strokeWidth%3D2%3Bhtml%3D1%3Bshape%3Dmxgraph.flowchart.decision%3BwhiteSpace%3Dwrap%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22105%22%20y%3D%22100%22%20width%3D%22150%22%20height%3D%2270%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%227%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%228%22%20target%3D%2213%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%228%22%20value%3D%22Created%20or%20Updated%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2240%22%20y%3D%22210%22%20width%3D%22130%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%229%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2210%22%20target%3D%2217%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2210%22%20value%3D%22Deleted%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22230%22%20y%3D%22210%22%20width%3D%2280%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2211%22%20value%3D%22a.Node%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfontSize%3D12%3Bglass%3D0%3BstrokeWidth%3D1%3Bshadow%3D0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22120%22%20y%3D%2230%22%20width%3D%22120%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2212%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2213%22%20target%3D%2214%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2213%22%20value%3D%22Add%20Node%20IP%20to%20CIDR%20List%26amp%3Bnbsp%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2214%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2215%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2216%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2217%22%20target%3D%2215%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2217%22%20value%3D%22Remove%20Node%20IP%20to%26amp%3Bnbsp%3B%26lt%3Bdiv%26gt%3BCIDR%20List%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2218%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BentryPerimeter%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2211%22%20target%3D%226%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#9E9E9E,#9E9E9E);container=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-16" vertex="1">
          <mxGeometry x="872" y="469" width="200" height="170" as="geometry" />
        </mxCell>
        <mxCell id="QcH75vGbOHyAUbfCnX0q-27" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-16" source="QcH75vGbOHyAUbfCnX0q-28" target="QcH75vGbOHyAUbfCnX0q-29" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="QcH75vGbOHyAUbfCnX0q-28" value="UDS Exemption" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-16" vertex="1">
          <mxGeometry x="912" y="504" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="QcH75vGbOHyAUbfCnX0q-29" value="Exemption CR Validation" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-16" vertex="1">
          <mxGeometry x="912" y="564" width="120" height="40" as="geometry" />
        </mxCell>
        <object label="Operator Overview" id="WIyWlLk6GJQsqaUBKTNV-1">
          <mxCell style="locked=1;" parent="WIyWlLk6GJQsqaUBKTNV-0" visible="0" />
        </object>
        <mxCell id="gfeORhnWdxzPLgAA0gov-0" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=light-dark(#24B0FF,#24B0FF);strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-486" y="130" width="976" height="756" as="geometry" />
        </mxCell>
        <mxCell id="TqU2hf1_4hmcHqV1vsfY-1" value="&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-size: 0px; text-align: start; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22group%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22-240%22%20y%3D%22160%22%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%223%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%23B1DDF0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%224%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%2210%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%225%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BexitPerimeter%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%228%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%226%22%20value%3D%22If%20Kube%20Node%26lt%3Bdiv%26gt%3BCIDRs%20Disabled%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22strokeWidth%3D2%3Bhtml%3D1%3Bshape%3Dmxgraph.flowchart.decision%3BwhiteSpace%3Dwrap%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22105%22%20y%3D%22100%22%20width%3D%22150%22%20height%3D%2270%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%227%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%228%22%20target%3D%2213%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%228%22%20value%3D%22Created%20or%20Updated%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2240%22%20y%3D%22210%22%20width%3D%22130%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%229%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2210%22%20target%3D%2217%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2210%22%20value%3D%22Deleted%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22230%22%20y%3D%22210%22%20width%3D%2280%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2211%22%20value%3D%22a.Node%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfontSize%3D12%3Bglass%3D0%3BstrokeWidth%3D1%3Bshadow%3D0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22120%22%20y%3D%2230%22%20width%3D%22120%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2212%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2213%22%20target%3D%2214%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2213%22%20value%3D%22Add%20Node%20IP%20to%20CIDR%20List%26amp%3Bnbsp%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2214%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2215%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2216%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2217%22%20target%3D%2215%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2217%22%20value%3D%22Remove%20Node%20IP%20to%26amp%3Bnbsp%3B%26lt%3Bdiv%26gt%3BCIDR%20List%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2218%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BentryPerimeter%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2211%22%20target%3D%226%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#144A8F,#144A8F);container=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-471" y="235" width="290" height="562" as="geometry" />
        </mxCell>
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-3" value="&lt;font&gt;UDS Pepr Operator&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=48;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Teko;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DTeko;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-172.5" y="150" width="405" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EkdyL6BHs51SKdhCIPZB-0" value="" style="group;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1" connectable="0">
          <mxGeometry x="-150" y="235" width="360" height="430" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-56" value="&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-size: 0px; text-align: start; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22group%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22-240%22%20y%3D%22160%22%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%223%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%23B1DDF0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%224%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%2210%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%225%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BexitPerimeter%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%228%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%226%22%20value%3D%22If%20Kube%20Node%26lt%3Bdiv%26gt%3BCIDRs%20Disabled%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22strokeWidth%3D2%3Bhtml%3D1%3Bshape%3Dmxgraph.flowchart.decision%3BwhiteSpace%3Dwrap%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22105%22%20y%3D%22100%22%20width%3D%22150%22%20height%3D%2270%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%227%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%228%22%20target%3D%2213%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%228%22%20value%3D%22Created%20or%20Updated%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2240%22%20y%3D%22210%22%20width%3D%22130%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%229%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2210%22%20target%3D%2217%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2210%22%20value%3D%22Deleted%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22230%22%20y%3D%22210%22%20width%3D%2280%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2211%22%20value%3D%22a.Node%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfontSize%3D12%3Bglass%3D0%3BstrokeWidth%3D1%3Bshadow%3D0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22120%22%20y%3D%2230%22%20width%3D%22120%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2212%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2213%22%20target%3D%2214%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2213%22%20value%3D%22Add%20Node%20IP%20to%20CIDR%20List%26amp%3Bnbsp%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2214%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2215%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2216%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2217%22%20target%3D%2215%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2217%22%20value%3D%22Remove%20Node%20IP%20to%26amp%3Bnbsp%3B%26lt%3Bdiv%26gt%3BCIDR%20List%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2218%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BentryPerimeter%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2211%22%20target%3D%226%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#9E9E9E,#9E9E9E);container=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" vertex="1">
          <mxGeometry width="360" height="430" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-57" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" target="tMXGoEb_9TJprHVc1VYA-25" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="180" y="170" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-35" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;fontStyle=0;strokeWidth=1;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" source="tMXGoEb_9TJprHVc1VYA-23" target="tMXGoEb_9TJprHVc1VYA-34" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-23" value="Created or Updated" style="rounded=1;whiteSpace=wrap;html=1;strokeWidth=1;fontStyle=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" vertex="1">
          <mxGeometry x="40" y="210" width="130" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-47" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" source="tMXGoEb_9TJprHVc1VYA-25" target="tMXGoEb_9TJprHVc1VYA-43" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-25" value="Deleted" style="rounded=1;whiteSpace=wrap;html=1;strokeWidth=1;fontStyle=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" vertex="1">
          <mxGeometry x="230" y="210" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-28" value="a.Node" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" vertex="1">
          <mxGeometry x="120" y="30" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-37" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;fontStyle=0;strokeWidth=1;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" source="tMXGoEb_9TJprHVc1VYA-34" target="tMXGoEb_9TJprHVc1VYA-36" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-34" value="Add Node IP to CIDR List&amp;nbsp;" style="rounded=1;whiteSpace=wrap;html=1;strokeWidth=1;fontStyle=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" vertex="1">
          <mxGeometry x="35" y="270" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-36" value="Rebuild and Update NetworkPolicies" style="rounded=1;whiteSpace=wrap;html=1;strokeWidth=1;fontStyle=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" vertex="1">
          <mxGeometry x="35" y="340" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-42" value="Rebuild and Update NetworkPolicies" style="rounded=1;whiteSpace=wrap;html=1;strokeWidth=1;fontStyle=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" vertex="1">
          <mxGeometry x="200" y="340" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-48" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" source="tMXGoEb_9TJprHVc1VYA-43" target="tMXGoEb_9TJprHVc1VYA-42" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-43" value="Remove Node IP to&amp;nbsp;&lt;div&gt;CIDR List&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;strokeWidth=1;fontStyle=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" vertex="1">
          <mxGeometry x="200" y="270" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-118" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" source="tMXGoEb_9TJprHVc1VYA-114" target="tMXGoEb_9TJprHVc1VYA-23" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="150" y="170" />
              <mxPoint x="105" y="170" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-114" value="If Kube Node&lt;div&gt;CIDRs Disabled&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" vertex="1">
          <mxGeometry x="120" y="90" width="120" height="110" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-115" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-0" source="tMXGoEb_9TJprHVc1VYA-28" target="tMXGoEb_9TJprHVc1VYA-114" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EkdyL6BHs51SKdhCIPZB-2" value="" style="group;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1" connectable="0">
          <mxGeometry x="-80" y="690" width="200" height="160" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-80" value="&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-size: 0px; text-align: start; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22group%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22-240%22%20y%3D%22160%22%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%223%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%23B1DDF0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%224%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%2210%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%225%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BexitPerimeter%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%228%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%226%22%20value%3D%22If%20Kube%20Node%26lt%3Bdiv%26gt%3BCIDRs%20Disabled%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22strokeWidth%3D2%3Bhtml%3D1%3Bshape%3Dmxgraph.flowchart.decision%3BwhiteSpace%3Dwrap%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22105%22%20y%3D%22100%22%20width%3D%22150%22%20height%3D%2270%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%227%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%228%22%20target%3D%2213%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%228%22%20value%3D%22Created%20or%20Updated%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2240%22%20y%3D%22210%22%20width%3D%22130%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%229%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2210%22%20target%3D%2217%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2210%22%20value%3D%22Deleted%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22230%22%20y%3D%22210%22%20width%3D%2280%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2211%22%20value%3D%22a.Node%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfontSize%3D12%3Bglass%3D0%3BstrokeWidth%3D1%3Bshadow%3D0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22120%22%20y%3D%2230%22%20width%3D%22120%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2212%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2213%22%20target%3D%2214%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2213%22%20value%3D%22Add%20Node%20IP%20to%20CIDR%20List%26amp%3Bnbsp%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2214%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2215%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2216%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2217%22%20target%3D%2215%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2217%22%20value%3D%22Remove%20Node%20IP%20to%26amp%3Bnbsp%3B%26lt%3Bdiv%26gt%3BCIDR%20List%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2218%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BentryPerimeter%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2211%22%20target%3D%226%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#9E9E9E,#9E9E9E);container=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-2" vertex="1">
          <mxGeometry width="199.99999999999997" height="160" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-12" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-2" source="tMXGoEb_9TJprHVc1VYA-10" target="tMXGoEb_9TJprHVc1VYA-11" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-10" value="a.Service" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-2" vertex="1">
          <mxGeometry x="40" y="35.55555555555556" width="120" height="35.55555555555556" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-11" value="Update API Server CIDR&amp;nbsp;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-2" vertex="1">
          <mxGeometry x="40" y="88.88888888888889" width="120" height="35.55555555555556" as="geometry" />
        </mxCell>
        <mxCell id="EkdyL6BHs51SKdhCIPZB-3" value="" style="group;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1" connectable="0">
          <mxGeometry x="-426" y="608" width="200" height="170" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-84" value="&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-size: 0px; text-align: start; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22group%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22-240%22%20y%3D%22160%22%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%223%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%23B1DDF0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%224%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%2210%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%225%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BexitPerimeter%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%228%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%226%22%20value%3D%22If%20Kube%20Node%26lt%3Bdiv%26gt%3BCIDRs%20Disabled%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22strokeWidth%3D2%3Bhtml%3D1%3Bshape%3Dmxgraph.flowchart.decision%3BwhiteSpace%3Dwrap%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22105%22%20y%3D%22100%22%20width%3D%22150%22%20height%3D%2270%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%227%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%228%22%20target%3D%2213%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%228%22%20value%3D%22Created%20or%20Updated%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2240%22%20y%3D%22210%22%20width%3D%22130%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%229%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2210%22%20target%3D%2217%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2210%22%20value%3D%22Deleted%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22230%22%20y%3D%22210%22%20width%3D%2280%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2211%22%20value%3D%22a.Node%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfontSize%3D12%3Bglass%3D0%3BstrokeWidth%3D1%3Bshadow%3D0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22120%22%20y%3D%2230%22%20width%3D%22120%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2212%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2213%22%20target%3D%2214%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2213%22%20value%3D%22Add%20Node%20IP%20to%20CIDR%20List%26amp%3Bnbsp%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2214%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2215%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2216%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2217%22%20target%3D%2215%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2217%22%20value%3D%22Remove%20Node%20IP%20to%26amp%3Bnbsp%3B%26lt%3Bdiv%26gt%3BCIDR%20List%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2218%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BentryPerimeter%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2211%22%20target%3D%226%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#9E9E9E,#9E9E9E);container=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-3" vertex="1">
          <mxGeometry width="200" height="170" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-9" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-3" source="tMXGoEb_9TJprHVc1VYA-2" target="tMXGoEb_9TJprHVc1VYA-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-2" value="UDS Exemption" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-3" vertex="1">
          <mxGeometry x="40" y="35" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-8" value="Exemption CR Validation" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="EkdyL6BHs51SKdhCIPZB-3" vertex="1">
          <mxGeometry x="40" y="95" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="TqU2hf1_4hmcHqV1vsfY-9" value="" style="group;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1" connectable="0">
          <mxGeometry x="-436" y="285" width="220" height="300" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-82" value="&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-size: 0px; text-align: start; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22group%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22-240%22%20y%3D%22160%22%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%223%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%23B1DDF0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%224%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%2210%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%225%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BexitPerimeter%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%228%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%226%22%20value%3D%22If%20Kube%20Node%26lt%3Bdiv%26gt%3BCIDRs%20Disabled%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22strokeWidth%3D2%3Bhtml%3D1%3Bshape%3Dmxgraph.flowchart.decision%3BwhiteSpace%3Dwrap%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22105%22%20y%3D%22100%22%20width%3D%22150%22%20height%3D%2270%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%227%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%228%22%20target%3D%2213%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%228%22%20value%3D%22Created%20or%20Updated%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2240%22%20y%3D%22210%22%20width%3D%22130%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%229%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2210%22%20target%3D%2217%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2210%22%20value%3D%22Deleted%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22230%22%20y%3D%22210%22%20width%3D%2280%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2211%22%20value%3D%22a.Node%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfontSize%3D12%3Bglass%3D0%3BstrokeWidth%3D1%3Bshadow%3D0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22120%22%20y%3D%2230%22%20width%3D%22120%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2212%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2213%22%20target%3D%2214%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2213%22%20value%3D%22Add%20Node%20IP%20to%20CIDR%20List%26amp%3Bnbsp%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2214%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2215%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2216%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2217%22%20target%3D%2215%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2217%22%20value%3D%22Remove%20Node%20IP%20to%26amp%3Bnbsp%3B%26lt%3Bdiv%26gt%3BCIDR%20List%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2218%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BentryPerimeter%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2211%22%20target%3D%226%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#9E9E9E,#9E9E9E);container=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-9" vertex="1">
          <mxGeometry width="220" height="300" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-9" source="G6NHiv4yU3Q8GBfmJWzk-4" target="tMXGoEb_9TJprHVc1VYA-0" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-4" value="UDS Package" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-9" vertex="1">
          <mxGeometry x="50" y="40" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-9" source="tMXGoEb_9TJprHVc1VYA-0" target="tMXGoEb_9TJprHVc1VYA-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-0" value="Package CR Validation" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-9" vertex="1">
          <mxGeometry x="50" y="100" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-9" source="tMXGoEb_9TJprHVc1VYA-4" target="tMXGoEb_9TJprHVc1VYA-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-4" value="Queue Package for Processing" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-9" vertex="1">
          <mxGeometry x="50" y="160" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-6" value="Finalize Package (deletions)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);labelBackgroundColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="TqU2hf1_4hmcHqV1vsfY-9" vertex="1">
          <mxGeometry x="50" y="220" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="TqU2hf1_4hmcHqV1vsfY-10" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;b&gt;Custom Resources&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=light-dark(#FFFFFF,#FFFFFF);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-423" y="245" width="194" height="30" as="geometry" />
        </mxCell>
        <mxCell id="XUFUkx4sifwEHbH8NtLB-5" value="" style="group;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1" connectable="0">
          <mxGeometry x="240" y="220" width="220" height="300" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-89" value="&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-size: 0px; text-align: start; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22group%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22-240%22%20y%3D%22160%22%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%223%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%23B1DDF0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%224%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%2210%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%225%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BexitPerimeter%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%228%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%226%22%20value%3D%22If%20Kube%20Node%26lt%3Bdiv%26gt%3BCIDRs%20Disabled%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22strokeWidth%3D2%3Bhtml%3D1%3Bshape%3Dmxgraph.flowchart.decision%3BwhiteSpace%3Dwrap%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22105%22%20y%3D%22100%22%20width%3D%22150%22%20height%3D%2270%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%227%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%228%22%20target%3D%2213%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%228%22%20value%3D%22Created%20or%20Updated%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2240%22%20y%3D%22210%22%20width%3D%22130%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%229%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2210%22%20target%3D%2217%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2210%22%20value%3D%22Deleted%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22230%22%20y%3D%22210%22%20width%3D%2280%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2211%22%20value%3D%22a.Node%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfontSize%3D12%3Bglass%3D0%3BstrokeWidth%3D1%3Bshadow%3D0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22120%22%20y%3D%2230%22%20width%3D%22120%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2212%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2213%22%20target%3D%2214%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2213%22%20value%3D%22Add%20Node%20IP%20to%20CIDR%20List%26amp%3Bnbsp%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2214%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2215%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2216%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2217%22%20target%3D%2215%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2217%22%20value%3D%22Remove%20Node%20IP%20to%26amp%3Bnbsp%3B%26lt%3Bdiv%26gt%3BCIDR%20List%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2218%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BentryPerimeter%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2211%22%20target%3D%226%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#9E9E9E,#9E9E9E);container=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="XUFUkx4sifwEHbH8NtLB-5" vertex="1">
          <mxGeometry width="220" height="300" as="geometry" />
        </mxCell>
        <mxCell id="XUFUkx4sifwEHbH8NtLB-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="XUFUkx4sifwEHbH8NtLB-5" source="XUFUkx4sifwEHbH8NtLB-0" target="XUFUkx4sifwEHbH8NtLB-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="XUFUkx4sifwEHbH8NtLB-0" value="Keycloak UDSPackage" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=1;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="XUFUkx4sifwEHbH8NtLB-5" vertex="1">
          <mxGeometry x="30" y="30" width="160" height="35" as="geometry" />
        </mxCell>
        <mxCell id="XUFUkx4sifwEHbH8NtLB-4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="XUFUkx4sifwEHbH8NtLB-5" source="XUFUkx4sifwEHbH8NtLB-1" target="XUFUkx4sifwEHbH8NtLB-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="XUFUkx4sifwEHbH8NtLB-1" value="is Identity Deployed" style="rhombus;whiteSpace=wrap;html=1;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);rounded=1;arcSize=14;strokeWidth=1;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="XUFUkx4sifwEHbH8NtLB-5" vertex="1">
          <mxGeometry x="70" y="100" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="XUFUkx4sifwEHbH8NtLB-3" value="Set UDSConfig isIdentityConfigDeployed" style="whiteSpace=wrap;html=1;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);rounded=1;arcSize=14;strokeWidth=1;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="XUFUkx4sifwEHbH8NtLB-5" vertex="1">
          <mxGeometry x="20" y="210" width="180" height="40" as="geometry" />
        </mxCell>
        <mxCell id="XUFUkx4sifwEHbH8NtLB-6" value="" style="group;fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1" connectable="0">
          <mxGeometry x="240" y="535" width="220" height="300" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-78" value="&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-size: 0px; text-align: start; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22group%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22-240%22%20y%3D%22160%22%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%223%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%23B1DDF0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20width%3D%22360%22%20height%3D%22430%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%224%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%2210%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%225%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BexitPerimeter%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%226%22%20target%3D%228%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%226%22%20value%3D%22If%20Kube%20Node%26lt%3Bdiv%26gt%3BCIDRs%20Disabled%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22strokeWidth%3D2%3Bhtml%3D1%3Bshape%3Dmxgraph.flowchart.decision%3BwhiteSpace%3Dwrap%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22105%22%20y%3D%22100%22%20width%3D%22150%22%20height%3D%2270%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%227%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%228%22%20target%3D%2213%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%228%22%20value%3D%22Created%20or%20Updated%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2240%22%20y%3D%22210%22%20width%3D%22130%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%229%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2210%22%20target%3D%2217%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2210%22%20value%3D%22Deleted%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22230%22%20y%3D%22210%22%20width%3D%2280%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2211%22%20value%3D%22a.Node%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfontSize%3D12%3Bglass%3D0%3BstrokeWidth%3D1%3Bshadow%3D0%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22120%22%20y%3D%2230%22%20width%3D%22120%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2212%22%20value%3D%22%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BfontStyle%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2213%22%20target%3D%2214%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2213%22%20value%3D%22Add%20Node%20IP%20to%20CIDR%20List%26amp%3Bnbsp%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2214%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2235%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2215%22%20value%3D%22Rebuild%20and%20Update%20NetworkPolicies%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22340%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2216%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BstrokeWidth%3D1%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2217%22%20target%3D%2215%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2217%22%20value%3D%22Remove%20Node%20IP%20to%26amp%3Bnbsp%3B%26lt%3Bdiv%26gt%3BCIDR%20List%26lt%3B%2Fdiv%26gt%3B%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BstrokeWidth%3D1%3BfontStyle%3D0%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%22200%22%20y%3D%22270%22%20width%3D%22140%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%2218%22%20style%3D%22edgeStyle%3DorthogonalEdgeStyle%3Brounded%3D1%3BorthogonalLoop%3D1%3BjettySize%3Dauto%3Bhtml%3D1%3BexitX%3D0.5%3BexitY%3D1%3BexitDx%3D0%3BexitDy%3D0%3BentryX%3D0.5%3BentryY%3D0%3BentryDx%3D0%3BentryDy%3D0%3BentryPerimeter%3D0%3Bcurved%3D0%3B%22%20edge%3D%221%22%20parent%3D%222%22%20source%3D%2211%22%20target%3D%226%22%3E%3CmxGeometry%20relative%3D%221%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=light-dark(#9E9E9E,#9E9E9E);container=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="XUFUkx4sifwEHbH8NtLB-6" vertex="1">
          <mxGeometry width="220" height="300" as="geometry" />
        </mxCell>
        <mxCell id="G6NHiv4yU3Q8GBfmJWzk-18" value="a.EndpointSlice" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="XUFUkx4sifwEHbH8NtLB-6" vertex="1">
          <mxGeometry x="57.39130434782612" y="38.709677419354875" width="114.78260869565217" height="38.70967741935484" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-13" value="Update the API Server CIDR" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="XUFUkx4sifwEHbH8NtLB-6" vertex="1">
          <mxGeometry x="54.406956521739176" y="222.58064516129036" width="120.76086956521739" height="38.70967741935484" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-121" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="XUFUkx4sifwEHbH8NtLB-6" source="tMXGoEb_9TJprHVc1VYA-119" target="tMXGoEb_9TJprHVc1VYA-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-119" value="If Kube API&lt;div&gt;CIDRs Disabled&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;glass=0;strokeWidth=1;shadow=0;container=0;fillColor=light-dark(#FFFFFF,#FFFFFF);strokeColor=light-dark(#000000,#000000);fontColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="XUFUkx4sifwEHbH8NtLB-6" vertex="1">
          <mxGeometry x="56.09000000000003" y="100.79999999999995" width="117.39" height="98.39" as="geometry" />
        </mxCell>
        <mxCell id="tMXGoEb_9TJprHVc1VYA-120" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#000000,#000000);fontFamily=Poppins;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DPoppins;" parent="XUFUkx4sifwEHbH8NtLB-6" source="G6NHiv4yU3Q8GBfmJWzk-18" target="tMXGoEb_9TJprHVc1VYA-119" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
