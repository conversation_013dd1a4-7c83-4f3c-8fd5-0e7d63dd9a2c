/**
 * Copyright 2025 Defense Unicorns
 * SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial
 */

// This file is auto-generated by kubernetes-fluent-client, do not edit manually
import { GenericKind, RegisterKind } from "kubernetes-fluent-client";
export class Gateway extends GenericKind {
  /**
   * Configuration affecting edge load balancer. See more details at:
   * https://istio.io/docs/reference/config/networking/gateway.html
   */
  spec?: Spec;
  status?: Status;
}

/**
 * Configuration affecting edge load balancer. See more details at:
 * https://istio.io/docs/reference/config/networking/gateway.html
 */
export interface Spec {
  /**
   * One or more labels that indicate a specific set of pods/VMs on which this gateway
   * configuration should be applied.
   */
  selector?: { [key: string]: string };
  /**
   * A list of server specifications.
   */
  servers?: Server[];
}

export interface Server {
  /**
   * The ip or the Unix domain socket to which the listener should be bound to.
   */
  bind?: string;
  defaultEndpoint?: string;
  /**
   * One or more hosts exposed by this gateway.
   */
  hosts: string[];
  /**
   * An optional name of the server, when set must be unique across all servers.
   */
  name?: string;
  /**
   * The Port on which the proxy should listen for incoming connections.
   */
  port: Port;
  /**
   * Set of TLS related options that govern the server's behavior.
   */
  tls?: TLS;
}

/**
 * The Port on which the proxy should listen for incoming connections.
 */
export interface Port {
  /**
   * Label assigned to the port.
   */
  name: string;
  /**
   * A valid non-negative integer port number.
   */
  number: number;
  /**
   * The protocol exposed on the port.
   */
  protocol: string;
  targetPort?: number;
}

/**
 * Set of TLS related options that govern the server's behavior.
 */
export interface TLS {
  /**
   * REQUIRED if mode is `MUTUAL` or `OPTIONAL_MUTUAL`.
   */
  caCertificates?: string;
  /**
   * OPTIONAL: The path to the file containing the certificate revocation list (CRL) to use in
   * verifying a presented client side certificate.
   */
  caCrl?: string;
  /**
   * Optional: If specified, only support the specified cipher list.
   */
  cipherSuites?: string[];
  /**
   * For gateways running on Kubernetes, the name of the secret that holds the TLS certs
   * including the CA certificates.
   */
  credentialName?: string;
  /**
   * If set to true, the load balancer will send a 301 redirect for all http connections,
   * asking the clients to use HTTPS.
   */
  httpsRedirect?: boolean;
  /**
   * Optional: Maximum TLS protocol version.
   *
   * Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
   */
  maxProtocolVersion?: ProtocolVersion;
  /**
   * Optional: Minimum TLS protocol version.
   *
   * Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
   */
  minProtocolVersion?: ProtocolVersion;
  /**
   * Optional: Indicates whether connections to this port should be secured using TLS.
   *
   * Valid Options: PASSTHROUGH, SIMPLE, MUTUAL, AUTO_PASSTHROUGH, ISTIO_MUTUAL,
   * OPTIONAL_MUTUAL
   */
  mode?: Mode;
  /**
   * REQUIRED if mode is `SIMPLE` or `MUTUAL`.
   */
  privateKey?: string;
  /**
   * REQUIRED if mode is `SIMPLE` or `MUTUAL`.
   */
  serverCertificate?: string;
  /**
   * A list of alternate names to verify the subject identity in the certificate presented by
   * the client.
   */
  subjectAltNames?: string[];
  /**
   * An optional list of hex-encoded SHA-256 hashes of the authorized client certificates.
   */
  verifyCertificateHash?: string[];
  /**
   * An optional list of base64-encoded SHA-256 hashes of the SPKIs of authorized client
   * certificates.
   */
  verifyCertificateSpki?: string[];
}

/**
 * Optional: Maximum TLS protocol version.
 *
 * Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
 *
 * Optional: Minimum TLS protocol version.
 *
 * Valid Options: TLS_AUTO, TLSV1_0, TLSV1_1, TLSV1_2, TLSV1_3
 */
export enum ProtocolVersion {
  TLSAuto = "TLS_AUTO",
  Tlsv10 = "TLSV1_0",
  Tlsv11 = "TLSV1_1",
  Tlsv12 = "TLSV1_2",
  Tlsv13 = "TLSV1_3",
}

/**
 * Optional: Indicates whether connections to this port should be secured using TLS.
 *
 * Valid Options: PASSTHROUGH, SIMPLE, MUTUAL, AUTO_PASSTHROUGH, ISTIO_MUTUAL,
 * OPTIONAL_MUTUAL
 */
export enum Mode {
  AutoPassthrough = "AUTO_PASSTHROUGH",
  IstioMutual = "ISTIO_MUTUAL",
  Mutual = "MUTUAL",
  OptionalMutual = "OPTIONAL_MUTUAL",
  Passthrough = "PASSTHROUGH",
  Simple = "SIMPLE",
}

export interface Status {
  /**
   * Current service state of the resource.
   */
  conditions?: Condition[];
  /**
   * Resource Generation to which the Reconciled Condition refers.
   */
  observedGeneration?: number | string;
  /**
   * Includes any errors or warnings detected by Istio's analyzers.
   */
  validationMessages?: ValidationMessage[];
}

export interface Condition {
  /**
   * Last time we probed the condition.
   */
  lastProbeTime?: Date;
  /**
   * Last time the condition transitioned from one status to another.
   */
  lastTransitionTime?: Date;
  /**
   * Human-readable message indicating details about last transition.
   */
  message?: string;
  /**
   * Unique, one-word, CamelCase reason for the condition's last transition.
   */
  reason?: string;
  /**
   * Status is the status of the condition.
   */
  status?: string;
  /**
   * Type is the type of the condition.
   */
  type?: string;
}

export interface ValidationMessage {
  /**
   * A url pointing to the Istio documentation for this specific error type.
   */
  documentationUrl?: string;
  /**
   * Represents how severe a message is.
   *
   * Valid Options: UNKNOWN, ERROR, WARNING, INFO
   */
  level?: Level;
  type?: Type;
}

/**
 * Represents how severe a message is.
 *
 * Valid Options: UNKNOWN, ERROR, WARNING, INFO
 */
export enum Level {
  Error = "ERROR",
  Info = "INFO",
  Unknown = "UNKNOWN",
  Warning = "WARNING",
}

export interface Type {
  /**
   * A 7 character code matching `^IST[0-9]{4}$` intended to uniquely identify the message
   * type.
   */
  code?: string;
  /**
   * A human-readable name for the message type.
   */
  name?: string;
}

RegisterKind(Gateway, {
  group: "networking.istio.io",
  version: "v1",
  kind: "Gateway",
  plural: "gateways",
});
