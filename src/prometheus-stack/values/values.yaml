# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

crds:
  enabled: false
grafana:
  enabled: false
  forceDeployDashboards: true
kube-state-metrics:
  # Enable scraping of istio-specifc namespace labels
  metricLabelsAllowlist:
    - namespaces=[istio-injection,istio.io/dataplane-mode]
nameOverride: kube-prometheus-stack
nodeExporter:
  jobLabel: jobLabel
  operatingSystems:
    darwin:
      enabled: false
    linux:
      enabled: false
  serviceMonitor:
    interval: ""
prometheus:
  serviceMonitor:
    selfMonitor: true
  prometheusSpec:
    podMetadata:
      labels:
        app: prometheus
    podMonitorSelectorNilUsesHelmValues: false
    probeSelectorNilUsesHelmValues: false
    resources:
      limits:
        cpu: 500m
        memory: 4Gi
      requests:
        cpu: 100m
        memory: 512Mi
    ruleSelectorNilUsesHelmValues: false
    serviceMonitorSelectorNilUsesHelmValues: false
    storageSpec:
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteOnce
          resources:
            requests:
              storage: 50Gi
          storageClassName: null
prometheus-node-exporter:
  containerSecurityContext:
    readOnlyRootFilesystem: true
  hostNetwork: false
  hostPID: false
  podAnnotations:
    cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
prometheusOperator:
  # Kubelet healthprobes on FIPS nodes fail when this is set to 1.3
  tls:
    tlsMinVersion: VersionTLS12
  admissionWebhooks:
    patch:
      resources:
        limits:
          cpu: 100m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 128Mi
  prometheusConfigReloader:
    enableProbe:
      false
    resources:
      limits:
        cpu: 100m
        memory: 128Mi
      requests:
        cpu: 50m
        memory: 128Mi
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 512Mi
alertmanager:
  alertmanagerSpec:
    scheme: "http"
