{"namespaces": [{"apiVersion": "v1", "kind": "Namespace", "metadata": {"creationTimestamp": "2024-04-22T14:00:42Z", "labels": {"kubernetes.io/metadata.name": "default", "zarf.dev/agent": "ignore"}, "name": "default", "resourceVersion": "680", "uid": "0f317322-d4b2-49cf-9bdc-949b1760c7a0"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"creationTimestamp": "2024-04-22T14:00:42Z", "labels": {"kubernetes.io/metadata.name": "kube-system", "zarf.dev/agent": "ignore"}, "name": "kube-system", "resourceVersion": "681", "uid": "4b063c85-1386-4887-b391-a303532f586e"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"creationTimestamp": "2024-04-22T14:00:42Z", "labels": {"kubernetes.io/metadata.name": "kube-public", "zarf.dev/agent": "ignore"}, "name": "kube-public", "resourceVersion": "683", "uid": "5047265c-9614-4b52-8617-b20de9a1ae13"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"creationTimestamp": "2024-04-22T14:00:42Z", "labels": {"kubernetes.io/metadata.name": "kube-node-lease", "zarf.dev/agent": "ignore"}, "name": "kube-node-lease", "resourceVersion": "684", "uid": "ba53aa5c-2b5b-4302-8572-fed2efbb88d4"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"creationTimestamp": "2024-04-22T14:01:05Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "kubernetes.io/metadata.name": "zarf", "zarf.dev/agent": "ignore"}, "name": "zarf", "resourceVersion": "685", "uid": "80e207f5-5aab-414e-9ed4-e3f96b654920"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"creationTimestamp": "2024-04-22T14:01:06Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "kubernetes.io/metadata.name": "uds-dev-stack", "zarf.dev/agent": "ignore"}, "name": "uds-dev-stack", "resourceVersion": "686", "uid": "4967045b-0d5d-4d47-b221-b9f7c41f5fa9"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"creationTimestamp": "2024-04-22T14:03:52Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "kubernetes.io/metadata.name": "uds-crds"}, "name": "uds-crds", "resourceVersion": "949", "uid": "20f83ea4-99bd-49e0-99c8-5f61a83271b2"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"creationTimestamp": "2024-04-22T14:04:14Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "kubernetes.io/metadata.name": "istio-system"}, "name": "istio-system", "resourceVersion": "1051", "uid": "eadee776-a36e-448e-8ec7-e8649db8e181"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"creationTimestamp": "2024-04-22T14:04:19Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "istio-injection": "enabled", "kubernetes.io/metadata.name": "pepr-system", "zarf-helm-release": "zarf-a102b532d6a523b085622665b606574b0cd82025"}, "name": "pepr-system", "resourceVersion": "1128", "uid": "6ff15b92-117f-4226-97aa-c334ce7a6d5d"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"creationTimestamp": "2024-04-22T14:04:21Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "kubernetes.io/metadata.name": "istio-admin-gateway"}, "name": "istio-admin-gateway", "resourceVersion": "1142", "uid": "d66870cc-5b8e-4611-aeae-6f314f1e8076"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"creationTimestamp": "2024-04-22T14:04:27Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "kubernetes.io/metadata.name": "istio-tenant-gateway"}, "name": "istio-tenant-gateway", "resourceVersion": "1200", "uid": "2e51a476-fe56-4aaf-a342-27efe1fe4ce0"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"creationTimestamp": "2024-04-22T14:04:31Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "kubernetes.io/metadata.name": "istio-passthrough-gateway"}, "name": "istio-passthrough-gateway", "resourceVersion": "1258", "uid": "895505ba-3dd7-49db-8b6a-76a16220526d"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"annotations": {"uds.dev/original-istio-injection": "non-existent", "uds.dev/pkg-metrics-server": "true"}, "creationTimestamp": "2024-04-22T14:04:54Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "istio-injection": "enabled", "kubernetes.io/metadata.name": "metrics-server"}, "name": "metrics-server", "resourceVersion": "1516", "uid": "b98e6771-1752-46f9-9de8-dbab84ef3803"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"annotations": {"uds.dev/original-istio-injection": "non-existent", "uds.dev/pkg-keycloak": "true"}, "creationTimestamp": "2024-04-22T14:05:21Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "istio-injection": "enabled", "kubernetes.io/metadata.name": "keycloak"}, "name": "keycloak", "resourceVersion": "1698", "uid": "6560188f-4555-475b-aa5b-b3b0173d6e32"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"creationTimestamp": "2024-04-22T14:06:37Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "kubernetes.io/metadata.name": "uds-policy-exemptions"}, "name": "uds-policy-exemptions", "resourceVersion": "1903", "uid": "f56bf67e-4c62-4b15-80c2-82639586bd35"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"annotations": {"uds.dev/original-istio-injection": "non-existent", "uds.dev/pkg-neuvector": "true"}, "creationTimestamp": "2024-04-22T14:06:35Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "istio-injection": "enabled", "kubernetes.io/metadata.name": "neuvector"}, "name": "neuvector", "resourceVersion": "1940", "uid": "b69f3003-abc4-4495-b3bb-79a3e12ab05f"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"annotations": {"uds.dev/original-istio-injection": "non-existent", "uds.dev/pkg-loki": "true"}, "creationTimestamp": "2024-04-22T14:07:07Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "istio-injection": "enabled", "kubernetes.io/metadata.name": "loki"}, "name": "loki", "resourceVersion": "2421", "uid": "e92842e6-2242-44d5-9473-83a97fdf0c3d"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"annotations": {"uds.dev/original-istio-injection": "non-existent", "uds.dev/pkg-prometheus-stack": "true"}, "creationTimestamp": "2024-04-22T14:08:40Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "istio-injection": "enabled", "kubernetes.io/metadata.name": "monitoring"}, "name": "monitoring", "resourceVersion": "2915", "uid": "ebb401c8-13ee-4636-8e36-b04a0a0e3cfb"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"annotations": {"uds.dev/original-istio-injection": "non-existent", "uds.dev/pkg-promtail": "true"}, "creationTimestamp": "2024-04-22T14:09:19Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "istio-injection": "enabled", "kubernetes.io/metadata.name": "promtail"}, "name": "promtail", "resourceVersion": "3466", "uid": "9ee64052-8c6c-4d4e-82fb-d5fe016f441e"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"annotations": {"uds.dev/original-istio-injection": "non-existent", "uds.dev/pkg-grafana": "true"}, "creationTimestamp": "2024-04-22T14:07:07Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "istio-injection": "enabled", "kubernetes.io/metadata.name": "grafana"}, "name": "grafana", "resourceVersion": "3621", "uid": "c8064cce-25f4-4fd4-a16c-9e5a4af869b5"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"annotations": {"uds.dev/original-istio-injection": "non-existent", "uds.dev/pkg-authservice": "true"}, "creationTimestamp": "2024-04-22T14:10:05Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "istio-injection": "enabled", "kubernetes.io/metadata.name": "authservice"}, "name": "authservice", "resourceVersion": "3775", "uid": "2c399f0d-f380-43b5-a717-edf4f19d2b17"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}, {"apiVersion": "v1", "kind": "Namespace", "metadata": {"annotations": {"uds.dev/original-istio-injection": "non-existent", "uds.dev/pkg-velero": "true"}, "creationTimestamp": "2024-04-22T14:10:17Z", "labels": {"app.kubernetes.io/managed-by": "zarf", "istio-injection": "enabled", "kubernetes.io/metadata.name": "velero"}, "name": "velero", "resourceVersion": "3857", "uid": "e6a181f4-02b4-4625-8d9e-78656316fc35"}, "spec": {"finalizers": ["kubernetes"]}, "status": {"phase": "Active"}}]}