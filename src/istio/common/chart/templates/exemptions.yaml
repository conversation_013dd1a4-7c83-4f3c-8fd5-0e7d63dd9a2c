# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial
apiVersion: uds.dev/v1alpha1
kind: Exemption
metadata:
 name: istio
 namespace: uds-policy-exemptions
spec:
 exemptions:
   - policies:
       - RequireNonRootUser # CNI plugin requires root
       - RestrictVolumeTypes # CNI plugin uses 'hostPath' volume type
       - DisallowHostNamespaces # CNI plugin requires host namespace access for networking
       - RestrictCapabilities # CNI plugin requires NET_ADMIN, NET_RAW, SYS_PTRACE, SYS_ADMIN, and DAC_OVERRIDE capabilities
       - RestrictHostPathWrite # CNI plugin requires access to write to CNI
       - RestrictHostPorts # CNI plugin requires access to host ports
       - RestrictSELinuxType # CNI plugin requires spc_t SELinux type when running in SELinux-enforcing environments
     matcher:
       namespace: istio-system
       kind: pod
       name: "istio-cni-node.*"
     title: "Istio CNI exemptions"
     description: "Exemptions necessary for Istio CNI to manage network configurations and ensure proper traffic routing"
   - policies:
       - DisallowPrivileged # ztunnel runs with allowPrivilegeEscalation
       - RequireNonRootUser # ztunnel requires root
       - RestrictVolumeTypes # ztunnel uses 'hostPath' volume type
       - RestrictCapabilities # ztunnel requires NET_ADMIN, NET_RAW, and SYS_ADMIN capabilities
       - RestrictHostPathWrite # ztunnel requires access to write to CNI
       - RestrictSELinuxType # ztunnel requires spc_t SELinux type when running in SELinux-enforcing environments
     matcher:
       namespace: istio-system
       kind: pod
       name: "ztunnel.*"
     title: "Istio ztunnel exemptions"
     description: "Exemptions necessary for ztunnel to manage network traffic and ensure secure data plane operations"
