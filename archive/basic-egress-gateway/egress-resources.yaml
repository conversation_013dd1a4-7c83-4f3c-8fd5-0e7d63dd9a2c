# Add the gateway, service entry, destination rule, and virtual service
apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: basic-egress-gateway
  namespace: basic-egress-test
spec:
  selector:
    app: egressgateway
  servers:
  - port:
      number: 443
      name: tls
      protocol: TLS
    hosts:
      - httpbin.org
    tls:
      mode: PASSTHROUGH
---
apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: basic-egress-service-entry
  namespace: basic-egress-test
spec:
  location: MESH_EXTERNAL
  hosts:
  - httpbin.org
  ports: 
  - number: 443
    name: tls
    protocol: TLS
  resolution: DNS
---
apiVersion: networking.istio.io/v1
kind: DestinationRule
metadata:
  name: egressgateway-for-basic-egress
  namespace: basic-egress-test
spec:
  host: egressgateway.istio-egress-gateway.svc.cluster.local
  subsets:
    - name: basic-egress
---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: basic-egress-through-egress-gateway
  namespace: basic-egress-test
spec:
  hosts:
  - httpbin.org
  gateways:
  - basic-egress-gateway
  - mesh
  tls:
  - match:
    - gateways:
      - mesh
      port: 443
      sniHosts:
      - httpbin.org
    route:
    - destination:
        host: egressgateway.istio-egress-gateway.svc.cluster.local
        subset: basic-egress
        port:
          number: 443
  - match:
    - gateways:
      - basic-egress-gateway
      port: 443
      sniHosts:
      - httpbin.org
    route:
    - destination:
        host: httpbin.org
        port:
          number: 443
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-egress-gateway-access
  namespace: basic-egress-test
spec:
  podSelector: 
    matchLabels:
      app: curl1
  policyTypes:
  - Egress
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: istio-egress-gateway
      podSelector:
        matchLabels:
          app: egressgateway
    ports:
    - protocol: TCP
      port: 443