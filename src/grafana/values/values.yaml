# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

sidecar:
  dashboards:
    enabled: true
    label: grafana_dashboard
    searchNamespace: ALL
  datasources:
    enabled: true
    label: grafana_datasource

extraSecretMounts:
  - name: auth-generic-oauth-secret-mount
    secretName: sso-client-uds-core-admin-grafana
    defaultMode: 0440
    mountPath: /etc/secrets/auth_generic_oauth
    readOnly: true

grafana.ini:
  server:
    root_url: https://grafana.{{ "###ZARF_VAR_ADMIN_DOMAIN###" | default "admin.###ZARF_VAR_DOMAIN###" }}
  # Disable telemetry that doesn't function in the airgap
  analytics:
    reporting_enabled: false
    check_for_updates: false
    check_for_plugin_updates: false
    feedback_links_enabled: false
  plugins:
    public_key_retrieval_disabled: true
    preinstall_disabled: true # Ensure we don't try to install plugins from grafana.com which would fail in the airgap
  auth:
    # Disable the login form to force users to use SSO
    disable_login_form: true
  auth.generic_oauth:
    # https://grafana.com/docs/grafana/latest/setup-grafana/configure-security/configure-authentication/keycloak/
    enabled: true
    client_id: $__file{/etc/secrets/auth_generic_oauth/clientId}
    client_secret: $__file{/etc/secrets/auth_generic_oauth/secret}
    scopes: openid profile
    email_attribute_path: email
    login_attribute_path: preferred_username
    name_attribute_path: name
    name: UDS Identity Service
    auth_url: https://sso.###ZARF_VAR_DOMAIN###/realms/uds/protocol/openid-connect/auth
    # Note: this has to be the external URL to ensure that the token issuer checks in Grafana line up with this URL
    token_url: https://sso.###ZARF_VAR_DOMAIN###/realms/uds/protocol/openid-connect/token
    signout_redirect_url: https://sso.###ZARF_VAR_DOMAIN###/realms/uds/protocol/openid-connect/logout?post_logout_redirect_uri=https%3A%2F%2Fgrafana.admin.###ZARF_VAR_DOMAIN###%2Flogin
    use_refresh_token: true
    allow_sign_up: true
    # Require a UDS Core group to access Grafana
    role_attribute_path: "contains(groups[], '/UDS Core/Admin') && 'Admin' || contains(groups[], '/UDS Core/Auditor') && 'Viewer' || 'Unauthorized'"
    role_attribute_strict: true
    # Automatically redirect to the SSO login page
    auto_login: true

# Add environment variables for PostgresQL connection from uds-config chart
envFromSecret: "uds-grafana-config-postgresql"

service:
  appProtocol: "http"

autoscaling:
  # Enable HorizontalPodAutoscaler for Grafana
  enabled: false
  # Additional labels for the HorizontalPodAutoscaler
  labels: {}
  # Minimum and maximum number of replicas for the Grafana deployment
  minReplicas: 2
  maxReplicas: 5
  # Metrics to use for scaling (CPU, memory, or custom metrics)
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70 # Adjust based on expected load
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 75 # Optional, based on memory usage patterns
  # Scaling behavior (optional): Controls how fast to scale up or down
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300 # Prevent rapid scaling down
      policies:
        - type: Pods
          value: 1
          periodSeconds: 300 # Scale down one pod every 5 minutes
