## UDS Pepr Module

This module consolidates all the UDS Core Pepr related functionality:

- [istio](istio/README.md) - Custom Istio behavior to support Job termination
- [operator](operator/README.md) - The UDS Operator manages the lifecycle of UDS Package CRs and their corresponding resources
- [policies](policies/README.md) - A policy engine (like Kyverno or OPA) to enforce security best practices
