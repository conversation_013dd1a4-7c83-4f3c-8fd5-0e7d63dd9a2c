apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-1
  namespace: test-1
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 443
        remoteHost: example.com
        remoteProtocol: TLS
        description: "Example Curl"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-2
  namespace: test-2
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 443
        remoteHost: example.com
        remoteProtocol: TLS
        description: "Example Curl"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-3
  namespace: test-3
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 80
        remoteHost: example.com
        remoteProtocol: HTTP
        description: "Example Curl"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-4
  namespace: test-4
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 80
        remoteHost: example.com
        remoteProtocol: HTTP
        description: "Example Curl"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-5
  namespace: test-5
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 80
        remoteHost: example.com
        remoteProtocol: HTTP
        description: "Example Curl"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-6
  namespace: test-6
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 80
        remoteHost: example.com
        remoteProtocol: HTTP
        description: "Example Curl"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-7
  namespace: test-7
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 1234
        remoteHost: google.com
        remoteProtocol: HTTP
        description: "Example Curl"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-8
  namespace: test-8
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 443
        remoteHost: google.com
        remoteProtocol: TLS
        description: "Example Curl"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-9
  namespace: test-9
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 443
        remoteHost: example.com
        remoteProtocol: TLS
        description: "Example Curl"
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-10
  namespace: test-10
spec:
  network:
    allow:
      - direction: Egress
        selector:
          app: curl
        ports:
          - 443
        remoteHost: example.com
        remoteProtocol: TLS
        description: "Example Curl"