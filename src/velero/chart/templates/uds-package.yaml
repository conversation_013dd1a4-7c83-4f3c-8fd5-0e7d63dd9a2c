# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: velero
  namespace: {{ .Release.Namespace }}
spec:
  network:
    serviceMesh:
      mode: ambient
    allow:
      # Egress for S3 connections
      - direction: Egress
        selector:
          app.kubernetes.io/name: velero
        description: Storage
        {{- if .Values.storage.internal.enabled }}
        remoteSelector:
          {{- .Values.storage.internal.remoteSelector | toYaml | nindent 10 }}
        remoteNamespace: {{ .Values.storage.internal.remoteNamespace }}
        {{- else if .Values.storage.egressCidr }}
        remoteCidr: {{ .Values.storage.egressCidr }}
        {{- else }}
        remoteGenerated: Anywhere
        {{- end }}

      - direction: Egress
        selector:
          batch.kubernetes.io/job-name: "velero-upgrade-crds"
        remoteGenerated: KubeAPI

      - direction: Ingress
        remoteNamespace: monitoring
        remoteSelector:
          app: prometheus
        remoteServiceAccount: kube-prometheus-stack-prometheus
        selector:
          app.kubernetes.io/name: velero
        port: 8085
        description: "Prometheus Metrics"
