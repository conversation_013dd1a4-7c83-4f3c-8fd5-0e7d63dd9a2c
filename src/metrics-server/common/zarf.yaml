# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: uds-core-metrics-server-common
  description: "UDS Metrics Server Common"
  url: https://github.com/kubernetes-sigs/metrics-server

components:
  - name: metrics-server
    required: false # This component is optional since most k8s distros provide this out of the box
    charts:
      - name: uds-metrics-server-config
        namespace: metrics-server
        version: 0.1.0
        localPath: ../chart
      - name: metrics-server
        namespace: metrics-server
        url: https://kubernetes-sigs.github.io/metrics-server
        version: 3.12.2
        valuesFiles:
          - "../values/values.yaml"
