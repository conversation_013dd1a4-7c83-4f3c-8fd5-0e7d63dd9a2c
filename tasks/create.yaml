# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

includes:
  - common: https://raw.githubusercontent.com/defenseunicorns/uds-common/v1.16.3/tasks/create.yaml

variables:
  - name: FLAVOR
    default: upstream

  - name: REGISTRY1_PEPR_IMAGE
    # renovate: datasource=docker depName=registry1.dso.mil/ironbank/opensource/defenseunicorns/pepr/controller versioning=semver
    default: registry1.dso.mil/ironbank/opensource/defenseunicorns/pepr/controller:v0.51.5

  - name: UNICORN_PEPR_IMAGE
    # renovate: datasource=github-tags depName=defenseunicorns/pepr versioning=semver
    default: ghcr.io/defenseunicorns/pepr/private/controller:v0.51.5

  - name: LAYER

  - name: CREATE_OPTIONS
    description: "Additional options passed in when creating Zarf packages. For example: --skip-sbom"
    default: ""

tasks:
  - name: standard-package
    description: "Create the UDS Core Zarf Package"
    inputs:
      create_options:
        default: ${CREATE_OPTIONS}
        description: "Additional options passed in when creating Zarf packages. For example: --skip-sbom"
    actions:
      - task: single-layer
        with:
          layer: standard
          create_options: ${{ .inputs.create_options }}

  - name: k3d-standard-bundle
    description: "Create the K3d-UDS Core Bundle"
    actions:
      - description: "Create the UDS Core Standard Bundle"
        cmd: "uds create bundles/k3d-standard --confirm --no-progress --architecture=${ZARF_ARCHITECTURE}"

  - name: k3d-slim-dev-bundle
    description: "Create the slim dev bundle (Base and Identity)"
    actions:
      - description: "Create base package"
        task: single-layer
        with:
          layer: base

      - description: "Create identity-authorization package"
        task: single-layer
        with:
          layer: identity-authorization

      - description: "Create the slim dev bundle (Base and Identity)"
        cmd: "uds create bundles/k3d-slim-dev --confirm --no-progress --architecture=${ZARF_ARCHITECTURE}"

  - name: checkpoint-dev-package
    description: "Create the K3d + UDS Core Checkpoint Zarf Package"
    actions:
      - description: "Create build output directory"
        cmd: "mkdir -p build"
      - description: "Create the UDS Core Checkpoint Zarf Package"
        cmd: "uds zarf package create packages/checkpoint-dev --confirm --no-progress --skip-sbom"

  # This task is a wrapper to support --set LAYER=identity-authorization
  - name: single-layer-callable
    actions:
      - task: single-layer
        with:
          layer: $LAYER

  - name: single-layer
    inputs:
      layer:
        default: base
        description: The UDS Core layer to build
      create_options:
        default: ${CREATE_OPTIONS}
        description: "Additional options passed in when creating Zarf packages. For example: --skip-sbom"
    actions:
      # Note: Maru does not support `or` for this conditional task so we just duplicate it for each case
      - task: pepr-build
        if: ${{ eq .inputs.layer "standard" }}
      - task: pepr-build
        if: ${{ eq .inputs.layer "base" }}
      - description: "Create build output directory"
        cmd: "mkdir -p build"
      - description: "Create the UDS Core Zarf Package"
        task: common:package
        with:
          path: packages/${{ index .inputs "layer" }}
          config: ./zarf-config.yaml
          options: ${{ .inputs.create_options }}
          architecture: ${ZARF_ARCHITECTURE}

  - name: pepr-build
    description: "Build the UDS Core Pepr Module"
    actions:
      - description: "Build the UDS Core Pepr Module"
        cmd: |
          if [ -n "${PEPR_CUSTOM_IMAGE}" ] ; then
            # e.g. PEPR_CUSTOM_IMAGE="pepr:dev uds run slim-dev"
            PEPR_OVERRIDE_IMAGE="--custom-image ${PEPR_CUSTOM_IMAGE}"
          elif [ "${FLAVOR}" = "registry1" ] ; then
            PEPR_OVERRIDE_IMAGE="--custom-image ${REGISTRY1_PEPR_IMAGE}"
          elif [ "${FLAVOR}" = "unicorn" ] ; then
            PEPR_OVERRIDE_IMAGE="--custom-image ${UNICORN_PEPR_IMAGE}"
          else
            PEPR_OVERRIDE_IMAGE=""
          fi
          rm -fr dist
          npm ci
          npx pepr build -z chart $PEPR_OVERRIDE_IMAGE
