# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: Test packages

on:
  # Manual trigger
  workflow_dispatch:
    inputs:
      package:
        type: string
        description: "The name of the source package to test"
        required: true
      flavor:
        type: string
        description: "Flavor of the source package to test"
        required: true
      test_type:
        type: string
        description: "The type of test to perform"
        required: true
    # Triggered by pull-request-conditionals.yaml
  workflow_call:
    inputs:
      package:
        type: string
        description: "The name of the core package layer to test"
        required: true
      flavor:
        type: string
        description: "Flavor of the source package to test"
        required: true
      test_type:
        type: string
        description: "The type of test to perform"
        required: true

permissions:
  contents: read
  id-token: write # This is needed for OIDC federation.
  packages: read # Allows reading the published GHCR packages

jobs:
  test:
    # Use the 8 core runner for full-core or 4 core runner (with larger disk) for func layers tests
    runs-on: "${{ inputs.package == 'all' && 'uds-ubuntu-big-boy-8-core' || 'uds-ubuntu-big-boy-4-core'}}"
    # Increase the timeout for longer upgrades - temporarily increased to 60 for longer upgrade from cgr -> rf
    timeout-minutes: ${{ inputs.test_type == 'upgrade' && 60 || 30 }}
    name: Test
    env:
      UDS_LAYER: ${{ inputs.package }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Environment setup
        uses: ./.github/actions/setup
        with:
          registry1Username: ${{ secrets.IRON_BANK_ROBOT_USERNAME }}
          registry1Password: ${{ secrets.IRON_BANK_ROBOT_PASSWORD }}
          ghToken: ${{ secrets.GITHUB_TOKEN }}
          rapidfortUsername: ${{ secrets.RAPIDFORT_USERNAME }}
          rapidfortPassword: ${{ secrets.RAPIDFORT_PASSWORD }}

      - name: Test a single layer package
        if: ${{ inputs.package != 'all' && inputs.test_type == 'install' }}
        run: uds run test-single-layer --set FLAVOR=${{ inputs.flavor }} --set LAYER=${{ inputs.package }} --no-progress

      - name: Test UDS Core Install
        if: ${{ inputs.package == 'all' && inputs.test_type == 'install' }}
        run: uds run test:uds-core-e2e --set FLAVOR=${{ inputs.flavor }} --no-progress

      # - name: Compose UDS Core Component Definitions
      #   if: ${{ inputs.package == 'all' && inputs.test_type == 'install' }}
      #   run: uds run test-compliance-compose --no-progress

      # - name: Validate UDS Core Compliance
      #   if: ${{ inputs.package == 'all' && inputs.test_type == 'install' }}
      #   run: uds run test-compliance-validate --no-progress

      # - name: Upload Assessment
      #   if: ${{ inputs.package == 'all' && inputs.test_type == 'install' }}
      #   uses: actions/upload-artifact@4cec3d8aa04e39d1a68397de0c4cd6fb9dce8ec1 # v4.6.1
      #   with:
      #     name: ${{ inputs.flavor }}-assessment-results
      #     path: ./compliance/oscal-assessment-results.yaml

      - name: Test UDS Core Upgrade
        if: ${{ inputs.package == 'all' && inputs.test_type == 'upgrade' }}
        run: uds run test-uds-core-upgrade --set FLAVOR=${{ inputs.flavor }} --no-progress

      - name: Debug Output
        if: ${{ always() }}
        uses: ./.github/actions/debug-output

      - name: Save logs
        if: always()
        uses: ./.github/actions/save-logs
        with:
          suffix: -${{ inputs.test_type }}-${{ inputs.package }}-${{ inputs.flavor }}
