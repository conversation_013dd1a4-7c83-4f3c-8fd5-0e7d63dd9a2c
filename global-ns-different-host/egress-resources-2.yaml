# Global gateway, destination rule, virtual service
apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: httpbin-org-443-tls-gateway
  namespace: istio-egress-gateway
  annotations:
    uds-core.pepr.dev/egress: '{"pkgs":["pkg-2"], "route": {"host": "httpbin.org", "port": 443, "protcol": "tls"}}' 
spec:
  selector:
    app: egressgateway
  servers:
  - port:
      number: 443
      name: tls
      protocol: TLS
    hosts:
      - httpbin.org
    tls:
      mode: PASSTHROUGH
---
apiVersion: networking.istio.io/v1
kind: DestinationRule
metadata:
  name: egressgateway-for-httpbin-org-443-tls
  namespace: istio-egress-gateway
  annotations:
    uds-core.pepr.dev/egress: '{"pkgs":["pkg-2"], "route": {"host": "httpbin.org", "port": 443, "protcol": "tls"}}' 
spec:
  host: egressgateway.istio-egress-gateway.svc.cluster.local
  subsets:
    - name: egress-to-httpbin-org-443-tls
---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: through-egress-gateway-2
  namespace: istio-egress-gateway
  annotations:
    uds-core.pepr.dev/egress: '{"pkgs":["pkg-2"], "route": {"host": "httpbin.org", "port": 443, "protcol": "tls"}}' 
spec:
  hosts:
  - httpbin.org
  gateways:
  - httpbin-org-443-tls-gateway
  - mesh
  tls:
  - match:
    - gateways:
      - mesh
      port: 443
      sniHosts:
      - httpbin.org
    route:
    - destination:
        host: egressgateway.istio-egress-gateway.svc.cluster.local
        subset: egress-to-httpbin-org-443-tls
        port:
          number: 443
  - match:
    - gateways:
      - httpbin-org-443-tls-gateway
      port: 443
      sniHosts:
      - httpbin.org
    route:
    - destination:
        host: httpbin.org
        port:
          number: 443
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-egress-gateway-access-2
  namespace: ns-2
spec:
  podSelector: 
    matchLabels:
      app: curl2
  policyTypes:
  - Egress
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: istio-egress-gateway
      podSelector:
        matchLabels:
          app: egressgateway
    ports:
    - protocol: TCP
      port: 443
---
apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: service-entry-2
  namespace: ns-2
spec:
  location: MESH_EXTERNAL
  hosts:
  - httpbin.org
  ports: 
  - number: 443
    name: tls
    protocol: TLS
  resolution: DNS
  exportTo:
    - "."
    - "istio-egress-gateway"