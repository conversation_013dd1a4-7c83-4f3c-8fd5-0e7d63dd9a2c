# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

# -- When setting this above 1, a redis configuration is required.
replicaCount: 1

image:
  repository: ghcr.io/istio-ecosystem/authservice/authservice
  pullPolicy: IfNotPresent
  # -- Overrides the image tag whose default is the chart appVersion.
  tag: ""

nameOverride: "authservice"

redis:
  uri: "###ZARF_VAR_AUTHSERVICE_REDIS_URI###"
  egressCidr: ""
  internal:
    enabled: false
    remoteSelector: {}
    remoteNamespace: ""

podAnnotations: {}

podSecurityContext: {}

securityContext: {}

resources: {}

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}
