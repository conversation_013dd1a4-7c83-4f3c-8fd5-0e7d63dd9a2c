# Test apps with various network allow configurations for egress testing

# # Package 1
# apiVersion: v1
# kind: Namespace
# metadata:
#   name: test-app-1
# ---
# apiVersion: uds.dev/v1alpha1
# kind: Package
# metadata:
#   name: test-app-1
#   namespace: test-app-1
# spec:
#   network:
#     allow:
#       - direction: Egress
#         remoteHost: "api.github.com"
#         port: 443

# ---

# # Package 2
# apiVersion: v1
# kind: Namespace
# metadata:
#   name: test-app-2
# ---
# apiVersion: uds.dev/v1alpha1
# kind: Package
# metadata:
#   name: test-app-2
#   namespace: test-app-2
# spec:
#   network:
#     allow:
#       - direction: Egress
#         remoteHost: "registry.npmjs.org"
#         port: 443

# ---

# # Package 3
# apiVersion: v1
# kind: Namespace
# metadata:
#   name: test-app-3
# ---
# apiVersion: uds.dev/v1alpha1
# kind: Package
# metadata:
#   name: test-app-3
#   namespace: test-app-3
# spec:
#   network:
#     allow:
#       - direction: Egress
#         remoteHost: "api.github.com"
#         remoteProtocol: "TLS"
#         port: 443

# ---

# # Package 4
# apiVersion: v1
# kind: Namespace
# metadata:
#   name: test-app-4
# ---
# apiVersion: uds.dev/v1alpha1
# kind: Package
# metadata:
#   name: test-app-4
#   namespace: test-app-4
# spec:
#   network:
#     allow:
#       - direction: Egress
#         remoteHost: "registry.npmjs.org"
#         remoteProtocol: "TLS"
#         port: 443

# ---

# # Package 5
# apiVersion: v1
# kind: Namespace
# metadata:
#   name: test-app-5
# ---
# apiVersion: uds.dev/v1alpha1
# kind: Package
# metadata:
#   name: test-app-5
#   namespace: test-app-5
# spec:
#   network:
#     allow:
#       - direction: Egress
#         remoteHost: "api.github.com"
#         port: 80
#         description: "github-http"

#       - direction: Egress
#         remoteHost: "registry.npmjs.org"
#         port: 443
#         description: "npm-registry"

# ---

# # Package 6
# apiVersion: v1
# kind: Namespace
# metadata:
#   name: test-app-6
# ---
# apiVersion: uds.dev/v1alpha1
# kind: Package
# metadata:
#   name: test-app-6
#   namespace: test-app-6
# spec:
#   network:
#     allow:
#       - direction: Egress
#         remoteHost: "api.github.com"
#         remoteProtocol: "HTTP"
#         port: 80
#         description: "github-http-proto"

#       - direction: Egress
#         remoteHost: "registry.npmjs.org"
#         remoteProtocol: "TLS"
#         port: 443
#         description: "npm-registry-tls"

# ---

# # Package 7
# apiVersion: v1
# kind: Namespace
# metadata:
#   name: test-app-7
# ---
# apiVersion: uds.dev/v1alpha1
# kind: Package
# metadata:
#   name: test-app-7
#   namespace: test-app-7
# spec:
#   network:
#     allow:
#       - direction: Egress
#         remoteHost: "pypi.org"
#         port: 443

# ---

# # Package 8
# apiVersion: v1
# kind: Namespace
# metadata:
#   name: test-app-8
# ---
# apiVersion: uds.dev/v1alpha1
# kind: Package
# metadata:
#   name: test-app-8
#   namespace: test-app-8
# spec:
#   network:
#     allow:
#       - direction: Egress
#         remoteHost: "files.pythonhosted.org"
#         remoteProtocol: "TLS"
#         port: 443

# ---

# # Package 9
# apiVersion: v1
# kind: Namespace
# metadata:
#   name: test-app-9
# ---
# apiVersion: uds.dev/v1alpha1
# kind: Package
# metadata:
#   name: test-app-9
#   namespace: test-app-9
# spec:
#   network:
#     allow:
#       - direction: Egress
#         remoteHost: "api.github.com"
#         port: 22

# ---

# # Package 10
# apiVersion: v1
# kind: Namespace
# metadata:
#   name: test-app-10
# ---
# apiVersion: uds.dev/v1alpha1
# kind: Package
# metadata:
#   name: test-app-10
#   namespace: test-app-10
# spec:
#   network:
#     allow:
#       - direction: Egress
#         remoteHost: "registry.npmjs.org"
#         port: 53
# ---

# Package 11
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-11
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-11
  namespace: test-app-11
spec:
  network:
    allow:
      - direction: Egress
        remoteHost: "docker.io"
        port: 443

---

# Package 12
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-12
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-12
  namespace: test-app-12
spec:
  network:
    allow:
      - direction: Egress
        remoteHost: "quay.io"
        remoteProtocol: "TLS"
        port: 443

---

# Package 13
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-13
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-13
  namespace: test-app-13
spec:
  network:
    allow:
      - direction: Egress
        remoteHost: "docker.io"
        port: 80
        description: "docker-http"

      - direction: Egress
        remoteHost: "quay.io"
        port: 443
        description: "quay-https"

---

# Package 14
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-14
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-14
  namespace: test-app-14
spec:
  network:
    allow:
      - direction: Egress
        remoteHost: "docker.io"
        remoteProtocol: "HTTP"
        port: 80
        description: "docker-http-proto"

      - direction: Egress
        remoteHost: "quay.io"
        remoteProtocol: "TLS"
        port: 443
        description: "quay-tls-proto"

---

# Package 15
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-15
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-15
  namespace: test-app-15
spec:
  network:
    allow:
      - direction: Egress
        remoteHost: "maven.org"
        port: 443

---

# Package 16
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-16
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-16
  namespace: test-app-16
spec:
  network:
    allow:
      - direction: Egress
        remoteHost: "repo1.maven.org"
        remoteProtocol: "TLS"
        port: 443

---

# Package 17
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-17
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-17
  namespace: test-app-17
spec:
  network:
    allow:
      - direction: Egress
        remoteHost: "rubygems.org"
        port: 443

---

# Package 18
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-18
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-18
  namespace: test-app-18
spec:
  network:
    allow:
      - direction: Egress
        remoteHost: "api.rubygems.org"
        remoteProtocol: "TLS"
        port: 443

---

# Package 19
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-19
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-19
  namespace: test-app-19
spec:
  network:
    allow:
      - direction: Egress
        remoteHost: "api.github.com"
        port: 443
        description: "github-api"

      - direction: Egress
        remoteHost: "registry.npmjs.org"
        port: 443
        description: "npm-registry-pkg19"

      - direction: Egress
        remoteHost: "pypi.org"
        port: 443
        description: "pypi-registry"

---

# Package 20
apiVersion: v1
kind: Namespace
metadata:
  name: test-app-20
---
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: test-app-20
  namespace: test-app-20
spec:
  network:
    allow:
      - direction: Egress
        remoteHost: "api.github.com"
        remoteProtocol: "TLS"
        port: 443
        description: "github-api-tls"

      - direction: Egress
        remoteHost: "registry.npmjs.org"
        remoteProtocol: "TLS"
        port: 443
        description: "npm-registry-tls-pkg20"

      - direction: Egress
        remoteHost: "pypi.org"
        remoteProtocol: "TLS"
        port: 443
        description: "pypi-registry-tls"