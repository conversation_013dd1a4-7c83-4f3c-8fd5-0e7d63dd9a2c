#  to patch Virtual Service route for egress-vs-example-com: {"data":{"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"VirtualService.networking.istio.io \"egress-vs-example-com\" is invalid: [spec.http[2]: Invalid value: \"array\": spec.http[2] in body must be of type object: \"array\", <nil>: Invalid value: \"null\": some validation rules were not checked because the object was invalid; correct the existing errors to complete validation]","reason":"Invalid","details":{"name":"egress-vs-example-com","group":"networking.istio.io","kind":"VirtualService","causes":[{"reason":"FieldValueTypeInvalid","message":"Invalid value: \"array\": spec.http[2] in body must be of type object: \"array\"","field":"spec.http[2]"},{"reason":"FieldValueInvalid","message":"Invalid value: \"null\": some validation rules were not checked because the object was invalid; correct the existing errors to complete validation","field":"<nil>"}]},"code":422},"ok":false,"status":422,"statusText":"Unprocessable Entity"}. Attempt 1 of 3.


# Try to create some virtual services.... can http and tls be defined together?