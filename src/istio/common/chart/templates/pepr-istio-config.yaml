# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

# Have to pre-create the namespace and also patch it with the istio-injection label later because
# Helm is kind of dumb: https://github.com/helm/helm/issues/350
kind: Namespace
apiVersion: v1
metadata:
  name: pepr-system
  labels:
    istio.io/dataplane-mode: ambient
---
apiVersion: "security.istio.io/v1beta1"
kind: PeerAuthentication
metadata:
  name: permissive-pepr-webhook
  namespace: pepr-system
spec:
  selector:
    matchLabels:
      pepr.dev/controller: admission
  mtls:
    mode: STRICT
  portLevelMtls:
    "3000":
      mode: PERMISSIVE
---
apiVersion: "security.istio.io/v1beta1"
kind: PeerAuthentication
metadata:
  name: permissive-pepr-webhook-watcher
  namespace: pepr-system
spec:
  selector:
    matchLabels:
      pepr.dev/controller: watcher
  mtls:
    mode: STRICT
  portLevelMtls:
    "3000":
      mode: PERMISSIVE
