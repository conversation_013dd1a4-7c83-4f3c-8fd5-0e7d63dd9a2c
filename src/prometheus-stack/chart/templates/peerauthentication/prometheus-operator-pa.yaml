# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

{{- if .Capabilities.APIVersions.Has "security.istio.io/v1beta1" }}
apiVersion: "security.istio.io/v1beta1"
kind: PeerAuthentication
metadata:
  name: prometheus-operator-webhook
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: kube-prometheus-stack-operator
  mtls:
    mode: STRICT
  portLevelMtls:
    # Allow webhooks to operate permissive since ingress originates from the nodes
    "10250":
      mode: PERMISSIVE
{{- end }}
