{"adminGateway": {"apiVersion": "networking.istio.io/v1beta1", "kind": "Gateway", "metadata": {"annotations": {"meta.helm.sh/release-name": "uds-istio-config", "meta.helm.sh/release-namespace": "istio-admin-gateway"}, "creationTimestamp": "2024-04-22T14:04:26Z", "generation": 1, "labels": {"app.kubernetes.io/managed-by": "<PERSON><PERSON>"}, "name": "admin-gateway", "namespace": "istio-admin-gateway", "resourceVersion": "1196", "uid": "7dbddd10-c3ca-45d5-9377-f99bfd819a2c"}, "spec": {"selector": {"app": "admin-ingressgateway"}, "servers": [{"hosts": ["*.admin.uds.dev"], "port": {"name": "http-admin", "number": 80, "protocol": "HTTP"}, "tls": {"httpsRedirect": true}}, {"hosts": ["*.admin.uds.dev"], "port": {"name": "https-admin", "number": 443, "protocol": "HTTPS"}, "tls": {"credentialName": "gateway-tls", "minProtocolVersion": "TLSV1_3", "mode": "SIMPLE"}}, {"hosts": ["keycloak.admin.uds.dev"], "port": {"name": "http-keycloak", "number": 80, "protocol": "HTTP"}, "tls": {"httpsRedirect": true}}, {"hosts": ["keycloak.admin.uds.dev"], "port": {"name": "https-keycloak", "number": 443, "protocol": "HTTPS"}, "tls": {"credentialName": "gateway-tls", "minProtocolVersion": "TLSV1_3", "mode": "OPTIONAL_MUTUAL"}}]}}, "virtualServices": [{"apiVersion": "networking.istio.io/v1beta1", "kind": "VirtualService", "metadata": {"creationTimestamp": "2024-04-22T14:05:22Z", "generation": 1, "labels": {"uds/generation": "1", "uds/package": "keycloak"}, "name": "keycloak-tenant-remove-private-paths-from-public-gateway", "namespace": "keycloak", "ownerReferences": [{"apiVersion": "uds.dev/v1alpha1", "kind": "Package", "name": "keycloak", "uid": "9310f8a4-d575-4fe9-bcc5-f00ba5abe9d2"}], "resourceVersion": "1702", "uid": "e4ff9458-0a7c-4723-9c51-b51568a6a277"}, "spec": {"gateways": ["istio-tenant-gateway/tenant-gateway"], "hosts": ["sso.uds.dev"], "http": [{"headers": {"request": {"add": {"istio-mtls-client-certificate": "%DOWNSTREAM_PEER_CERT%"}, "remove": ["istio-mtls-client-certificate"]}}, "match": [{"name": "redirect-welcome", "uri": {"exact": "/"}}, {"name": "redirect-admin", "uri": {"prefix": "/admin"}}, {"name": "redirect-master-realm", "uri": {"prefix": "/realms/master"}}, {"name": "redirect-metrics", "uri": {"prefix": "/metrics"}}], "rewrite": {"uri": "/realms/uds/account"}, "route": [{"destination": {"host": "keycloak-http.keycloak.svc.cluster.local", "port": {"number": 8080}}}]}]}}, {"apiVersion": "networking.istio.io/v1beta1", "kind": "VirtualService", "metadata": {"creationTimestamp": "2024-04-22T14:05:22Z", "generation": 1, "labels": {"uds/generation": "1", "uds/package": "keycloak"}, "name": "keycloak-tenant-public-auth-access-with-optional-client-certificate", "namespace": "keycloak", "ownerReferences": [{"apiVersion": "uds.dev/v1alpha1", "kind": "Package", "name": "keycloak", "uid": "9310f8a4-d575-4fe9-bcc5-f00ba5abe9d2"}], "resourceVersion": "1703", "uid": "8355e39e-79b8-4bb8-b5fc-6d4e5f72c8a4"}, "spec": {"gateways": ["istio-tenant-gateway/tenant-gateway"], "hosts": ["sso.uds.dev"], "http": [{"headers": {"request": {"add": {"istio-mtls-client-certificate": "%DOWNSTREAM_PEER_CERT%"}, "remove": ["istio-mtls-client-certificate"]}}, "route": [{"destination": {"host": "keycloak-http.keycloak.svc.cluster.local", "port": {"number": 8080}}}]}]}}, {"apiVersion": "networking.istio.io/v1beta1", "kind": "VirtualService", "metadata": {"creationTimestamp": "2024-04-22T14:05:22Z", "generation": 1, "labels": {"uds/generation": "1", "uds/package": "keycloak"}, "name": "keycloak-admin-admin-access-with-optional-client-certificate", "namespace": "keycloak", "ownerReferences": [{"apiVersion": "uds.dev/v1alpha1", "kind": "Package", "name": "keycloak", "uid": "9310f8a4-d575-4fe9-bcc5-f00ba5abe9d2"}], "resourceVersion": "1704", "uid": "100da481-a5a8-4a87-82a7-35163d0f7a10"}, "spec": {"gateways": ["istio-admin-gateway/admin-gateway"], "hosts": ["keycloak.admin.uds.dev"], "http": [{"headers": {"request": {"add": {"istio-mtls-client-certificate": "%DOWNSTREAM_PEER_CERT%"}, "remove": ["istio-mtls-client-certificate"]}}, "route": [{"destination": {"host": "keycloak-http.keycloak.svc.cluster.local", "port": {"number": 8080}}}]}]}}, {"apiVersion": "networking.istio.io/v1beta1", "kind": "VirtualService", "metadata": {"creationTimestamp": "2024-04-22T14:05:22Z", "generation": 1, "labels": {"uds/generation": "1", "uds/package": "keycloak"}, "name": "keycloak-tenant-emulate-gitlab-authorize-endpoint", "namespace": "keycloak", "ownerReferences": [{"apiVersion": "uds.dev/v1alpha1", "kind": "Package", "name": "keycloak", "uid": "9310f8a4-d575-4fe9-bcc5-f00ba5abe9d2"}], "resourceVersion": "1706", "uid": "45ad1fa8-b4b2-47f6-8d27-b3711f500ecd"}, "spec": {"gateways": ["istio-tenant-gateway/tenant-gateway"], "hosts": ["sso.uds.dev"], "http": [{"match": [{"name": "gitlab-authorize", "uri": {"prefix": "/oauth/authorize"}}], "rewrite": {"uri": "/realms/uds/protocol/openid-connect/auth"}, "route": [{"destination": {"host": "keycloak-http.keycloak.svc.cluster.local", "port": {"number": 8080}}}]}]}}, {"apiVersion": "networking.istio.io/v1beta1", "kind": "VirtualService", "metadata": {"creationTimestamp": "2024-04-22T14:05:22Z", "generation": 1, "labels": {"uds/generation": "1", "uds/package": "keycloak"}, "name": "keycloak-tenant-emulate-gitlab-user-endpoint", "namespace": "keycloak", "ownerReferences": [{"apiVersion": "uds.dev/v1alpha1", "kind": "Package", "name": "keycloak", "uid": "9310f8a4-d575-4fe9-bcc5-f00ba5abe9d2"}], "resourceVersion": "1710", "uid": "cead5510-3501-4605-bbb0-c2c05dc56702"}, "spec": {"gateways": ["istio-tenant-gateway/tenant-gateway"], "hosts": ["sso.uds.dev"], "http": [{"match": [{"name": "gitlab-user", "uri": {"prefix": "/api/v4/user"}}], "rewrite": {"uri": "/realms/uds/protocol/openid-connect/userinfo"}, "route": [{"destination": {"host": "keycloak-http.keycloak.svc.cluster.local", "port": {"number": 8080}}}]}]}}, {"apiVersion": "networking.istio.io/v1beta1", "kind": "VirtualService", "metadata": {"creationTimestamp": "2024-04-22T14:05:22Z", "generation": 1, "labels": {"uds/generation": "1", "uds/package": "keycloak"}, "name": "keycloak-tenant-emulate-gitlab-token-endpoint", "namespace": "keycloak", "ownerReferences": [{"apiVersion": "uds.dev/v1alpha1", "kind": "Package", "name": "keycloak", "uid": "9310f8a4-d575-4fe9-bcc5-f00ba5abe9d2"}], "resourceVersion": "1711", "uid": "53f44ca4-2a3d-4053-9c25-7ddc3e1213f4"}, "spec": {"gateways": ["istio-tenant-gateway/tenant-gateway"], "hosts": ["sso.uds.dev"], "http": [{"match": [{"name": "gitlab-token", "uri": {"prefix": "/oauth/token"}}], "rewrite": {"uri": "/realms/uds/protocol/openid-connect/token"}, "route": [{"destination": {"host": "keycloak-http.keycloak.svc.cluster.local", "port": {"number": 8080}}}]}]}}, {"apiVersion": "networking.istio.io/v1beta1", "kind": "VirtualService", "metadata": {"creationTimestamp": "2024-04-22T14:06:37Z", "generation": 1, "labels": {"uds/generation": "1", "uds/package": "neuvector"}, "name": "neuvector-admin-neuvector-8443-neuvector-service-webui", "namespace": "neuvector", "ownerReferences": [{"apiVersion": "uds.dev/v1alpha1", "kind": "Package", "name": "neuvector", "uid": "e5021367-f25b-466f-93a1-19a6419ebafa"}], "resourceVersion": "1941", "uid": "b647739b-571a-44e5-a90a-14156f910572"}, "spec": {"gateways": ["istio-admin-gateway/admin-gateway"], "hosts": ["neuvector.admin.uds.dev"], "http": [{"route": [{"destination": {"host": "neuvector-service-webui.neuvector.svc.cluster.local", "port": {"number": 8443}}}]}]}}, {"apiVersion": "networking.istio.io/v1beta1", "kind": "VirtualService", "metadata": {"creationTimestamp": "2024-04-22T14:09:45Z", "generation": 1, "labels": {"uds/generation": "1", "uds/package": "grafana"}, "name": "grafana-admin-grafana-80-grafana", "namespace": "grafana", "ownerReferences": [{"apiVersion": "uds.dev/v1alpha1", "kind": "Package", "name": "grafana", "uid": "5e00957e-acfd-453d-82eb-c4fae4252660"}], "resourceVersion": "3622", "uid": "e1805572-9271-4e48-9e26-a1f84ac78a09"}, "spec": {"gateways": ["istio-admin-gateway/admin-gateway"], "hosts": ["grafana.admin.uds.dev"], "http": [{"route": [{"destination": {"host": "grafana.grafana.svc.cluster.local", "port": {"number": 80}}}]}]}}]}